# frozen_string_literal: true

Deface::Override.new(
  virtual_path: "spree/admin/return_authorizations/edit",
  name: "add_authorize_field",
  insert_before: "[data-hook='buttons']",
  text: "<div data-hook='admin_return_authorizations_authorized'>
              <%= f.field_container :authorized, class: ['form-group'] do %>
                <%= f.check_box :authorized, checked: true %>
                <%= f.label :authorized, Spree.t(:authorized) %>
              <% end %>
            </div>",
)
