# frozen_string_literal: true

Deface::Override.new(
  virtual_path: "spree/admin/return_authorizations/new",
  name: "add_refund_shipping_fee_field",
  insert_before: "[data-hook='buttons']",
  text: "<div data-hook='admin_return_authorizations_refund_shipping_fee'>
              <%= f.field_container :refund_shipping_fee, class: ['form-group'] do %>
                <%= f.check_box :refund_shipping_fee, checked: true %>
                <%= f.label :refund_shipping_fee, Spree.t(:refund_shipping_fee) %>
              <% end %>
            </div>",
)
