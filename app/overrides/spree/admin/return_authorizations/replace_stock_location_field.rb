# frozen_string_literal: true

Deface::Override.new(
  virtual_path: "spree/admin/return_authorizations/_form",
  name: "replace_stock_location_field",
  replace: "erb[loud]:contains('f.select :stock_location_id, Spree::StockLocation.order_default.active.to_a.collect{|l|[l.name, l.id]}')",
  text: <<~ERB,
    <%= f.select :stock_location_id,
      current_store.stock_locations.order_default.active.to_a.collect { |l| [l.name, l.id] },
      { include_blank: true },
      { class: 'select2-clear', 'data-placeholder' => Spree.t(:select_a_stock_location) } %>
  ERB
)
