# frozen_string_literal: true

Deface::Override.new(
  virtual_path: "spree/admin/shared/_main_menu",
  name: "add_organisation_tab",
  insert_before: "erb[silent]:contains(':admin, Spree::OauthApplication')",
  text: <<-HTML,
    <% if (can? :admin, Spree::Organisation) && Apartment::Tenant.current == 'public' %>
      <p class="mb-0 pl-3 my-3" style="font-size:20px; font-weight:600; color:#344055;">Organisation</p>
      <div class="px-4">
        <div class="sub-links">
          <%= tab(:organisations, admin_organisations_path) %>#{" "}
        </div>
        <div class="sub-links">
          <%= tab(:metabase_settings, admin_metabase_settings_path) %>#{" "}
        </div>
        <div class="sub-links">
          <%= tab(:metabase_report_settings, admin_metabase_report_settings_path) %>#{" "}
        </div>
      </div>
    <% elsif (can? :admin, Spree::Organisation) && Apartment::Tenant.current == 'axel' %>
      <p class="mb-0 pl-3 my-3" style="font-size:20px; font-weight:600; color:#344055;">Organisation</p>
      <div class="px-4">
        <div class="sub-links">
          <%= tab(:metabase_settings, admin_metabase_settings_path) %>#{" "}
        </div>
        <div class="sub-links">
          <%= tab(:metabase_report_settings, admin_metabase_report_settings_path) %>#{" "}
        </div>
      </div>
    <% end %>
  HTML
)
