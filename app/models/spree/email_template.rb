# frozen_string_literal: true

module Spree
  class EmailTemplate < ApplicationRecord
    attr_accessor :sale_channel_ids

    belongs_to :store

    enum template_type: { other: 'other', email_campaign: 'email_campaign' }

    scope :for_store, ->(store) { where(store: store) }
    scope :current_store, ->(current_store) { where(store_id: current_store) }
    scope :active, -> { where(active: true) }
    scope :inactive, -> { where(active: false) }
    scope :without_shipping_default, -> { where.not(template_name: Spree.t(:default_shipping_email_name)) }
    scope :default_shipping_email, -> { where(template_name: Spree.t(:default_shipping_email_name)) }
    scope :for_email_campaigns, -> { where(template_type: 'email_campaign') }

    validates :template_name, uniqueness: { scope: :store_id }
    validates :mailer_class, :template_type, presence: true

    has_many :email_template_tags, dependent: :delete_all
    has_many :tags, through: :email_template_tags, class_name: "Spree::Tag"
    has_many :sale_channels, class_name: "Spree::SaleChannel"
    before_update :update_sale_channels
    after_create :create_sale_channels

    before_save :update_variables, if: -> { template_type == 'email_campaign' }

    def render_body(resource, options)
      @resource = resource
      @options = options
      template = ::Liquid::Template.parse(body)
      template_variables = if private_methods.include?(template_name.to_sym)
        send(template_name.to_s)
      elsif reload.tags.include?(Spree::Tag.shipping_tag)
        Rails.logger.info("Resource state third reload and ship: #{resource.inspect}")
        send(:shipped_email)
      end
      Rails.logger.info("Resource state fourth reload and ship: #{template_variables}")
      template.render(template_variables)
    end

    def send_test_email
      if mailer_class == "Spree::UserMailer"
        mailer_class.constantize.send(
          template_name,
          record,
          "randomtoken",
          { current_store_id: Spree::Store.current.id },
          test: true,
        ).deliver_now
      else
        mailer_class.constantize.send(template_name, record, test: true).deliver_now
      end
    end

    def variables_with_custom_message
      self.variables ||= customer_mailer_variables
      if tags.include?(Spree::Tag.shipping_tag) && self.variables.exclude?("custom_message")
        self.variables << "custom_message"
      end
      self.variables << "shipment_note" if template_name == "shipped_email" && self.variables.exclude?("shipment_note")
      self.variables
    end

    def customer_mailer_variables
      [
        "username",
        "user_email",
        "order_number",
        "order_total",
        "tax",
        "subtotal",
        "store_name",
        "line_item_details",
        "line_items_table",
        "tracking",
      ]
    end

    private

    def update_variables
      self.variables = ["username", "user_email", "store_name", "campaign_title"]
    end

    def update_sale_channels
      if tags.map(&:name).include?(Spree.t(:shipping_notification_email))
        param_ids = sale_channel_ids || []
        existing_ids = self.sale_channels.pluck(:id) || []

        to_add_or_update = param_ids - existing_ids
        to_remove = existing_ids - param_ids

        if to_remove.present?
          Spree::SaleChannel.where(id: to_remove).update_all(email_template_id: nil)
        end
        if to_add_or_update.present?
          Spree::SaleChannel.where(id: to_add_or_update).update_all(email_template_id: self.id)
        end
      else
        Spree::SaleChannel.where(id: sale_channel_ids).update_all(email_template_id: nil)
      end
    end

    def create_sale_channels
      if tags.map(&:name).include?(Spree.t(:shipping_notification_email))
        params_ids = sale_channel_ids || []
        if params_ids.present?
          Spree::SaleChannel.where(id: params_ids).update_all(email_template_id: self.id)
        end
      end
    end

    def welcome_email
      welcome_variables
    end

    def confirm_email
      order_vaiables
    end

    def cancel_email
      order_vaiables
    end

    def shipped_email
      shipment_variables
    end

    def store_owner_notification_email
      order_vaiables
    end

    def return_authorization_email
      return_authorization_variables
    end

    def return_authorization_send_label_email
      return_authorization_send_label_variables
    end

    def reimbursement_email
      reimbursement_email_variables
    end

    def confirmation_instructions
      confirmation_instructions_variables
    end

    def reset_password_instructions
      user_variables
    end

    def subscription_email
      subscription_email_variables
    end

    def subscription_ended_email
      subscription_email_variables
    end

    def unsubscribed_email
      subscription_email_variables
    end

    def paypal_payment_url
      paypal_payment_variables
    end

    def send_confirmation
      {
        "user_email" => @resource.email,
        "confirmation_code" => @resource.otp_code,
        "customer_support_email" => @options[:customer_support_email],
      }
    end

    def seller_welcome_email
      {
        "user_email" => @resource.admin_email,
        "store_name" => @options[:store_name],
        "workplace_name" => @options[:workplace_name],
        "customer_support_email" => @options[:customer_support_email],
        "setup_guide_url" => @options[:setup_guide_url]
      }
    end

    def store_employee_invite
      {
        "user_email" => @options[:user_email],
        "admin_email" => @resource.admin_email,
        "store_name" => @options[:store_name],
        "workplace_name" => @options[:workplace_name],
        "customer_support_email" => @options[:customer_support_email],
        "invitation_url" => @options[:invitation_url]
      }
    end

    def paypal_payment_variables
      {
        "customer_firstname" => @resource.user&.addresses&.take&.firstname,
        "order_number" => @resource.number,
        "purchase_timestamp" => @resource.completed_at,
        "seller_support_email" => @resource.store&.customer_support_email,
        "store_name" => @resource.store&.name,
        "user_email" => @resource.email,
        "customer_email" => @resource.user&.email,
        "customer_shipping_address" => @resource.ship_address&.display_name,
        "order_total" => Spree::Money.new(@resource.total, currency: "USD").to_s,
        "line_item_details" => @options[:item_details],
        "payment_link" => @options[:url],
        "payable_amount" => @options[:amount],
      }
    end

    def order_vaiables
      {
        "customer_firstname" => @resource.user&.addresses&.take&.firstname,
        "order_number" => @resource.number,
        "purchase_timestamp" => @resource.completed_at,
        "canceled_timestamp" => @resource.canceled_at,
        "total_amount_refund" => @resource.total,
        "payment_type_refund" => @resource.payments&.take&.payment_method&.name,
        "seller_support_email" => @resource.store&.customer_support_email,
        "store_name" => @resource.store&.name,
        "user_email" => @resource.email,
        "customer_email" => @resource.user&.email,
        "customer_lastname" => @resource.user&.addresses&.take&.lastname,
        "username" => @resource.name,
        "customer_shipping_address" => @resource.ship_address&.display_name,
        "order_total" => @resource.total,
        "line_item_details" => @options[:line_item_details],
        "tax" => @resource.additional_tax_total,
        "subtotal" => @resource.item_total,
        "line_items_table" => @options[:line_items_table],
        "store_logo" => @resource.store&.logo,
      }
    end

    def shipment_variables
      Rails.logger.info("Resource state first reload and ship: #{@resource.custom_message}")
      {
        "customer_firstname" => @resource.order.user&.addresses&.take&.firstname,
        "order_number" => @resource.order.number,
        "purchase_timestamp" => @resource.order.completed_at,
        "tracking" => @resource.reload.tracking,
        "line_item_details" => @options[:line_item_details],
        "order_total" => @resource.order.total,
        "store_name" => @resource.store.name,
        "seller_support_email" => @resource.store.customer_support_email,
        "username" => @resource.order.name,
        "user_email" => @resource.order.email,
        "shipping_method_name" => @resource.shipping_method&.name,
        "track_information" => @resource.tracking,
        "tax" => @resource.order.additional_tax_total,
        "subtotal" => @resource.order.item_total,
        "line_items_table" => @options[:line_items_table],
        "custom_message" => @resource.reload.custom_message,
        "shipment_note" => @options[:shipment_note],
      }
    end

    def reimbursement_variables
      {
        "customer_firstname" => @resource.user&.addresses&.take&.firstname,
        "username" => @resource.order.name,
        "user_email" => @resource.order.email,
        "order_number" => @resource.order.number,
        "refund_total" => @resource.total,
        "line_item_details" => @options[:line_item_details],
        "tax" => @resource.order.additional_tax_total,
        "subtotal" => @resource.order.item_total,
        "store_name" => @resource.store.name,
        "line_items_table" => @options[:line_items_table],
      }
    end

    def return_authorization_variables
      {
        "customer_firstname" => @resource.order.user&.addresses&.take&.firstname,
        "order_number" => @resource.order.number,
        "tracking" => @resource.order.shipments&.map(&:tracking)&.join(","),
        "return_item_details" => @options[:return_item_details],
        "order_total" => @resource.order.total,
        "store_name" => @resource.order.store.name,
        "rma_number" => @resource.number,
        "username" => @resource.order.name,
        "user_email" => @resource.order.email,
        "return_authorization_number" => @resource.number,
        "address1" => @resource.stock_location.address1,
        "city" => @resource.stock_location.city,
        "zipcode" => @resource.stock_location.zipcode,
        "phone" => @resource.stock_location.phone,
      }
    end

    def return_authorization_send_label_variables
      {
        "customer_firstname" => @resource.order.user&.addresses&.take&.firstname,
        "order_number" => @resource.order.number,
        "tracking" => @resource.order.shipments&.map(&:tracking)&.join(","),
        "customer_tracking" => @options[:customer_tracking],
        "customer_tracking_label" => @options[:customer_tracking_label],
        "customer_tracking_number" => @options[:customer_tracking_number],
        "order_total" => @resource.order.total,
        "store_name" => @resource.order.store.name,
        "rma_number" => @resource.number,
        "username" => @resource.order.name,
        "user_email" => @resource.order.email,
        "return_authorization_number" => @resource.number,
        "address1" => @resource.stock_location.address1,
        "city" => @resource.stock_location.city,
        "zipcode" => @resource.stock_location.zipcode,
        "phone" => @resource.stock_location.phone,
      }
    end

    def reimbursement_email_variables
      {
        "username" => @resource.order.name,
        "user_email" => @resource.order.email,
        "order_number" => @resource.order.number,
        "store_name" => @resource.order.store.name,
        "reimbursement_item_details" => @options[:reimbursement_item_details],
        "reimbursement_total" => @resource.total,
      }
    end

    def confirmation_instructions_variables
      {
        "user_email" => @resource.email,
        "confirmation_url" => @options[:confirmation_url],
      }
    end

    def user_variables
      {
        "customer_email" => @resource.email,
        "store_name" => @options[:store].name,
        "password_reset_url" => @options[:password_reset_url],
        "seller_support_email" => @options[:store].customer_support_email,
      }
    end

    def welcome_variables
      {
        "customer_firstname" => @resource&.addresses&.take&.firstname,
        "customer_lastname" => @resource&.addresses&.take&.lastname,
        "seller_support_email" => @options[:store].customer_support_email,
      }
    end

    def subscription_email_variables
      {
        "username" => @resource.first_name,
        "user_email" => @options[:user_email],
        "product_name" => @options[:subscription].line_item.listing.title,
        "delivery_frequency" => @options[:subscription].interval&.titleize,
        "price" => @options[:subscription].line_item.price,
        "next_scheduled_delivery" => @options[:subscription].next_delivery_date.to_date,
      }
    end

    def record
      # TO DO: dummy records to handle nil calss errors
      case mailer_class
      when "Spree::OrderMailer"
        Spree::Order.last
      when "Spree::ShipmentMailer"
        Spree::Shipment.last
      when "Spree::ReturnAuthorizationMailer"
        Spree::ReturnAuthorization.last
      when "Spree::ReturnAuthorizationSendLabelMailer"
        Spree::ReturnAuthorization.last
      when "Spree::ReimbursementMailer"
        Spree::Reimbursement.last
      when "Spree::UserMailer"
        Spree::User.last
      when "Spree::SubscriptionMailer"
        Spree::Subscription.last
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_email_templates
#
#  id            :bigint           not null, primary key
#  active        :boolean
#  body          :string
#  description   :string
#  mailer_class  :string
#  subject       :string
#  template_name :string
#  template_type :string           default("other")
#  variables     :string           is an Array
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  store_id      :bigint           indexed
#
# Indexes
#
#  index_spree_email_templates_on_store_id  (store_id)
#
