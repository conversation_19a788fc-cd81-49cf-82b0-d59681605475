# frozen_string_literal: true

module Spree
  module Stock
    class OrderPackageEstimator
      class_attribute :easypost_setting

      def shipping_rates(package, shipping_method_filter = ::Spree::ShippingMethod::DISPLAY_ON_FRONT_END,
        parcel = nil)

        self.easypost_setting = parcel[:easypost_setting].present? ? parcel[:easypost_setting] : Spree::EasypostSetting.first

        weight = package.is_a?(Array) ? package.sum(&:weight) : package.weight

        if weight.zero? && (parcel.nil? || parcel[:weight].nil?)
          parcel = { weight: 0.1 }
        end

        parcel_weight = parcel.is_a?(Hash) && parcel[:weight].present? ? parcel[:weight].to_f : weight

        # Only use easypost on the FrontEnd if the flag is set and the package
        # flag allows for it to be used. Otherwise use the default spree methods.
        # This allows for faster load times on the front end if we dont want to do dyanmic shipping
        if package.present? && use_easypost_to_calculate_rate?(package, shipping_method_filter, parcel_weight)

          order_package = package.map { |pack| pack.shipment.order_package }[0]
          order_package = order_package.easypost_shipment(parcel, weight)
          return [] if order_package.nil? || order_package[:error].present?

          rates = order_package.rates.sort_by { |r| r.rate.to_i }
          shipping_rates = []
          if rates.any?
            order = package.is_a?(Array) ? package[0].order : package.order
            rates.each do |rate|
              # See if we can find the shipping method otherwise create it
              shipping_method = find_or_create_shipping_method(rate, order)
              # Get the calculator to see if we want to use easypost rate
              calculator = shipping_method.calculator
              quantity = package.sum(&:quantity)
              # order = package.is_a?(Array) ? package[0].order : package.order
              rate_cost = order.state == "complete" && order.shipment_state == "ready" ? rate.rate.to_f : rate.rate.to_f * quantity
              # Create the easypost rate
              spree_rate = ::Spree::ShippingRate.new(
                cost: if calculator.class == ::Spree::Calculator::Shipping::EasypostRate
                        rate_cost
                      else
                        calculator.compute(package)
                      end,
                easy_post_shipment_id: rate.shipment_id,
                easy_post_rate_id: rate.id,
                shipping_method: shipping_method,
              )
              # Save the rates that we want to show the customer
              shipping_rates << spree_rate if shipping_method.available_to_display?(shipping_method_filter)
            end
            # Sets cheapest rate to be selected by default
            if shipping_rates.any?
              shipping_rates.min_by(&:cost).selected = true
            end

            shipping_rates
          else
            []
          end
        else
          # rates = calculate_shipping_rates(package, shipping_method_filter)
          # choose_default_shipping_rate(rates)
          # sort_shipping_rates(rates)
          []
        end
      end

      private

      def use_easypost_to_calculate_rate?(package, shipping_method_filter, package_weight)
        return false if package_weight <= 0

        all_use_easypost = package.all?(&:use_easypost?)
        all_have_shipment = package.all? { |pack| pack.shipment.present? }
        any_free_shipping = package.any? { |pack| pack.shipping_categories.map(&:name).include?("Free Shipping") }

        easypost_condition = all_use_easypost || (all_have_shipment && any_free_shipping)
        shipping_method_condition = ::Spree::ShippingMethod::DISPLAY_ON_BACK_END == shipping_method_filter ||
          shipping_rate_dynamic_on_front_end?(shipping_method_filter)

        easypost_condition && shipping_method_condition
      end

      def shipping_rate_dynamic_on_front_end?(shipping_method_filter)
        # used easypostsettings table for retrieving the easypost configuration
        easypost_setting.use_easypost_on_frontend &&
          (::Spree::ShippingMethod::DISPLAY_ON_FRONT_END == shipping_method_filter)
      end

      # Cartons require shipping methods to be present, This will lookup a
      # Shipping method based on the admin(internal)_name. This is not user facing
      # and should not be changed in the admin.
      def find_or_create_shipping_method(rate, order = nil)
        method_name = "#{rate.carrier} #{rate.service}"
        ::Spree::ShippingMethod.find_or_create_by(admin_name: method_name) do |r|
          r.name = method_name
          r.store_id = order.store.id
          r.display_on = "both"
          r.code = rate.service
          r.calculator = ::Spree::Calculator::Shipping::EasypostRate.create
          # get shipping category where use_easypost is true
          r.shipping_categories = [order.store.shipping_categories.where(use_easypost: true).first]
        end
      end
    end
  end
end
