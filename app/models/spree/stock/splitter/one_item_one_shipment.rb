# frozen_string_literal: true

module Spree
  module Stock
    module Splitter
      class OneItemOneShipment < Spree::Stock::Splitter::Base
        def split(packages)
          packages.flat_map do |package|
            package.contents.map do |content|
              new_package = build_package
              new_package.contents << content
              new_package
            end
          end
        end
      end
    end
  end
end
