module Spree
  module Stock
    module PrioritizerDecorator
      private
      def hash_item(item)
        shipment = item.inventory_unit.shipment
        variant  = item.inventory_unit.variant
        line_item = item.inventory_unit.line_item
        if shipment.present?
          variant.hash ^ shipment.hash ^ line_item.hash
        else
          variant.hash ^ line_item.hash
        end
      end
    end
  end
end

Spree::Stock::Prioritizer.prepend(Spree::Stock::PrioritizerDecorator)
