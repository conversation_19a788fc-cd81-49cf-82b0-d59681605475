# frozen_string_literal: true

module Spree
  class Subscriber < ApplicationRecord
    has_many :campaign_subscribers, class_name: 'Spree::CampaignSubscriber', dependent: :destroy
    has_many :campaigns, through: :campaign_subscribers, class_name: 'Spree::Campaign'
    has_many :subscriber_behaviors, class_name: "Spree::SubscriberBehavior"
    belongs_to :sale_channel, class_name: 'Spree::SaleChannel', optional: true

    ## enums
    enum level: { subscriber: 0, customer: 1, buyer: 2, VIP: 3 }, _default: 0

    def is_subscriber?
      level == "subscriber"
    end

    def is_customer?
      level == "customer"
    end

    def is_buyer?
      level == "buyer"
    end

    def is_vip?
      level == "VIP"
    end

    VIP_ORDER_COUNT_THRESHOLD = 100
    VIP_QUANTITY_COUNT_THRESHOLD = 100
    VIP_AMOUNT_SUM_THRESHOLD = 1000

    def self.for_store(store)
      self
    end

    def self.json_api_permitted_attributes
      skipped_attributes = %w[id]
      if included_modules.include?(CollectiveIdea::Acts::NestedSet::Model)
        skipped_attributes.push('lft', 'rgt', 'depth')
      end
      column_names.reject { |c| skipped_attributes.include?(c.to_s) }
    end
  end
end

# == Schema Information
#
# Table name: spree_subscribers
#
#  id                  :bigint           not null, primary key
#  amount_sum          :decimal(10, 2)   default(0.0), not null
#  campaign_subscribed :boolean          default(TRUE)
#  email               :string           not null, indexed
#  level               :integer          default("subscriber"), not null
#  login_count         :integer          default(0), not null
#  name                :string
#  order_count         :integer          default(0), not null
#  quantity_count      :integer          default(0), not null
#  source              :string           default("storefront"), not null
#  visit_count         :integer          default(0), not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  sale_channel_id     :integer
#
# Indexes
#
#  index_spree_subscribers_on_email  (email) UNIQUE
#
