# frozen_string_literal: true

module Spree
  class StoreUser < Spree::Base
    belongs_to :user, class_name: Spree.user_class.to_s
    belongs_to :store, class_name: "Spree::Store"
  end
end

# == Schema Information
#
# Table name: spree_store_users
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  store_id   :bigint           indexed
#  user_id    :bigint           indexed
#
# Indexes
#
#  index_spree_store_users_on_store_id  (store_id)
#  index_spree_store_users_on_user_id   (user_id)
#
