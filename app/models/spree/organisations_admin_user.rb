# frozen_string_literal: true

module Spree
  class OrganisationsAdminUser < ApplicationRecord
    belongs_to :organisation, class_name: 'Spree::Organisation'
    belongs_to :admin_user, class_name: 'Spree::AdminUser'
  end
end

# == Schema Information
#
# Table name: public.spree_organisations_admin_users
#
#  id              :bigint           not null, primary key
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  admin_user_id   :bigint           not null, indexed => [organisation_id]
#  organisation_id :bigint           not null, indexed => [admin_user_id]
#
# Indexes
#
#  index_unique_org_admin_user  (organisation_id,admin_user_id) UNIQUE
#
