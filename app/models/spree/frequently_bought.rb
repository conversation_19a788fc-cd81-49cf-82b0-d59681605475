# frozen_string_literal: true

module Spree
  class FrequentlyBought < ApplicationRecord
    acts_as_list
    belongs_to :listing
    belongs_to :matched_listing, class_name: "Spree::Listing"

    default_scope { order("#{table_name}.position, #{table_name}.updated_at") }

    def display_price
      matched_product = matched_listing.product
      variant_ids = matched_product.variants.ids.presence || [matched_product.master.id]
      price = listing&.listing_inventories&.where(variant_id: variant_ids)&.find_by(is_selected: true)&.price
      price.present? ? Spree::Money.new(price, currency: "USD").to_s : nil
    end
  end
end

# == Schema Information
#
# Table name: spree_frequently_boughts
#
#  id                 :bigint           not null, primary key
#  position           :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  listing_id         :bigint           indexed
#  matched_listing_id :bigint
#
# Indexes
#
#  index_spree_frequently_boughts_on_listing_id  (listing_id)
#
