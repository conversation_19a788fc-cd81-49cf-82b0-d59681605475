# frozen_string_literal: true

module Spree
  class Store < Spree::Base
    include TranslatableResource
    include Spree::Webhooks::Has<PERSON>ebhooks

    acts_as_paranoid

    TRANSLATABLE_FIELDS = [
      :name,
      :meta_description,
      :meta_keywords,
      :seo_title,
      :facebook,
      :twitter,
      :instagram,
      :customer_support_email,
      :description,
      :address,
      :contact_phone,
      :new_order_notifications_email,
    ].freeze
    translates(*TRANSLATABLE_FIELDS)

    self::Translation.class_eval do
      acts_as_paranoid
      # deleted translation values still need to be accessible - remove deleted_at scope
      default_scope { unscope(where: :deleted_at) }
    end

    belongs_to :default_country,
      class_name: "Spree::Country"
    belongs_to :checkout_zone,
      class_name: "Spree::Zone"
    has_one :logo,
      class_name: "Spree::StoreLogo",
      dependent: :destroy,
      as: :viewable
    has_one :mailer_logo,
      class_name: "Spree::StoreMailerLogo",
      dependent: :destroy,
      as: :viewable
    has_one :favicon_image,
      class_name: "Spree::StoreFaviconImage",
      dependent: :destroy,
      as: :viewable
    has_one :invoice_setting,
      dependent: :destroy
    has_one :email_setting, -> { where(email_setting_type: 'store_email') }, 
          class_name: 'Spree::EmailSetting', foreign_key: 'store_id', dependent: :destroy
    has_one :campaign_email_setting, -> { where(email_setting_type: 'campaign_email') }, 
          class_name: 'Spree::EmailSetting', foreign_key: 'store_id', dependent: :destroy
    has_one :active_shipping_setting,
      class_name: "Spree::ActiveShippingSetting",
      dependent: :destroy
    has_one :taxjar_setting,
      dependent: :destroy
    has_one :easypost_setting,
      class_name: "Spree::EasypostSetting",
      dependent: :destroy
    has_one :stripe_tax_setting,
      class_name: "::Spree::StripeTaxSettings",
      dependent: :destroy

    has_many :campaigns, class_name: 'Spree::Campaign'
    has_many :blog_post_attachments,
      class_name: 'Spree::BlogPostAttachment'
    has_many :orders,
      class_name: "Spree::Order"
    has_many :line_items,
      through: :orders,
      class_name: "Spree::LineItem"
    has_many :payments,
      through: :orders,
      class_name: "Spree::Payment"
    has_many :store_payment_methods,
      class_name: "Spree::StorePaymentMethod"
    has_many :payment_methods,
      through: :store_payment_methods,
      class_name: "Spree::PaymentMethod"
    has_many :return_authorizations,
      through: :orders,
      class_name: "Spree::ReturnAuthorization"
    has_many :customer_returns,
      class_name: "Spree::CustomerReturn",
      inverse_of: :store

    has_many :shipments,
      through: :orders,
      class_name: "Spree::Shipment"

    has_many :cms_pages,
      class_name: "Spree::CmsPage"
    has_many :cms_sections,
      through: :cms_pages,
      class_name: "Spree::CmsSection"

    has_many :menus,
      class_name: "Spree::Menu"
    has_many :menu_items,
      through: :menus,
      class_name: "Spree::MenuItem"

    has_many :store_products,
      class_name: "Spree::StoreProduct"
    has_many :products,
      through: :store_products,
      class_name: "Spree::Product"
    has_many :product_properties,
      through: :products,
      class_name: "Spree::ProductProperty"
    has_many :variants,
      through: :products,
      class_name: "Spree::Variant",
      source: :variants_including_master
    has_many :stock_items,
      through: :variants,
      class_name: "Spree::StockItem"
    has_many :inventory_units,
      through: :variants,
      class_name: "Spree::InventoryUnit"

    has_many :store_credits,
      class_name: "Spree::StoreCredit"
    has_many :store_credit_events,
      through: :store_credits,
      class_name: "Spree::StoreCreditEvent"

    has_many :taxonomies,
      class_name: "Spree::Taxonomy"
    has_many :taxons,
      through: :taxonomies,
      class_name: "Spree::Taxon"

    has_many :store_promotions,
      class_name: "Spree::StorePromotion"
    has_many :promotions,
      through: :store_promotions,
      class_name: "Spree::Promotion"

    has_many :wishlists,
      class_name: "Spree::Wishlist"

    has_many :data_feeds,
      class_name: "Spree::DataFeed"

    has_many :shipping_categories,
      class_name: "Spree::ShippingCategory",
      dependent: :destroy
    has_many :shipping_methods,
      class_name: "Spree::ShippingMethod",
      dependent: :destroy

    has_many :tax_categories,
      dependent: :destroy
    has_many :tax_rates,
      dependent: :destroy

    has_many :spree_asset,
      class_name: "Spree::Asset",
      dependent: :destroy
    has_many :stock_locations,
      class_name: "Spree::StockLocation",
      dependent: :destroy
    has_many :option_types,
      class_name: "Spree::OptionType",
      dependent: :destroy
    has_many :properties,
      class_name: "Spree::Property",
      dependent: :destroy
    has_many :prototypes,
      class_name: "Spree::Prototype",
      dependent: :destroy
    has_many :oauth_application,
      class_name: "Spree::OauthApplication",
      dependent: :destroy
    has_many :email_templates,
      dependent: :destroy
    has_many :tags,
      dependent: :destroy
    has_many :gallery_items,
      dependent: :destroy
    has_many :listing,
      dependent: :destroy
    has_many :condition,
      dependent: :destroy
    has_many :ebay_policy,
      dependent: :destroy
    has_many :stock_transfers,
      class_name: "Spree::StockTransfer",
      dependent: :destroy
    has_many :store_users,
      class_name: "Spree::StoreUser",
      dependent: :destroy
    has_many :role_users,
      class_name: "Spree::RoleUser",
      dependent: :destroy
    has_many :scan_forms,
      class_name: "Spree::ScanForm",
      dependent: :destroy
    has_many :sale_channels,
      dependent: :destroy
    has_many :user_notifications

    has_many :blog_categories, class_name: "Spree::BlogCategory", dependent: :destroy
    has_many :blog_posts, through: :blog_categories, class_name: "Spree::BlogPost", dependent: :destroy

    default_scope { order(created_at: :asc) }
    scope :by_url, ->(url) { where("url like ?", "%#{url}%") }
    scope :for_store, ->(store) { where(id: store.id) }

    typed_store :settings, coder: ActiveRecord::TypedStore::IdentityCoder do |s|
      # Spree Digital Asset Configurations
      s.boolean(:limit_digital_download_count, default: true, null: false)
      s.boolean(:limit_digital_download_days, default: true, null: false)
      # number of times a customer can download a digital file.
      s.integer(:digital_asset_authorized_clicks, default: 5, null: false)
      # number of days after initial purchase the customer can download a file.
      s.integer(:digital_asset_authorized_days, default: 7, null: false)
      s.integer(:digital_asset_link_expire_time, default: 300, null: false) # 5 minutes in seconds
    end

    accepts_nested_attributes_for :logo, reject_if: :all_blank
    accepts_nested_attributes_for :mailer_logo, reject_if: :all_blank
    accepts_nested_attributes_for :favicon_image, reject_if: :all_blank
    attr_accessor :skip_validate_not_last

    alias_attribute :contact_email, :customer_support_email

    with_options presence: true do
      validates :name, :url, :mail_from_address, :default_currency, :code
    end
    validates :code, uniqueness: { case_sensitive: false, conditions: -> { with_deleted } }
    validates :mail_from_address, email: { allow_blank: false }
    validates :new_order_notifications_email, email: { allow_blank: true }
    validates :digital_asset_authorized_clicks, numericality: { only_integer: true, greater_than: 0 }
    validates :digital_asset_authorized_days, numericality: { only_integer: true, greater_than: 0 }

    before_save :ensure_default_exists_and_is_unique
    before_save :ensure_supported_currencies, :ensure_supported_locales, :ensure_default_country
    before_destroy :validate_not_last, unless: :skip_validate_not_last
    before_destroy :pass_default_flag_to_other_store
    after_commit :clear_cache
    after_create_commit :create_email_template
    after_create_commit :create_storefront_sale_channel

    def self.current(url = nil)
      ActiveSupport::Deprecation.warn(<<-DEPRECATION, caller)
        `Spree::Store.current` is deprecated and will be removed in Spree 5.0
        Please use `Spree::Stores::FindCurrent.new(url: "https://example.com").execute` instead
      DEPRECATION
      Stores::FindCurrent.new(url: url).execute
    end

    def self.default
      Rails.cache.fetch("default_store_#{Apartment::Tenant.current}") do
        # workaround for Mobility bug with first_or_initialize
        if where(default: true).any?
          where(default: true).first
        else
          new(default: true)
        end
      end
    end

    def self.available_locales
      Rails.cache.fetch("stores_available_locales_#{Apartment::Tenant.current}") do
        Spree::Store.all.map(&:supported_locales_list).flatten.uniq
      end
    end

    def default_menu(location)
      menu = menus.find_by(location: location, locale: default_locale) || menus.find_by(location: location)
      menu.root if menu.present?
    end

    def supported_currencies_list
      @supported_currencies_list ||= (self[:supported_currencies].to_s.split(",") << default_currency).sort.map(&:to_s).map do |code|
        ::Money::Currency.find(code.strip)
      end.uniq.compact
    end

    def homepage(requested_locale)
      cms_pages.by_locale(requested_locale).find_by(type: "Spree::Cms::Pages::Homepage") ||
        cms_pages.by_locale(default_locale).find_by(type: "Spree::Cms::Pages::Homepage") ||
        cms_pages.find_by(type: "Spree::Cms::Pages::Homepage")
    end

    # Generate unique ID based on UUID and store.url
    def price_analytics_customer_id
      Digest::UUID.uuid_v3("4a69d03a-4fdd-47d5-b8b8-4a2ceb7a6f37", url)
    end

    def seo_meta_description
      if meta_description.present?
        meta_description
      elsif seo_title.present?
        seo_title
      else
        name
      end
    end

    def supported_locales_list
      # TODO: add support of multiple supported languages to a single Store
      @supported_locales_list ||= (self[:supported_locales].to_s.split(",") << default_locale).compact.uniq.sort
    end

    def unique_name
      @unique_name ||= "#{name} (#{code})"
    end

    def formatted_url
      return if url.blank?

      @formatted_url ||= if url.match?(%r{http://|https://})
        url
      else
        Rails.env.development? || Rails.env.test? ? "http://#{url}" : "https://#{url}"
      end
    end

    def countries_available_for_checkout
      @countries_available_for_checkout ||= checkout_zone.try(:country_list) || Spree::Country.all
    end

    def states_available_for_checkout(country)
      checkout_zone.try(:state_list_for, country) || country.states
    end

    def checkout_zone_or_default
      ActiveSupport::Deprecation.warn("Store#checkout_zone_or_default is deprecated and will be removed in Spree 5")

      @checkout_zone_or_default ||= checkout_zone || Spree::Zone.default_checkout_zone
    end

    def favicon
      return unless favicon_image&.attachment&.attached?

      favicon_image.attachment.variant(resize_to_limit: [32, 32])
    end

    def can_be_deleted?
      self.class.where.not(id: id).any?
    end

    private

    def ensure_default_exists_and_is_unique
      if default
        Store.where.not(id: id).update_all(default: false) # rubocop:disable Rails/SkipsModelValidations
      elsif Store.where(default: true).count.zero?
        self.default = true
      end
    end

    def ensure_supported_locales
      return if attributes.keys.exclude?("supported_locales")
      return if supported_locales.present?
      return if default_locale.blank?

      self.supported_locales = default_locale
    end

    def ensure_supported_currencies
      return if attributes.keys.exclude?("supported_currencies")
      return if supported_currencies.present?
      return if default_currency.blank?

      self.supported_currencies = default_currency
    end

    def validate_not_last
      unless can_be_deleted?
        errors.add(:base, :cannot_destroy_only_store)
        throw(:abort)
      end
    end

    def pass_default_flag_to_other_store
      if default? && can_be_deleted?
        self.class.where.not(id: id).first.update!(default: true)
        self.default = false
      end
    end

    def clear_cache
      Rails.cache.delete("default_store_#{Apartment::Tenant.current}")
      Rails.cache.delete("stores_available_locales_#{Apartment::Tenant.current}")
    end

    def ensure_default_country
      return unless has_attribute?(:default_country_id)
      return if default_country.present? && (checkout_zone.blank? || checkout_zone.country_list.blank? ||
        checkout_zone.country_list.include?(default_country))

      self.default_country = if checkout_zone.present? && checkout_zone.country_list.any?
        checkout_zone.country_list.first
      else
        Country.find_by(iso: "US") || Country.first
      end
    end

    def meta_tags_length
      if meta_tags.present? && meta_tags.length > 1
        message = "#{I18n.t(:meta_tags_error)}: Only 0 or 1 meta tag is allowed."
        raise message
      end
    end

    def create_email_template
      AxelSpree::Spree::Seeds::EmailTemplate.call(self)
    end

    def create_storefront_sale_channel
      sale_channel = ::Spree::SaleChannel.create!(brand: "storefront", store_id: id)
      oauth_application = ::Spree::OauthApplication.create!(name: "Axel Storefront", sale_channel_id: sale_channel.id) if sale_channel
      oauth_application.access_tokens.create! if oauth_application.present?
    end
  end
end

# == Schema Information
#
# Table name: spree_stores
#
#  id                            :bigint           not null, primary key
#  about_us                      :string
#  address                       :text
#  barcodelookup_api_key         :string
#  code                          :string           indexed
#  contact_phone                 :string
#  contact_us                    :string
#  customer_support_email        :string
#  default                       :boolean          default(FALSE), not null, indexed
#  default_currency              :string
#  default_locale                :string
#  deleted_at                    :datetime         indexed
#  description                   :text
#  facebook                      :string
#  guarantee                     :string
#  instagram                     :string
#  mail_from_address             :string
#  meta_description              :text
#  meta_keywords                 :text
#  meta_tags                     :jsonb            not null
#  name                          :string
#  new_order_notifications_email :string
#  number_tracking               :boolean          default(FALSE)
#  price_analytics_client_secret :string
#  price_analytics_url           :string
#  published                     :boolean          default(FALSE)
#  return_refund_policy          :string
#  risky_listing                 :boolean          default(FALSE)
#  seo_robots                    :string
#  seo_title                     :string
#  settings                      :jsonb
#  shipping_delivery             :string
#  supported_currencies          :string
#  supported_locales             :string
#  term_policies                 :string
#  twitter                       :string
#  upcitemdb_api_key             :string
#  url                           :string           indexed
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  checkout_zone_id              :bigint
#  default_country_id            :bigint
#  destination_id                :jsonb
#  price_analytics_client_id     :string
#
# Indexes
#
#  index_spree_stores_on_code        (code) UNIQUE
#  index_spree_stores_on_default     (default)
#  index_spree_stores_on_deleted_at  (deleted_at)
#  index_spree_stores_on_url         (url)
#
