# frozen_string_literal: true

module Spree
  class TaxCategory < Spree::Base
    include Spree::SingleStoreResource
    include Spree::Webhooks::HasWebhooks

    acts_as_paranoid

    belongs_to :store

    has_many :tax_rates,
      inverse_of: :tax_category,
      dependent: :destroy

    has_many :products,
      dependent: :nullify
    has_many :variants,
      dependent: :nullify

    alias_attribute :default, :is_default

    validates :name,
      presence: true,
      uniqueness: { case_sensitive: true, scope: spree_base_uniqueness_scope.push(:deleted_at, :store) }

    before_save :set_default_category

    self.whitelisted_ransackable_attributes = ["name", "is_default", "tax_code"]

    protected

    def set_default_category
      # set existing default tax category to false if this one has been marked as default
      if is_default && (tax_category = self.class.where(is_default: true).where.not(id: id).first)
        tax_category.update_columns(is_default: false, updated_at: Time.current) # rubocop:disable Rails/SkipsModelValidations
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_tax_categories
#
#  id          :bigint           not null, primary key
#  deleted_at  :datetime         indexed
#  description :string
#  is_default  :boolean          default(FALSE), indexed
#  name        :string
#  tax_code    :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  store_id    :bigint           indexed
#
# Indexes
#
#  index_spree_tax_categories_on_deleted_at  (deleted_at)
#  index_spree_tax_categories_on_is_default  (is_default)
#  index_spree_tax_categories_on_store_id    (store_id)
#
