# frozen_string_literal: true

module Spree
  class RecommendedPrice < Spree::Base
    PERIODS = { 1 => "1 hour", 2 => "12 hour", 3 => "1 day", 4 => "1 week" }.freeze

    belongs_to :variant
    belongs_to :listing_inventory

    state_machine :status, initial: :wait do
      event :activate do
        transition to: :active
      end

      event :error do
        transition to: :error
      end
    end

    alias_attribute :date, :created_at

    validates :current_price,
      :zero_profit_price,
      :average_price,
      :average_effective_price,
      :weighted_average_effective,
      :min_price_ebay,
      numericality: { greater_than_or_equal_to: 0, allow_nil: true, less_than: BigDecimal(10**8) }

    before_save :set_auto_updated_at

    def apply_recommended_price!
      return if recommended_price[:value].to_f < zero_profit_price

      old_price = listing_inventory.price
      listing_inventory.price = recommended_price[:value]
      listing_inventory.save!

      Spree::PriceHistory.create!(
        variant: variant,
        currency: "USD",
        previous_amount: old_price,
        amount: listing_inventory.price,
        kind: "auto",
        strategy: recommended_price[:name],
      )
    end

    def apply_indexed_price!(idx)
      old_price = listing_inventory.price
      listing_inventory.price = if idx.zero?
        recommended_price[:value]
      else
        additional_prices[idx - 1][:value]
      end
      prices[:applied] = idx
      save!
      listing_inventory.save!

      strategy = if idx.zero?
        recommended_price[:name]
      else
        additional_prices[idx - 1][:name]
      end
      PriceHistory.create!(
        variant: variant,
        currency: "USD",
        previous_amount: old_price,
        amount: listing_inventory.price,
        kind: "manual",
        strategy: strategy,
      )
    end

    def recommended_price
      (prices["recommended"] || {})
        .then { |val| val.is_a?(Hash) ? val : {} }
        .symbolize_keys
    end

    def additional_prices
      (prices["additional"] || []).map(&:symbolize_keys)
    end

    def query_id
      prices["query_id"]
    end

    def items
      prices.fetch("items") { [] }.map(&:symbolize_keys)
    end

    def accepted_items
      items.select { |item| item[:state] == "accepted" }
    end

    def ignored_items
      items.select { |item| item[:state] == "ignored" }
    end

    def change_item_state(item, state)
      prices["items"] = items.each do |i|
        i[:state] = state if i[:id] == item[:id]
      end
    end

    def change_item_quantity(item, quantity)
      prices["items"] = items.each do |i|
        i[:quantity] = quantity if i[:id] == item[:id]
      end
    end

    def calc_next_auto_update_at
      if auto_update?
        self.auto_update_period ||= 1
        self.auto_update_at = case auto_update_period
        when 1 then 1.hour.since
        when 2 then 12.hours.since
        when 3 then 1.day.since
        when 4 then 1.week.since
        end
      else
        self.auto_update_at = nil
      end
    end

    private

    def set_auto_updated_at
      return unless auto_update_changed?

      self.auto_update_at = auto_update? ? Time.current : nil
    end
  end
end

# == Schema Information
#
# Table name: spree_recommended_prices
#
#  id                         :bigint           not null, primary key
#  auto_update                :boolean          default(FALSE), not null
#  auto_update_at             :datetime
#  auto_update_period         :integer
#  average_effective_price    :decimal(10, 2)
#  average_price              :decimal(10, 2)
#  current_price              :decimal(10, 2)
#  data_updated_at            :datetime
#  error_message              :string
#  min_price_ebay             :decimal(10, 2)
#  prices                     :jsonb            not null
#  request                    :string
#  request_data               :jsonb
#  response                   :string
#  sales_period               :string
#  sales_rate_value           :string
#  settings                   :jsonb            not null
#  shipping_cost              :decimal(10, 2)
#  status                     :string           default("wait"), not null
#  stocks_rate_value          :string
#  weighted_average_effective :decimal(10, 2)
#  zero_profit_price          :decimal(10, 2)
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  analysis_id                :uuid
#  external_id                :string
#  listing_inventory_id       :integer
#  variant_id                 :integer          not null, indexed
#
# Indexes
#
#  index_spree_recommended_prices_on_variant_id  (variant_id)
#
