# frozen_string_literal: true

module Spree
  class CmsPage < Base
    include SingleStoreResource
    include Spree::DisplayLink
    include Spree::Webhooks::HasWebhooks

    TYPES = [
      "Spree::Cms::Pages::StandardPage",
      "Spree::Cms::Pages::FeaturePage",
      "Spree::Cms::Pages::Homepage",
    ]

    acts_as_paranoid

    belongs_to :store, touch: true

    has_many :cms_sections,
      class_name: "Spree::CmsSection"

    has_many :sections,
      class_name: "Spree::CmsSection"

    has_many :menu_items,
      as: :linked_resource

    scope :visible, -> { where(visible: true) }
    scope :by_locale, ->(locale) { where(locale: locale) }
    scope :by_slug, ->(slug) { where(slug: slug) }

    scope :home, -> { where(type: "Spree::Cms::Pages::Homepage") }
    scope :standard, -> { where(type: "Spree::Cms::Pages::StandardPage") }
    scope :feature, -> { where(type: "Spree::Cms::Pages::FeaturePage") }

    accepts_nested_attributes_for :sections, allow_destroy: true

    before_validation :handle_slug

    validates :title, :locale, :type, presence: true
    validates :slug, uniqueness: { scope: :store_id, allow_nil: true, case_sensitive: true }
    validates :locale, uniqueness: { scope: [:store, :type] }, if: :homepage?

    self.whitelisted_ransackable_attributes = ["title", "type", "locale"]

    def seo_title
      meta_title.presence || title
    end

    # Override this if your page type uses cms_sections
    def sections?
      false
    end

    def homepage?
      type == "Spree::Cms::Pages::Homepage"
    end

    def draft_mode?
      !visible
    end

    private

    def handle_slug
      self.slug = if homepage?
        nil
      elsif slug.blank?
        title&.downcase&.to_url
      else
        slug&.downcase&.to_url
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_cms_pages
#
#  id               :bigint           not null, primary key
#  content          :text
#  deleted_at       :datetime         indexed, indexed => [slug, store_id]
#  locale           :string           indexed => [store_id, type]
#  meta_description :text
#  meta_title       :string
#  settings         :jsonb
#  slug             :string           indexed => [store_id, deleted_at]
#  title            :string           not null, indexed => [type, store_id]
#  type             :string           indexed => [store_id, locale], indexed => [title, store_id]
#  visible          :boolean          default(TRUE), indexed
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  store_id         :bigint           indexed => [slug, deleted_at], indexed, indexed => [locale, type], indexed => [title, type]
#
# Indexes
#
#  index_spree_cms_pages_on_deleted_at                        (deleted_at)
#  index_spree_cms_pages_on_slug_and_store_id_and_deleted_at  (slug,store_id,deleted_at) UNIQUE
#  index_spree_cms_pages_on_store_id                          (store_id)
#  index_spree_cms_pages_on_store_id_and_locale_and_type      (store_id,locale,type)
#  index_spree_cms_pages_on_title_and_type_and_store_id       (title,type,store_id)
#  index_spree_cms_pages_on_visible                           (visible)
#
