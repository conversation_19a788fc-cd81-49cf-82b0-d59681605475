# frozen_string_literal: true

module Spree
  module Cms
    module Sections
      class ProductCarousel < Spree::CmsSection
        after_initialize :default_values

        LINKED_RESOURCE_TYPE = ["Spree::Taxon"]

        private

        def default_values
          self.fit ||= "Screen"
          self.linked_resource_type ||= "Spree::Taxon"
        end
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_cms_sections
#
#  id                   :bigint           not null, primary key
#  content              :text
#  destination          :string
#  fit                  :string
#  linked_resource_type :string           indexed => [linked_resource_id]
#  name                 :string           not null
#  position             :integer          indexed
#  properties           :jsonb            not null
#  settings             :text
#  type                 :string           indexed
#  visible              :boolean          default(TRUE), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  cms_page_id          :bigint           indexed
#  linked_resource_id   :bigint           indexed => [linked_resource_type]
#  listing_id           :bigint
#
# Indexes
#
#  index_spree_cms_sections_on_cms_page_id      (cms_page_id)
#  index_spree_cms_sections_on_linked_resource  (linked_resource_type,linked_resource_id)
#  index_spree_cms_sections_on_position         (position)
#  index_spree_cms_sections_on_type             (type)
#
