# frozen_string_literal: true

module Spree
  module Cms
    module Sections
      class ImageBanner < Spree::CmsSection
        class ImageBannerContent
          include AttrJson::Model

          attr_json_config(bad_cast: :as_nil, unknown_key: :strip)

          attr_json :url, :string, default: nil
          attr_json :header, :string, default: nil
          attr_json :header_visible, :boolean, default: true
          attr_json :content, :string, default: nil
          attr_json :content_visible, :boolean, default: true
          attr_json :text_color, :string, default: nil
          attr_json :button_visible, :boolean, default: true
          attr_json :button_label, :string, default: nil
          attr_json :button_link, :string, default: nil
          attr_json :button_color, :string, default: nil
          attr_json :content_position, :string, default: nil
          attr_json :content_alignment, :string, default: nil
        end

        attr_json :second_visible, :boolean, default: false
        attr_json :images, ImageBannerContent.to_type, array: true, default: -> { [ImageBannerContent.new, ImageBannerContent.new] }
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_cms_sections
#
#  id                   :bigint           not null, primary key
#  content              :text
#  destination          :string
#  fit                  :string
#  linked_resource_type :string           indexed => [linked_resource_id]
#  name                 :string           not null
#  position             :integer          indexed
#  properties           :jsonb            not null
#  settings             :text
#  type                 :string           indexed
#  visible              :boolean          default(TRUE), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  cms_page_id          :bigint           indexed
#  linked_resource_id   :bigint           indexed => [linked_resource_type]
#  listing_id           :bigint
#
# Indexes
#
#  index_spree_cms_sections_on_cms_page_id      (cms_page_id)
#  index_spree_cms_sections_on_linked_resource  (linked_resource_type,linked_resource_id)
#  index_spree_cms_sections_on_position         (position)
#  index_spree_cms_sections_on_type             (type)
#
