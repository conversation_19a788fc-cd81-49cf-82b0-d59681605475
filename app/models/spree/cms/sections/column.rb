# frozen_string_literal: true

module Spree
  module Cms
    module Sections
      class Column < Spree::CmsSection
        class ColumnContent
          include AttrJson::Model
          attr_json :image_url, :string, default: nil
          attr_json :header, :string, default: nil
          attr_json :header_visible, :boolean, default: true
          attr_json :content, :string, default: nil
          attr_json :content_visible, :boolean, default: true
          attr_json :link, :string, default: nil
        end

        attr_json :columns, ColumnContent.to_type, array: true, default: -> { Array.new(3) { ColumnContent.new } }
        attr_json :text_color, :string, default: nil
        attr_json :bg_color, :string, default: nil
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_cms_sections
#
#  id                   :bigint           not null, primary key
#  content              :text
#  destination          :string
#  fit                  :string
#  linked_resource_type :string           indexed => [linked_resource_id]
#  name                 :string           not null
#  position             :integer          indexed
#  properties           :jsonb            not null
#  settings             :text
#  type                 :string           indexed
#  visible              :boolean          default(TRUE), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  cms_page_id          :bigint           indexed
#  linked_resource_id   :bigint           indexed => [linked_resource_type]
#  listing_id           :bigint
#
# Indexes
#
#  index_spree_cms_sections_on_cms_page_id      (cms_page_id)
#  index_spree_cms_sections_on_linked_resource  (linked_resource_type,linked_resource_id)
#  index_spree_cms_sections_on_position         (position)
#  index_spree_cms_sections_on_type             (type)
#
