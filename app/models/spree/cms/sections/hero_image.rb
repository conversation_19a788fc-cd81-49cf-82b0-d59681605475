# frozen_string_literal: true

module Spree
  module Cms
    module Sections
      class HeroImage < Spree::CmsSection
        after_initialize :default_values

        store :content, accessors: [:title, :button_text], coder: JSON
        store :settings, accessors: [:gutters], coder: JSON

        LINKED_RESOURCE_TYPE = ["Spree::Taxon", "Spree::Product", "Spree::CmsPage"]

        def gutters?
          gutters == "Gutters"
        end

        def img_one_sm(dimensions = "600x250>")
          super
        end

        def img_one_md(dimensions = "1200x500>")
          super
        end

        def img_one_lg(dimensions = "2400x1000>")
          super
        end

        def img_one_xl(dimensions = "4800x2000>")
          super
        end

        private

        def default_values
          self.gutters ||= "No Gutters"
          self.fit ||= "Screen"
          self.linked_resource_type ||= "Spree::Taxon"
        end
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_cms_sections
#
#  id                   :bigint           not null, primary key
#  content              :text
#  destination          :string
#  fit                  :string
#  linked_resource_type :string           indexed => [linked_resource_id]
#  name                 :string           not null
#  position             :integer          indexed
#  properties           :jsonb            not null
#  settings             :text
#  type                 :string           indexed
#  visible              :boolean          default(TRUE), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  cms_page_id          :bigint           indexed
#  linked_resource_id   :bigint           indexed => [linked_resource_type]
#  listing_id           :bigint
#
# Indexes
#
#  index_spree_cms_sections_on_cms_page_id      (cms_page_id)
#  index_spree_cms_sections_on_linked_resource  (linked_resource_type,linked_resource_id)
#  index_spree_cms_sections_on_position         (position)
#  index_spree_cms_sections_on_type             (type)
#
