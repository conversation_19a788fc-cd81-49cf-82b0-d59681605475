# frozen_string_literal: true

module Spree
  module Cms
    module Sections
      class SideBySideImage < Spree::CmsSection
        after_initialize :default_values
        validate :reset_multiple_link_attributes

        LINKED_RESOURCE_TYPE = if Rails::VERSION::STRING < "6.0"
          ["Spree::Taxon"].freeze
        else
          ["Spree::Taxon", "Spree::Product"].freeze
        end

        store :content,
          accessors: [
            :link_type_one,
            :link_one,
            :title_one,
            :subtitle_one,
            :link_type_two,
            :link_two,
            :title_two,
            :subtitle_two,
          ],
          coder: JSON

        store :settings, accessors: [:gutters], coder: <PERSON><PERSON><PERSON>

        def gutters?
          gutters == "Gutters"
        end

        #
        # img_one sizing
        def img_one_md(dimensions = "387x250>")
          super
        end

        def img_one_lg(dimensions = "540x390>")
          super
        end

        def img_one_xl(dimensions = "1468x952>")
          super
        end

        #
        # img_two sizing
        def img_two_md(dimensions = "387x250>")
          super
        end

        def img_two_lg(dimensions = "734x476>")
          super
        end

        def img_two_xl(dimensions = "1468x952>")
          super
        end

        private

        def reset_multiple_link_attributes
          return if Rails::VERSION::STRING < "6.0"

          if link_type_one_changed?
            return if link_type_one_was.nil?

            self.link_one = nil
          end

          if link_type_two_changed?
            return if link_type_two_was.nil?

            self.link_two = nil
          end
        end

        def default_values
          self.gutters ||= "Gutters"
          self.fit ||= "Container"
          self.link_type_one ||= "Spree::Taxon"
          self.link_type_two ||= "Spree::Taxon"
        end
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_cms_sections
#
#  id                   :bigint           not null, primary key
#  content              :text
#  destination          :string
#  fit                  :string
#  linked_resource_type :string           indexed => [linked_resource_id]
#  name                 :string           not null
#  position             :integer          indexed
#  properties           :jsonb            not null
#  settings             :text
#  type                 :string           indexed
#  visible              :boolean          default(TRUE), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  cms_page_id          :bigint           indexed
#  linked_resource_id   :bigint           indexed => [linked_resource_type]
#  listing_id           :bigint
#
# Indexes
#
#  index_spree_cms_sections_on_cms_page_id      (cms_page_id)
#  index_spree_cms_sections_on_linked_resource  (linked_resource_type,linked_resource_id)
#  index_spree_cms_sections_on_position         (position)
#  index_spree_cms_sections_on_type             (type)
#
