# frozen_string_literal: true

module Spree
  module Cms
    module Pages
      class Homepage < Spree::CmsPage
        before_create :empty_slug
        after_save :empty_slug

        def sections?
          true
        end

        def seo_meta_description
          meta_description.presence
        end

        private

        def empty_slug
          self.slug = nil
        end
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_cms_pages
#
#  id               :bigint           not null, primary key
#  content          :text
#  deleted_at       :datetime         indexed, indexed => [slug, store_id]
#  locale           :string           indexed => [store_id, type]
#  meta_description :text
#  meta_title       :string
#  settings         :jsonb
#  slug             :string           indexed => [store_id, deleted_at]
#  title            :string           not null, indexed => [type, store_id]
#  type             :string           indexed => [store_id, locale], indexed => [title, store_id]
#  visible          :boolean          default(TRUE), indexed
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  store_id         :bigint           indexed => [slug, deleted_at], indexed, indexed => [locale, type], indexed => [title, type]
#
# Indexes
#
#  index_spree_cms_pages_on_deleted_at                        (deleted_at)
#  index_spree_cms_pages_on_slug_and_store_id_and_deleted_at  (slug,store_id,deleted_at) UNIQUE
#  index_spree_cms_pages_on_store_id                          (store_id)
#  index_spree_cms_pages_on_store_id_and_locale_and_type      (store_id,locale,type)
#  index_spree_cms_pages_on_title_and_type_and_store_id       (title,type,store_id)
#  index_spree_cms_pages_on_visible                           (visible)
#
