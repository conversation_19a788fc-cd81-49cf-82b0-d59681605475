# frozen_string_literal: true

module Spree
  class ShippingCategory < Spree::Base
    include UniqueName
    if defined?(Spree::Webhooks)
      include Spree::Webhooks::HasWebhooks
    end

    with_options inverse_of: :shipping_category do
      has_many :products
      has_many :shipping_method_categories
    end
    has_many :shipping_methods, through: :shipping_method_categories
  end
end

# == Schema Information
#
# Table name: spree_shipping_categories
#
#  id           :bigint           not null, primary key
#  name         :string           indexed
#  use_easypost :boolean          default(FALSE)
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  store_id     :bigint           indexed
#
# Indexes
#
#  index_spree_shipping_categories_on_name      (name)
#  index_spree_shipping_categories_on_store_id  (store_id)
#
