# frozen_string_literal: true

module Spree
  class WalmartOrderHook < ApplicationRecord
  end
end

# == Schema Information
#
# Table name: spree_walmart_order_hooks
#
#  id               :bigint           not null, primary key
#  error_details    :string
#  event_type       :string
#  hook_received_at :datetime
#  payload          :json
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  order_id         :string
#
