# frozen_string_literal: true

module Spree
  class DailySalesRecord < ApplicationRecord
    def self.batch_create_sale(records)
      chunk_size = 5000
      records.each_slice(chunk_size) do |chunk|
        ActiveRecord::Base.transaction do
          chunk.each do |key, value|
            create(product_id: key, date: value[:date], quantity: value[:quantity])
          end
        end
      end
    end

    def self.delete_records_before(date)
      # Remove all the records efore the date
      where("date < ?", date).delete_all
    end
  end
end

# == Schema Information
#
# Table name: spree_daily_sales_records
#
#  id         :bigint           not null, primary key
#  date       :date             indexed, indexed => [product_id]
#  quantity   :integer          default(0)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  product_id :bigint           indexed => [date]
#
# Indexes
#
#  index_spree_daily_sales_records_on_date                 (date)
#  index_spree_daily_sales_records_on_product_id_and_date  (product_id,date) UNIQUE
#
