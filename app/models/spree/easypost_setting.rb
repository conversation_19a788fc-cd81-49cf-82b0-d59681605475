# frozen_string_literal: true

module Spree
  class EasypostSetting < Base
    VALID_ENDORSEMENTS = [
      "ADDRESS_SERVICE_REQUESTED",
      "FORWARDING_SERVICE_REQUESTED",
      "RETURN_SERVICE_REQUESTED",
      "CHANGE_SERVICE_REQUESTED"
    ].freeze

    VALID_CONTENTS_TYPES = %w[
      documents
      gift
      merchandise
      returned_goods
      sample
      other
    ].freeze

    VALID_EEL_PFC_CODES = [
      "NOEEI 30.37(a)",
      "NOEEI 30.37(h)",
      "NOEEI 30.37(f)",
      "30.36",
      "30.37",
      "30.39"
    ].freeze

    before_validation :normalize_fields

    validates :endorsement_type,
              inclusion: {
                in: VALID_ENDORSEMENTS,
                message: "is not a valid endorsement type. Please select one from: #{VALID_ENDORSEMENTS}" 
              },
              presence: true
    validates :customs_contents_type,
              inclusion: {
                in: VALID_CONTENTS_TYPES,
                message: "is not a valid contents type. Please select one from: #{VALID_CONTENTS_TYPES}" 
              },
              allow_blank: true
    validates :customs_eel_pfc,
              inclusion: {
                in: VALID_EEL_PFC_CODES,
                message: "is not a valid EEL PFC code. Please select one from: #{VALID_EEL_PFC_CODES}"
              },
              allow_blank: true

    belongs_to :store
    has_many :order_packages, class_name: 'Spree::OrderPackage'
    scope :for_store, ->(store) { where(store_id: store.id) }

    private

    def normalize_fields
      self.endorsement_type = endorsement_type.to_s.strip.upcase
      self.customs_contents_type = customs_contents_type.to_s.strip.downcase
    end
  end
end

# == Schema Information
#
# Table name: spree_easypost_settings
#
#  id                        :bigint           not null, primary key
#  buy_postage_when_shipped  :boolean          default(FALSE)
#  carrier_accounts_returns  :string
#  carrier_accounts_shipping :string
#  customs_contents_type     :string
#  customs_eel_pfc           :string
#  customs_signer            :string
#  endorsement_type          :string
#  key                       :string
#  name                      :string
#  use_easypost_on_frontend  :boolean          default(FALSE)
#  validate_address          :boolean          default(FALSE)
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  returns_stock_location_id :integer
#  store_id                  :bigint           indexed
#
# Indexes
#
#  index_spree_easypost_settings_on_store_id  (store_id)
#
