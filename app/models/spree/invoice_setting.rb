# frozen_string_literal: true

module Spree
  class InvoiceSetting < ApplicationRecord
    has_one_attached :logo
    # has_one_attached :preview_pdf
    belongs_to :store
    belongs_to :country, class_name: "Spree::Country"
    belongs_to :state, class_name: "Spree::State", optional: true

    def logo_disk_path
      ActiveStorage::Blob.service.path_for(logo.key)
    end

    def state_name_text
      state_name.presence || state&.name
    end
  end
end

# == Schema Information
#
# Table name: spree_invoice_settings
#
#  id         :bigint           not null, primary key
#  address    :string
#  city       :string
#  message    :string
#  name       :string
#  state_name :string
#  zipcode    :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  country_id :bigint
#  state_id   :string
#  store_id   :bigint           indexed
#
# Indexes
#
#  index_spree_invoice_settings_on_store_id  (store_id)
#
