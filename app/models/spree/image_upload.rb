# frozen_string_literal: true

module Spree
  class ImageUpload < ApplicationRecord
    belongs_to :imageable, polymorphic: true
    belongs_to :user, class_name: Spree.user_class.to_s, optional: true
    belongs_to :image, class_name: "ActiveStorage::Attachment"
  end
end

# == Schema Information
#
# Table name: spree_image_uploads
#
#  id             :bigint           not null, primary key
#  imageable_type :string           not null, indexed => [imageable_id]
#  position       :integer          default(0)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  image_id       :integer
#  imageable_id   :bigint           not null, indexed => [imageable_type]
#  user_id        :bigint           not null, indexed
#
# Indexes
#
#  index_spree_image_uploads_on_imageable  (imageable_type,imageable_id)
#  index_spree_image_uploads_on_user_id    (user_id)
#
