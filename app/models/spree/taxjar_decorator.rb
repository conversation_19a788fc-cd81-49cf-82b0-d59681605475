# frozen_string_literal: true

module Spree
  module TaxjarDecorator
    def logger_enabled?
      return false if @order.store.taxjar_setting.nil?

      @order.store.taxjar_setting.taxjar_debug_enabled
    end

    def create_refund_transaction_for_order
      if has_nexus? && !reimbursement_present?
        api_params = refund_params
        SpreeTaxjar::Logger.log(
          __method__,
          {
            order: { id: @order.id, number: @order.number },
            reimbursement: { id: @reimbursement.id, number: @reimbursement.number },
            api_params: api_params,
          },
        ) if logger_enabled?
        api_response = @client.create_refund(api_params)
        SpreeTaxjar::Logger.log(
          __method__,
          {
            order: { id: @order.id, number: @order.number },
            reimbursement: { id: @reimbursement.id, number: @reimbursement.number },
            api_response: api_response,
          },
        ) if logger_enabled?
        api_response
      end
    end

    def create_transaction_for_order
      SpreeTaxjar::Logger.log(__method__, { order: { id: @order.id, number: @order.number } }) if logger_enabled?
      if has_nexus?
        api_params = transaction_parameters
        SpreeTaxjar::Logger.log(
          __method__,
          { order: { id: @order.id, number: @order.number }, api_params: api_params },
        ) if logger_enabled?
        api_response = @client.create_order(api_params)
        SpreeTaxjar::Logger.log(
          __method__,
          { order: { id: @order.id, number: @order.number }, api_response: api_response },
        ) if logger_enabled?
        api_response
      end
    end

    def delete_transaction_for_order
      SpreeTaxjar::Logger.log(__method__, { order: { id: @order.id, number: @order.number } }) if logger_enabled?
      if has_nexus?
        api_response = @client.delete_order(@order.number)
        SpreeTaxjar::Logger.log(
          __method__,
          { order: { id: @order.id, number: @order.number }, api_response: api_response },
        ) if logger_enabled?
        api_response
      end
    rescue ::Taxjar::Error::NotFound => e
      SpreeTaxjar::Logger.log(
        __method__,
        { order: { id: @order.id, number: @order.number }, error_msg: e.message },
      ) if logger_enabled?
    end

    def calculate_tax_for_shipment
      SpreeTaxjar::Logger.log(
        __method__,
        { shipment: { order: { id: @shipment.order.id, number: @shipment.order.number } } },
      ) if logger_enabled?
      if has_nexus?
        api_params = shipment_tax_params
        SpreeTaxjar::Logger.log(
          __method__,
          {
            shipment: {
              order: { id: @shipment.order.id, number: @shipment.order.number },
              api_params: api_params,
            },
          },
        ) if logger_enabled?
        api_response = @client.tax_for_order(api_params)
        SpreeTaxjar::Logger.log(
          __method__,
          {
            shipment: {
              order: { id: @shipment.order.id, number: @shipment.order.number },
              api_response: api_response,
            },
          },
        ) if logger_enabled?
        api_response.amount_to_collect
      else
        0
      end
    end

    def has_nexus?
      return false if tax_address.blank?
      return false if @order.store.taxjar_setting.nil?

      # Only calculate tax for Nevada state as per client requirement (can be changed later)
      return false unless tax_address_state_abbr == "NV"

      nexus_regions = @client.nexus_regions
      SpreeTaxjar::Logger.log(__method__, {
        order: { id: @order.id, number: @order.number },
        nexus_regions: nexus_regions,
        address: { state: tax_address_state_abbr, city: tax_address_city, zip: tax_address_zip },
      }) if logger_enabled?
      if nexus_regions.present?
        nexus_states(nexus_regions).include?(tax_address_state_abbr)
      else
        false
      end
    end

    def calculate_tax_for_order
      SpreeTaxjar::Logger.log(__method__, { order: { id: @order.id, number: @order.number } }) if logger_enabled?
      if has_nexus?
        api_params = tax_params
        SpreeTaxjar::Logger.log(
          __method__,
          { order: { id: @order.id, number: @order.number }, api_params: api_params },
        ) if logger_enabled?
        api_response = @client.tax_for_order(api_params)
        SpreeTaxjar::Logger.log(
          __method__,
          { order: { id: @order.id, number: @order.number }, api_response: api_response },
        ) if logger_enabled?
        api_response
      end
    end

    def tax_params
      {
        amount: @order.item_total,
        shipping: @order.shipment_total + adjustments_total(@order.shipment_adjustments),
        to_country: tax_address_country_iso,
        to_state: tax_address_state_abbr,
        to_zip: tax_address_zip,
        line_items: taxable_line_items_params,
      }
    end

    def client_params
      {
        api_key: @order.store.taxjar_setting.taxjar_api_key,
        api_url: api_url,
      }
    end

    def api_url
      if @order.store.taxjar_setting.taxjar_sandbox_environment_enabled
        ::Taxjar::API::Request::SANDBOX_API_URL
      else
        ::Taxjar::API::Request::DEFAULT_API_URL
      end
    end
  end
end

Spree::Taxjar.prepend(Spree::TaxjarDecorator)
