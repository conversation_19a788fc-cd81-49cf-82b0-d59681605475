# frozen_string_literal: true

module Spree
  class TrackerLog < ApplicationRecord
    belongs_to :trackable, polymorphic: true
  end
end

# == Schema Information
#
# Table name: spree_tracker_logs
#
#  id             :bigint           not null, primary key
#  details        :jsonb
#  trackable_type :string           not null, indexed => [trackable_id]
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  trackable_id   :bigint           not null, indexed => [trackable_type]
#
# Indexes
#
#  index_spree_tracker_logs_on_trackable  (trackable_type,trackable_id)
#
