# frozen_string_literal: true

module Spree
  class Digital < Spree::Base
    include Spree::Webhooks::HasWebhooks

    belongs_to :variant, optional: false

    has_many :digital_links,
      dependent: :destroy

    if Spree.private_storage_service_name
      has_one_attached :attachment, service: Spree.private_storage_service_name
    else
      has_one_attached :attachment
    end

    self.whitelisted_ransackable_attributes = ["variant_id"]

    validates :attachment, attached: true
  end
end

# == Schema Information
#
# Table name: spree_digitals
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  variant_id :bigint           indexed
#
# Indexes
#
#  index_spree_digitals_on_variant_id  (variant_id)
#
