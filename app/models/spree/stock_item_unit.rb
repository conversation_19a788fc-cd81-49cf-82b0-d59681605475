# frozen_string_literal: true

module Spree
  class StockItemUnit < Spree::Base
    include ::Spree::Admin::LineItemsExtHelper
    belongs_to :stock_item, optional: false
    belongs_to :shipment, optional: true, touch: true
    belongs_to :line_item, optional: true
    has_many :remarks, as: :remarkable, dependent: :destroy
    has_many :activity_logs, as: :loggable, class_name: 'Spree::ActivityLog'

    enum state: {
      stock: "stock",
      locked: "locked",
      shipped: "shipped",
      removed: "removed",
    }
    enum expiry_type: { fixed: 0, estimated: 1, na: 2 }

    scope :on_hand, -> { where(state: [:stock, :locked]) }
    scope :on_hold, -> { where(state: :locked) }

    counter_culture :stock_item,
      execute_after_commit: true,
      touch: true,
      column_name: proc { |unit| unit.state.to_sym.in?([:stock, :locked]) ? :count_on_hand : nil },
      delta_column: "pack_size",
      column_names: proc { { Spree::StockItemUnit.on_hand => :count_on_hand } }

    before_save :ensure_unique_item_number
    before_save :remove_leading_trailing_spaces
    after_create :touch_variant
    after_update :touch_variant
    after_save :add_remark
    after_update :check_line_items_ext_after_update

    self.whitelisted_ransackable_associations = ["stock_item"]
    self.whitelisted_ransackable_attributes = ["state", "number", "vendor_inventory_number"]
    self.whitelisted_ransackable_scopes = ["on_hand"]

    def barcode
      @barcode ||= ::Barby::Code128A.new(number)
    end

    def qr_code
      @qr_code ||= RQRCode::QRCode.new(number)
    end

    # Generates a potential unique item number based on the product and other attributes
    def generate_item_number(last_counter, counter)
      last_counter = last_counter.to_i + counter
      product = stock_item.product
      product_code_part = product&.abbreviation.present? ? product&.abbreviation&.upcase : ""
      sequential_number_part = format("%06d", (last_counter || 0) + 1)
      expiration_date_part = expiry_date.present? ? expiry_date.strftime("%y%m%d") : "000000"

      if product_code_part.present?
        "#{product_code_part}-#{sequential_number_part}-#{expiration_date_part}"
      else
        "#{sequential_number_part}-#{expiration_date_part}"
      end
    end

    # Ensures that the item number is unique before saving the record
    def ensure_unique_item_number
      return if number.present?

      product = stock_item.product

      # Use a loop to ensure unique number generation
      counter = 0
      loop do
        last_stock_item_unit = Spree::StockItemUnit.joins(stock_item: { variant: :product })
          .where(spree_products: { id: product.id })
          .lock(true)
          .order("spree_stock_item_units.created_at DESC")
          .first

        last_counter = last_stock_item_unit&.number&.split("-")&.dig(1)&.to_i

        self.number = generate_item_number(last_counter, counter)
        counter += 1
        # Break loop if the number is unique
        unless Spree::StockItemUnit.exists?(number: number)
          return
        end
      end
    end

    def remove_leading_trailing_spaces
      %w[
        remark
        vendor_receipt_number
        vendor_inventory_number
        vendor_inventory_cost
        pack_size
      ].each do |field|
        value = send(field) # Get the field value dynamically
        send("#{field}=", value.to_s.strip) if value.present?
      end
    end

    def add_remark
      if saved_change_to_remark? && remark.present?
        remarks.create!(content: remark)
      end
    end

    def show_expiry_date
      expiry_type != "na" ? expiry_date&.to_date : "N/A"
    end

    def expriy_field_class(field)
      field.include?(expiry_type) ? "form-control custom-input " : "d-none form-control custom-input"
    end

    def calendar_logo_class(type)
      expiry_type == type ? "customwidth" : "d-none customwidth"
    end

    def show_tax_category
      if self.stock_item.variant.is_master? # rubocop:disable Style/RedundantSelf
        self.stock_item.product.tax_category_id.present? ? self.stock_item.product.tax_category&.name : "---" # rubocop:disable Style/RedundantSelf
      else
        self.stock_item.variant.tax_category_id.present? ? self.stock_item.variant.tax_category&.name : "---" # rubocop:disable Style/RedundantSelf
      end
    end

    def show_state
      state_mappings = {
        "stock" => "In Stock",
        "locked" => "Confirmed in order",
        "shipped" => "Shipped",
        "removed" => "Removed",
      }
      state_mappings[self.state] # rubocop:disable Style/RedundantSelf
    end

    def touch_variant
      stock_item.variant.update(updated_at: Time.current)
    end

    def check_line_items_ext_after_update
      item = self.line_item
      return unless item.present?
      Rails.logger.info("=====entering StockItemUnit check_line_items_ext_after_update -2: #{item&.id}")
      order = item&.order
      return unless order.present?
      Rails.logger.info("=====entering StockItemUnit check_line_items_ext_after_update -1: #{order&.id}")
      if order&.line_items.present?
        Rails.logger.info("=====entering StockItemUnit check_line_items_ext_after_update 0: #{order&.id}")
        line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: item&.id)
        process_line_items_ext(line_items_ext, item, order) if line_items_ext
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_stock_item_units
#
#  id                      :bigint           not null, primary key
#  expiry_date             :datetime
#  expiry_type             :integer          default("fixed")
#  number                  :string
#  pack_size               :integer          default(1)
#  pack_size_counter       :string
#  printed                 :boolean          default(FALSE)
#  remark                  :string
#  state                   :string           default("stock")
#  vendor_inventory_cost   :string
#  vendor_inventory_number :string
#  vendor_receipt_date     :datetime
#  vendor_receipt_number   :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  line_item_id            :bigint           indexed
#  shipment_id             :bigint           indexed
#  stock_item_id           :bigint           indexed
#
# Indexes
#
#  index_spree_stock_item_units_on_line_item_id   (line_item_id)
#  index_spree_stock_item_units_on_shipment_id    (shipment_id)
#  index_spree_stock_item_units_on_stock_item_id  (stock_item_id)
#
