# frozen_string_literal: true

module Spree
  class SubscriberBehavior < ApplicationRecord
    belongs_to :subscriber
    
    def self.for_store(store)
      self
    end

    def self.json_api_permitted_attributes
      skipped_attributes = %w[id]
      if included_modules.include?(CollectiveIdea::Acts::NestedSet::Model)
        skipped_attributes.push('lft', 'rgt', 'depth')
      end
      column_names.reject { |c| skipped_attributes.include?(c.to_s) }
    end
  end
end

# == Schema Information
#
# Table name: spree_subscriber_behaviors
#
#  id            :bigint           not null, primary key
#  behavior      :string
#  detail        :string
#  record_type   :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  record_id     :bigint
#  subscriber_id :bigint           indexed
#
# Indexes
#
#  index_spree_subscriber_behaviors_on_subscriber_id  (subscriber_id)
#
#
