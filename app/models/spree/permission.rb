# frozen_string_literal: true

module Spree
  class Permission < ApplicationRecord
    belongs_to :role,
      class_name: "Spree::Role"
  end
end

# == Schema Information
#
# Table name: spree_permissions
#
#  id                                 :bigint           not null, primary key
#  access_reports                     :boolean
#  allow_product_delete               :boolean
#  create_and_edit_blogs              :boolean
#  create_and_edit_listing            :boolean
#  create_and_edit_order              :boolean
#  create_and_edit_product            :boolean
#  create_and_edit_store              :boolean
#  create_and_edit_users              :boolean
#  delete_blogs                       :boolean
#  delete_store                       :boolean
#  delete_users                       :boolean
#  edit_listing                       :boolean
#  general_settings                   :boolean
#  manage_blogs                       :boolean
#  manage_listings                    :boolean
#  manage_orders                      :boolean
#  manage_roles                       :boolean
#  manage_sale_channel                :boolean
#  manage_stock                       :boolean
#  manage_store_payment_configuration :boolean
#  manage_users                       :boolean
#  manage_webhooks                    :boolean
#  product_and_inventory_report       :boolean
#  publish_unpublish_store            :boolean
#  sale_and_finance_report            :boolean
#  send_email                         :boolean
#  show_dashboard                     :boolean
#  show_inventory                     :boolean
#  store_settings                     :boolean
#  view_activity_log                  :boolean
#  view_blogs                         :boolean
#  view_finance_and_sales             :boolean
#  view_product_cost_price            :boolean
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  role_id                            :bigint           indexed
#
# Indexes
#
#  index_spree_permissions_on_role_id  (role_id)
#
# Foreign Keys
#
#  fk_rails_...  (role_id => spree_roles.id)
#
