# frozen_string_literal: true

module Spree
  class MenuItem < Spree::Base
    include Spree::DisplayLink
    if defined?(Spree::Webhooks)
      include Spree::Webhooks::HasWebhooks
    end

    ITEM_TYPE = ["Link", "Container"]
    LINKED_RESOURCE_TYPE = ["Spree::Linkable::Uri", "Spree::Linkable::Homepage", "Spree::Product", "Spree::Taxon", "Spree::CmsPage"]

    acts_as_nested_set dependent: :destroy

    belongs_to :menu,
      touch: true

    has_one :icon,
      as: :viewable,
      class_name: "Spree::Icon",
      dependent: :destroy

    validates :name, presence: true
    validates :item_type, inclusion: { in: ITEM_TYPE }
    validates :linked_resource_type, inclusion: { in: LINKED_RESOURCE_TYPE }

    before_save :paremeterize_code
    before_create :ensure_item_belongs_to_root
    before_update :reset_link_attributes

    after_save :touch_ancestors_and_menu
    after_touch :touch_ancestors_and_menu

    accepts_nested_attributes_for :icon, reject_if: :all_blank

    def container?
      item_type == "Container"
    end

    def code?(item_code = nil)
      if item_code
        code == item_code
      else
        code.present?
      end
    end

    private

    def reset_link_attributes
      if linked_resource_type_changed? || item_type == "Container"
        self.linked_resource_id = nil
        self.destination = nil
        self.new_window = false

        self.linked_resource_type = "Spree::Linkable::Uri" if item_type == "Container"
      end
    end

    def ensure_item_belongs_to_root
      if menu.try(:root).present? && parent_id.nil?
        self.parent = menu.root

        store_new_parent
      end
    end

    def touch_ancestors_and_menu
      ancestors.update_all(updated_at: Time.current) # rubocop:disable Rails/SkipsModelValidations
      menu&.touch # rubocop:disable Rails/SkipsModelValidations
    end

    def paremeterize_code
      return if code.blank?

      self.code = code.parameterize
    end
  end
end

# == Schema Information
#
# Table name: spree_menu_items
#
#  id                   :bigint           not null, primary key
#  code                 :string           indexed
#  depth                :integer          default(0), not null, indexed
#  destination          :string
#  item_type            :string           indexed
#  lft                  :bigint           not null, indexed
#  linked_resource_type :string           default("Spree::Linkable::Uri"), indexed => [linked_resource_id]
#  name                 :string           not null
#  new_window           :boolean          default(FALSE)
#  rgt                  :bigint           not null, indexed
#  subtitle             :string
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  linked_resource_id   :bigint           indexed => [linked_resource_type]
#  listing_id           :bigint
#  menu_id              :bigint           indexed
#  parent_id            :bigint           indexed
#
# Indexes
#
#  index_spree_menu_items_on_code             (code)
#  index_spree_menu_items_on_depth            (depth)
#  index_spree_menu_items_on_item_type        (item_type)
#  index_spree_menu_items_on_lft              (lft)
#  index_spree_menu_items_on_linked_resource  (linked_resource_type,linked_resource_id)
#  index_spree_menu_items_on_menu_id          (menu_id)
#  index_spree_menu_items_on_parent_id        (parent_id)
#  index_spree_menu_items_on_rgt              (rgt)
#
