# frozen_string_literal: true

module Spree
  class EmailTemplateTag < ApplicationRecord
    belongs_to :email_template
    belongs_to :tag
  end
end

# == Schema Information
#
# Table name: spree_email_template_tags
#
#  email_template_id :bigint           indexed
#  tag_id            :bigint           indexed
#
# Indexes
#
#  index_spree_email_template_tags_on_email_template_id  (email_template_id)
#  index_spree_email_template_tags_on_tag_id             (tag_id)
#
