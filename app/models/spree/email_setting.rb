# frozen_string_literal: true

module Spree
  class EmailSetting < ApplicationRecord
    belongs_to :store

    enum email_setting_type: { store_email: 0, campaign_email: 1}

    validates :api_key, presence: true, on: :update, if: -> { campaign_email? }
  end
end

# == Schema Information
#
# Table name: spree_email_settings
#
#  id                 :bigint           not null, primary key
#  api_key            :string
#  email_bcc          :string
#  email_from         :string
#  email_setting_type :integer          default("store_email")
#  intercept_email    :string
#  mail_delivery      :boolean
#  smtp               :json
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  store_id           :bigint           indexed
#
# Indexes
#
#  index_spree_email_settings_on_store_id  (store_id)
#
