# frozen_string_literal: true

module Spree
  class SectionItem < Spree::Asset
    MAXIMUM_INTEGER = 2147483647
    has_one_attached :attachment

    validates :sort_number,
      uniqueness: {
        scope: [:store_id, :category, :display_on], if: :sort_number_present?,
      },
      length: { maximum: 10, too_long: "must be less than or equal to %{count} digits" },
      numericality: {
        allow_nil: true,
        only_integer: true,
        greater_than_or_equal_to: 0,
        less_than_or_equal_to: MAXIMUM_INTEGER,
      }

    validates :attachment, attached: true, content_type: %r{\Aimage/.*\z}

    default_scope { includes(attachment_attachment: :blob) }

    enum category: { hero: 0, featured: 1 }

    def display_on
      self[:display_on].presence || "All"
    end

    private

    def sort_number_present?
      sort_number.present?
    end
  end
end

# == Schema Information
#
# Table name: spree_assets
#
#  id                      :bigint           not null, primary key
#  alignment_position      :string           default("Center"), not null
#  alt                     :text
#  attachment_content_type :string
#  attachment_file_name    :string
#  attachment_file_size    :integer
#  attachment_height       :integer
#  attachment_updated_at   :datetime
#  attachment_width        :integer
#  category                :integer
#  display_on              :string
#  link                    :string
#  overlay_color           :string
#  overlay_link            :string
#  overlay_position        :string
#  overlay_text            :string
#  overlay_type            :string
#  position                :integer          indexed
#  private_metadata        :jsonb
#  public_metadata         :jsonb
#  sort_number             :integer
#  subtitle                :string
#  title                   :string
#  type                    :string(75)       indexed => [viewable_type]
#  viewable_type           :string           indexed => [type]
#  created_at              :datetime
#  updated_at              :datetime
#  store_id                :bigint           indexed
#  viewable_id             :bigint           indexed
#
# Indexes
#
#  index_assets_on_viewable_id             (viewable_id)
#  index_assets_on_viewable_type_and_type  (viewable_type,type)
#  index_spree_assets_on_position          (position)
#  index_spree_assets_on_store_id          (store_id)
#
