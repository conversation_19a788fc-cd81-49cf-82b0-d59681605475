# frozen_string_literal: true

module Spree
  class GalleryItem < ApplicationRecord
    belongs_to :store
    
    has_one_attached :file
  end
end

# == Schema Information
#
# Table name: spree_gallery_items
#
#  id          :bigint           not null, primary key
#  title       :string
#  uploaded_by :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  store_id    :bigint           indexed
#
# Indexes
#
#  index_spree_gallery_items_on_store_id  (store_id)
#
