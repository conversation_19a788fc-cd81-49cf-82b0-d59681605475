# frozen_string_literal: true

module Spree
  class TaxjarSetting < ::Spree::Base
    belongs_to :store
  end
end

# == Schema Information
#
# Table name: spree_taxjar_settings
#
#  id                                 :bigint           not null, primary key
#  taxjar_api_key                     :string
#  taxjar_debug_enabled               :boolean          default(FALSE)
#  taxjar_enabled                     :boolean          default(FALSE)
#  taxjar_sandbox_environment_enabled :boolean          default(FALSE)
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  store_id                           :bigint           indexed
#
# Indexes
#
#  index_spree_taxjar_settings_on_store_id  (store_id)
#
