# frozen_string_literal: true

module Spree
  class ImportLog < ApplicationRecord
    belongs_to :store
    enum log_type: { job: 0, webhook: 1 }, _default: 0
  end
end

# == Schema Information
#
# Table name: spree_import_logs
#
#  id                :bigint           not null, primary key
#  error_details     :json
#  error_row_count   :integer
#  initiated_for     :string
#  log_type          :integer
#  started_by_email  :string
#  state             :string
#  success_row_count :integer
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  store_id          :integer
#
