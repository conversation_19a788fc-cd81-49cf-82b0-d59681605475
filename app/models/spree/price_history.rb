# frozen_string_literal: true

module Spree
  class PriceHistory < Spree::Base
    belongs_to :variant
  end
end

# == Schema Information
#
# Table name: spree_price_histories
#
#  id              :bigint           not null, primary key
#  amount          :decimal(10, 2)
#  currency        :string
#  kind            :string
#  previous_amount :decimal(10, 2)
#  strategy        :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  variant_id      :integer          not null, indexed
#
# Indexes
#
#  index_spree_price_histories_on_variant_id  (variant_id)
#
