# frozen_string_literal: true

module Spree
  class Tag < ApplicationRecord
    belongs_to :store

    has_many :email_template_tags,
      dependent: :delete_all
    has_many :email_templates,
      through: :email_template_tags

    scope :for_store, ->(store) { where(store_id: store.id) }
    scope :shipping_tag, -> { where(name: Spree.t(:shipping_notification_email)) }

    # validates :name, uniqueness: { scope: :store_id }
    validates :name, presence: true

    def self.shipping_emails_without_default
      shipping_tag.first&.email_templates&.without_shipping_default&.active
    end
  end
end

# == Schema Information
#
# Table name: spree_tags
#
#  id          :bigint           not null, primary key
#  description :string
#  name        :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  store_id    :bigint           indexed
#
# Indexes
#
#  index_spree_tags_on_store_id  (store_id)
#
