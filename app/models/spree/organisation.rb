# frozen_string_literal: true
require 'aws-sdk-route53'
require 'uri'

module Spree
  class Organisation < ApplicationRecord
    has_many :organisations_admin_users, class_name: 'Spree::OrganisationsAdminUser'
    has_many :admin_users, through: :organisations_admin_users,
             class_name: 'Spree::AdminUser'

    before_validation :generate_unique_subdomain, on: :create, unless: -> { subdomain.present? }
    before_validation :normalize_subdomain, if: -> { subdomain.present? }
    before_validation :ensure_name, if: -> { name.blank? }

    validates :name, presence: true
    validates :admin_email, :subdomain, presence: true, uniqueness: true
    validate :verify_subdomain, if: -> { subdomain.present? }
    validate :verify_custom_domain, if: -> { custom_domain.present? }

    before_create :set_url
    after_commit  :create_tenant, on: :create
    after_commit  :remove_tenant, on: :destroy

    def set_url
      self.url ||= build_url
    end

    private

    def ensure_name
      self.name = subdomain.titleize
    end

    def normalize_subdomain
      self.subdomain = subdomain.to_s.strip.downcase
    end

    def verify_subdomain
      errors.add(:subdomain, "allows only lowercase letters and numbers") unless subdomain.match?(/\A[a-z0-9]+\z/)
    end

    def verify_custom_domain
      errors.add(:custom_domain, "is not valid") unless extract_domain(custom_domain)
    end

    def generate_unique_subdomain
      loop do
        self.subdomain = SecureRandom.alphanumeric(8).downcase
        break unless self.class.exists?(subdomain: subdomain)
      end
    end

    def build_url
      scheme = Rails.application.credentials.dig(:host, :scheme)
      if Rails.env.staging?
        domain = Rails.application.credentials.dig(:host, :staging_domain)
      else
        domain = Rails.application.credentials.dig(:host, :domain)
      end
      "#{scheme}://#{subdomain}.#{domain}"
    end

    def create_tenant
      Rails.logger.debug("Creating tenant: #{subdomain}")
      Apartment::Tenant.create(subdomain)

      Rails.logger.debug("Seeding tenant: #{subdomain}")
      seed_tenant

      Rails.logger.debug("Updating whitelist for custom domain")
      update_whitelist_custom_domain

      Rails.logger.debug("Whitelisting domain in Route 53")
      whitelist_domain if custom_domain.present? && !Rails.env.test?

      Rails.logger.debug("Whitelisting subdomain in Cloudflare")
      whitelist_subdomain if Rails.env.staging?
    rescue StandardError => e
      Rails.logger.error("Error during tenant creation: #{e.message}")
    end

    def remove_tenant
      Rails.logger.debug("Removing tenant: #{subdomain}")
      Apartment::Tenant.drop(subdomain)
      update_whitelist_custom_domain
      cloudflare_service.delete_dns_record(extract_host(url)) if Rails.env.staging?
    rescue StandardError => e
      Rails.logger.error("Error during tenant removal: #{e.message}")
    end

    def seed_tenant
      AxelSpree::Spree::Seeds::Tenant.new(self).call
    rescue StandardError => e
      Rails.logger.error("Error during tenant seeding: #{e.message}")
    end

    def update_whitelist_custom_domain
      Rails.application.config.hosts = self.class.allowed_hosts
    end

    def whitelist_domain
      Rails.logger.debug("Starting Domain Whitelisting: #{custom_domain.presence || url}")
      aws_config = Rails.application.credentials.dig(:amazon, :aws)

      unless aws_config&.values_at(:region, :access_key_id, :secret_access_key).all?
        return Rails.logger.warn("AWS credentials missing")
      end

      domain_name = "#{extract_host(custom_domain.presence || url)}."
      record_value = extract_host(url)
      return Rails.logger.error("Domain name missing") if domain_name.blank?

      Rails.logger.debug("Starting Route 53 domain whitelisting for: #{custom_domain.presence || url}")

      begin
        route53_service = ::Aws::Route53Service.new(
          access_key: aws_config[:access_key_id],
          secret_key: aws_config[:secret_access_key],
          aws_region: aws_config[:region]
        )

        Rails.logger.debug("Started domain whitelisting service")

        response = route53_service.add_route53_record(aws_config[:zone_id], domain_name, record_value)

        Rails.logger.debug("Domain whitelisting service response: #{response}")

        Rails.logger.info "Domain whitelisting #{subdomain} on Route 53 in in progress!"
      rescue StandardError => e
        Rails.logger.error "Error while whitelisting domain '#{url}': #{e.message}"
        update(flag: "Failed to whitelist. Please allow the domain in DNS first.")
      end
    end

    def cloudflare_service
      cloudflare_config = Rails.application.credentials.dig(:cloudflare, :dns)

      unless cloudflare_config&.values_at(:email, :auth_key, :zone_id).all?
        return Rails.logger.warn("Cloudflare credentials missing")
      end

      ::Dns::CloudflareService.new(
        email: cloudflare_config[:email],
        auth_key: cloudflare_config[:auth_key],
        zone_id: cloudflare_config[:zone_id]
      )
    end

    def whitelist_subdomain
      begin
        Rails.logger.debug("Started subdomain whitelisting service")

        response = cloudflare_service.create_dns_record(subdomain)

        Rails.logger.debug("Subdomain whitelisting service response: #{response}")
      rescue StandardError => e
        Rails.logger.error "Error while whitelisting subdomain '#{subdomain}': #{e.message}"
        update(flag: "Failed to whitelist. Please allow the subdomain in DNS first.")
      end
    end

    def self.allowed_hosts
      pluck(:custom_domain, :url).filter_map do |custom_domain, url|
        domain_name = extract_domain_from_url(custom_domain.presence || url)
        domain_name.present? ? ".#{domain_name}" : nil
      end.uniq.compact
    rescue => e
      Rails.logger.error("Error generating allowed_hosts: #{e.message}")
      []
    end

    def self.extract_host_from_url(url)
      URI.parse(url).host
    rescue URI::InvalidURIError
      Rails.logger.warn("Invalid host URL: #{url}")
      nil
    end

    def self.extract_domain_from_url(url)
      extract_host_from_url(url)&.then { |host| PublicSuffix.domain(host) }
    rescue StandardError => e
      Rails.logger.warn("Error extracting domain from URL: #{url} - #{e.message}")
      nil
    end

    def extract_host(url)
      self.class.extract_host_from_url(url)
    end

    def extract_domain(url)
      self.class.extract_domain_from_url(url)
    end
  end
end

# == Schema Information
#
# Table name: public.spree_organisations
#
#  id                 :bigint           not null, primary key
#  admin_email        :string
#  custom_domain      :string
#  flag               :string
#  name               :string
#  subdomain          :string
#  two_factor_enabled :boolean          default(FALSE)
#  url                :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#
