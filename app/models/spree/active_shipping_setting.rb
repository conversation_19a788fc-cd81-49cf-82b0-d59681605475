# frozen_string_literal: true

module Spree
  class ActiveShippingSetting < ApplicationRecord
    belongs_to :store
  end
end

# == Schema Information
#
# Table name: spree_active_shipping_settings
#
#  id               :bigint           not null, primary key
#  australia_post   :jsonb
#  canada_post      :jsonb
#  fedex            :jsonb
#  general_settings :jsonb
#  ups              :jsonb
#  usps             :jsonb
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  store_id         :bigint           indexed
#
# Indexes
#
#  index_spree_active_shipping_settings_on_store_id  (store_id)
#
