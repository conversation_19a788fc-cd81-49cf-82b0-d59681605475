# frozen_string_literal: true

module Spree
  class ElasticsearchSyncRecord < ApplicationRecord
    enum op: { op_update: 0, op_delete: 1 }, _default: :op_update

    ransacker :op, formatter: proc { |it| ops[it] }

    def self.find_or_create_by_obj(obj, op = :op_update)
      return if obj.blank?

      record = find_or_initialize_by({ record_type: obj.class.name, record_id: obj.id })
      record.op = op
      record.save!
      record
    end

    def self.find_or_create_by_objs(objs, op = :op_update)
      return if objs.blank?

      records = []
      objs.map do |obj|
        records << find_or_create_by_obj(obj, op)
      end
      records
    end

    # obj can be an array or a single object
    def self.find_or_create(obj, op = :op_update)
      return if obj.blank?

      obj.is_a?(Array) ? find_or_create_by_objs(obj, op) : find_or_create_by_obj(obj, op)
    end

    def target_record
      record_type.constantize.find(record_id)
    end
  end
end

# == Schema Information
#
# Table name: spree_elasticsearch_sync_records
#
#  id          :bigint           not null, primary key
#  op          :integer          not null
#  record_type :string           not null, indexed
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  record_id   :bigint           not null, indexed
#
# Indexes
#
#  index_spree_elasticsearch_sync_records_on_record_id    (record_id)
#  index_spree_elasticsearch_sync_records_on_record_type  (record_type)
#
