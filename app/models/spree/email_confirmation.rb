# frozen_string_literal: true

module Spree
  class EmailConfirmation < ApplicationRecord
    VALID_EMAIL_REGEX = /\A[a-z0-9]+([._%+-]?[a-z0-9]+)*@[a-z0-9]+(-[a-z0-9]+)*(\.[a-z]{2,})+\z/i

    before_validation :normalize_email
    before_create :generate_code_and_expiry

    validates :email,
      presence: true,
      uniqueness: { case_sensitive: false },
      format: { with: VALID_EMAIL_REGEX, message: "must be a valid email address" }

    def regenerate_code!
      update!(
        otp_code: self.class.generate_code,
        expires_at: 5.minutes.from_now,
        confirmed: false,
      )
    end

    def self.generate_code
      rand(100000..999999).to_s
    end

    private

    def normalize_email
      self.email = email.to_s.strip.downcase
    end

    def generate_code_and_expiry
      self.otp_code = self.class.generate_code
      self.expires_at = 5.minutes.from_now
    end
  end
end

# == Schema Information
#
# Table name: public.spree_email_confirmations
#
#  id         :bigint           not null, primary key
#  confirmed  :boolean          default(FALSE)
#  email      :string
#  expires_at :datetime
#  otp_code   :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
