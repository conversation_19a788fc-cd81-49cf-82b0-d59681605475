# frozen_string_literal: true

module Spree
  class WishedItem < ::Spree::Base
    extend DisplayMoney

    money_methods :total, :price

    belongs_to :variant, class_name: "Spree::<PERSON>arian<PERSON>"
    belongs_to :wishlist, class_name: "Spree::Wishlist"
    belongs_to :listing, class_name: "Spree::Listing"
    has_one :product, class_name: "Spree::Product", through: :variant

    validates :variant, uniqueness: { scope: [:wishlist, :listing_id] }
    validates :quantity, numericality: { only_integer: true, greater_than: 0 }

    def price(currency)
      variant.amount_in(currency[:currency])
    end

    def total(currency)
      variant_price = variant.amount_in(currency[:currency])

      if variant_price.nil?
        variant_price
      else
        quantity * variant_price
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_wished_items
#
#  id          :bigint           not null, primary key
#  quantity    :integer          default(1), not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  listing_id  :bigint           indexed => [variant_id, wishlist_id]
#  variant_id  :bigint           indexed, indexed => [wishlist_id, listing_id]
#  wishlist_id :bigint           indexed => [variant_id, listing_id], indexed
#
# Indexes
#
#  index_spree_wished_items_on_variant_id                (variant_id)
#  index_spree_wished_items_on_variant_wishlist_listing  (variant_id,wishlist_id,listing_id)
#  index_spree_wished_items_on_wishlist_id               (wishlist_id)
#
