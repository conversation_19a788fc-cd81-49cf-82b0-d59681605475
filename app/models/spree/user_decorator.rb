# frozen_string_literal: true

module Spree
  module UserDecorator
    include ::Spree::BaseHelper

    def self.prepended(base)
      base.attr_accessor(:skip_sync_password)
      # rubocop:disable Rails/HasManyOrHasOneDependent
      base.has_many(:subscriptions)
      # rubocop:enable Rails/HasManyOrHasOneDependent
      base.after_update_commit(:send_welcome_email, if: :saved_change_to_confirmed_at?)

      unless base.defined_enums.key?("provider")
        base.enum(provider: { email: 0, google: 1, apple: 2 })
      end

      base.after_create(:sync_with_public_admin_user)
    end

    def send_welcome_email
      return unless confirmed?

      # TODO: Now use default store when create email
      ::Spree::UserMailer.welcome_email(id, ::Spree::Store.default.id).deliver_later
    end

    def confirmed?
      confirmed_at.present?
    end

    def tenant_name
      Apartment::Tenant.current
    end

    def name
      full_name = [first_name, last_name].compact.join(" ")
      full_name.presence
    end

    def sync_with_public_admin_user
      current_subdomain = Apartment::Tenant.current

      Apartment::Tenant.switch("public") do
        organisation = Spree::Organisation.find_by(subdomain: current_subdomain)
        return if organisation.nil?

        admin_user = Spree::AdminUser.find_or_create_by(email: email)

        unless admin_user.organisations.exists?(organisation.id)
          admin_user.organisations << organisation
        end
      end
    end
  end
end

Spree::User.prepend(Spree::UserDecorator)
