# frozen_string_literal: true

module Spree
  class CmsSection < Base
    include Spree::DisplayLink
    include AttrJson::Record

    attr_json_config default_container_attribute: "properties"

    acts_as_list scope: :cms_page
    belongs_to :cms_page, touch: true

    validate :reset_link_attributes

    has_one :image_one, class_name: "Spree::CmsSectionImageOne", dependent: :destroy, as: :viewable
    accepts_nested_attributes_for :image_one, reject_if: :all_blank

    has_one :image_two, class_name: "Spree::CmsSectionImageTwo", dependent: :destroy, as: :viewable
    accepts_nested_attributes_for :image_two, reject_if: :all_blank

    has_one :image_three, class_name: "Spree::CmsSectionImageThree", dependent: :destroy, as: :viewable
    accepts_nested_attributes_for :image_three, reject_if: :all_blank

    Spree::CmsSectionImage::IMAGE_COUNT.each do |count|
      Spree::CmsSectionImage::IMAGE_SIZE.each do |size|
        define_method(:"img_#{count}_#{size}") do |dimensions = nil|
          image = send(:"image_#{count}")&.attachment
          return if !image&.attached? || dimensions.nil?

          image.variant(resize_to_limit: dimensions.split("x").map(&:to_i))
        end
      end
    end

    default_scope { order(position: :asc) }

    validates :name, :type, presence: true

    LINKED_RESOURCE_TYPE = []

    TYPES = [
      "Spree::Cms::Sections::HeroImage",
      "Spree::Cms::Sections::FeaturedArticle",
      "Spree::Cms::Sections::ProductCarousel",
      "Spree::Cms::Sections::ImageGallery",
      "Spree::Cms::Sections::SideBySideImages",
      "Spree::Cms::Sections::RichTextContent",
    ]

    def boundaries
      ["Container", "Screen"]
    end

    def css_classes
      ["row", "section-row"].compact
    end

    def gutters_sizes
      ["Gutters", "No Gutters"]
    end

    def fullscreen?
      fit == "Screen"
    end

    private

    def reset_link_attributes
      if linked_resource_type_changed?
        return if linked_resource_id_was.nil?

        self.linked_resource_id = nil
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_cms_sections
#
#  id                   :bigint           not null, primary key
#  content              :text
#  destination          :string
#  fit                  :string
#  linked_resource_type :string           indexed => [linked_resource_id]
#  name                 :string           not null
#  position             :integer          indexed
#  properties           :jsonb            not null
#  settings             :text
#  type                 :string           indexed
#  visible              :boolean          default(TRUE), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  cms_page_id          :bigint           indexed
#  linked_resource_id   :bigint           indexed => [linked_resource_type]
#  listing_id           :bigint
#
# Indexes
#
#  index_spree_cms_sections_on_cms_page_id      (cms_page_id)
#  index_spree_cms_sections_on_linked_resource  (linked_resource_type,linked_resource_id)
#  index_spree_cms_sections_on_position         (position)
#  index_spree_cms_sections_on_type             (type)
#
