# frozen_string_literal: true

module Spree
  class Calculator
    module TaxjarCalculatorDecorator
      # SpreeTaxjar::Logger = ::TaxjarHelper::TaxjarLog.new(STDOUT)
      # SpreeTaxjar::Logger.logger.extend(ActiveSupport::Logger.broadcast(Rails.logger))

      class << self
        def prepended(base)
          base.const_set(:CACHE_EXPIRATION_DURATION, 10.minutes) unless base.const_defined?(:CACHE_EXPIRATION_DURATION)
        end
      end

      def logger_enabled?(store)
        return false unless store.taxjar_setting

        store.taxjar_setting.taxjar_debug_enabled
      end

      def compute_line_item(item)
        return 0 if item.order.store.taxjar_setting.nil?

        # Only calculate tax for Nevada state as per client requirement (can be changed later)
        return 0 unless item.order&.ship_address&.state_abbr == "NV"

        SpreeTaxjar::Logger.log(
          __method__,
          { line_item: { order: { id: item.order.id, number: item.order.number } } },
        ) if logger_enabled?(item.order.store)
        return 0 unless item.order.store.taxjar_setting.taxjar_enabled

        if rate.included_in_price
          0
        else
          round_to_two_places(tax_for_item(item))
        end
      end

      def compute_shipment(shipment)
        return 0 if shipment.order.store.taxjar_setting.nil?

        SpreeTaxjar::Logger.log(
          __method__,
          {
            shipment: {
              order: {
                id: shipment.order.id,
                number: shipment.order.number,
              },
            },
          },
        ) if logger_enabled?(shipment.order.store)
        return 0 unless shipment.order.store.taxjar_setting.taxjar_enabled

        tax_for_shipment(shipment)
      end

      def compute_shipping_rate(shipping_rate)
        0
        # return 0 unless !item.order.store.taxjar_setting.nil?
        # return 0 unless shipping_rate.order.store.taxjar_setting.taxjar_enabled
        # if rate.included_in_price
        #  raise Spree.t(:shipping_rate_exception_message)
        # else
        #  0
        # end
      end

      def tax_for_shipment(shipment)
        order = shipment.order
        return 0 unless (tax_address = order.tax_address)

        return 0 if order.store.taxjar_setting.nil?

        rails_cache_key = cache_key(order, shipment, tax_address)

        SpreeTaxjar::Logger.log(
          __method__,
          {
            shipment: { order: { id: shipment.order.id, number: shipment.order.number } },
            cache_key: rails_cache_key,
          },
        ) if logger_enabled?(order.store)

        Rails.cache.fetch(rails_cache_key, expires_in: self.class.const_get(:CACHE_EXPIRATION_DURATION)) do
          ::Spree::Taxjar.new(order, nil, shipment).calculate_tax_for_shipment
        end
      end

      def tax_for_item(item)
        order = item.order
        return 0 unless (tax_address = order.tax_address)

        return 0 if order.store.taxjar_setting.nil?

        rails_cache_key = cache_key(order, item, tax_address)

        SpreeTaxjar::Logger.log(
          __method__,
          {
            line_item: { order: { id: item.order.id, number: item.order.number } },
            cache_key: rails_cache_key,
          },
        ) if logger_enabled?(order.store)

        ## Test when caching enabled that only 1 API call is sent for an order
        ## should avoid N calls for N line_items
        Rails.cache.fetch(rails_cache_key, expires_in: self.class.const_get(:CACHE_EXPIRATION_DURATION)) do
          taxjar_response = ::Spree::Taxjar.new(order).calculate_tax_for_order
          return 0 unless taxjar_response

          tax_for_current_item = cache_response(taxjar_response, order, tax_address, item)
          tax_for_current_item
        end
      end

      def cache_response(taxjar_response, order, address, item = nil)
        return unless order.tax_address

        SpreeTaxjar::Logger.log(
          __method__,
          {
            order: { id: order.id, number: order.number },
            taxjar_api_advanced_res: taxjar_response,
          },
        ) if logger_enabled?(order.store)
        SpreeTaxjar::Logger.log(
          __method__,
          {
            order: { id: order.id, number: order.number },
            taxjar_api_advanced_res: taxjar_response.breakdown.line_items,
          },
        ) if logger_enabled?(order.store)
        ## res is set to faciliate testing as to return computed result from API
        ## for given line_item
        ## better to use Rails.cache.fetch for order and wrapping lookup based on line_item id
        res = nil
        taxjar_response.breakdown.line_items.each do |line_item|
          item_from_db = ::Spree::LineItem.includes(:adjustments).find_by(id: line_item.id)
          if item && item_from_db.id == item.id
            res = line_item.tax_collectable
          end
          Rails.cache.write(
            cache_key(order, item_from_db, address),
            line_item.tax_collectable,
            expires_in: self.class.const_get(:CACHE_EXPIRATION_DURATION),
          )
        end
        res
      end
    end
  end
end

::Spree::Calculator::TaxjarCalculator.prepend(::Spree::Calculator::TaxjarCalculatorDecorator)
