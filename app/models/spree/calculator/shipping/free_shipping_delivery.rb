# frozen_string_literal: true

require_dependency "spree/shipping_calculator"

module Spree
  class Calculator
    module Shipping
      class FreeShippingDelivery < ShippingCalculator
        preference :minimum_item_total, :decimal, default: 0
        preference :flat_rate, :decimal, default: 0

        def self.description
          Spree.t(:shipping_free_shipping_over_item_total)
        end

        def compute_package(package)
          if package.order.item_total >= preferred_minimum_item_total
            return 0
          end

          preferred_flat_rate
        end
      end
    end
  end
end

# == Schema Information
#
# Table name: spree_calculators
#
#  id              :bigint           not null, primary key, indexed => [type]
#  calculable_type :string           indexed => [calculable_id]
#  deleted_at      :datetime         indexed
#  preferences     :text
#  type            :string           indexed => [id]
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  calculable_id   :bigint           indexed => [calculable_type]
#
# Indexes
#
#  index_spree_calculators_on_calculable_id_and_calculable_type  (calculable_id,calculable_type)
#  index_spree_calculators_on_deleted_at                         (deleted_at)
#  index_spree_calculators_on_id_and_type                        (id,type)
#
