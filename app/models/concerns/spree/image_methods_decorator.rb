# frozen_string_literal: true

module Spree
  module ImageMethodsDecorator
    def generate_url(size:, gravity: "centre", quality: 80, background: [0, 0, 0])
      return if size.blank?

      size = size.gsub(/\s+/, "")
      return unless /(\d+)x(\d+)/.match?(size)

      width, height = size.split("x").map(&:to_i)
      gravity = translate_gravity_for_mini_magick(gravity)

      polymorphic_path(
        attachment.variant(
          resize_and_pad: [width, height, { gravity: gravity }],
          saver: { quality: quality },
        ),
        only_path: true,
      )
    end

    def original_url
      polymorphic_path(attachment, only_path: true)
    end
  end
end

Spree::ImageMethods.prepend(Spree::ImageMethodsDecorator)
