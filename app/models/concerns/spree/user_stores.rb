# frozen_string_literal: true

module Spree
  module UserStores
    extend ActiveSupport::Concern

    included do
      has_many :user_stores,
        class_name: "::Spree::StoreUser",
        dependent: :destroy

      # Ovsolete
      has_many :spree_stores,
        through: :user_stores,
        class_name: "::Spree::Store",
        source: :store

      has_many :stores,
        through: :user_stores,
        class_name: "::Spree::Store",
        source: :store

      scope :for_store, ->(store) { joins(:stores).where(Store.table_name => { id: store.id }) }
    end
  end
end
