# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module DefaultPriceDecorator
      extend ActiveSupport::Concern

      def self.prepended(base)
        base.delegate(:display_compare_to_price, :compare_to_price, :compare_to_price=, to: :find_or_build_default_price)
      end

      included do
        before_save(:check_master_price_changed)
      end

      def check_master_price_changed
        self.updated_at = Time.current if default_price_changed?
      end
    end
  end
end

Spree::DefaultPrice.prepend(AxelSpree::Spree::DefaultPriceDecorator)
