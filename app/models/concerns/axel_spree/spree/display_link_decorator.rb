# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module DisplayLinkDecorator
      def base_taxon_link(permalink)
        return if permalink.blank?

        "/#{::Spree::Config[:storefront_taxons_path]}/#{permalink}"
      end

      def base_link(resource_type, resource, listing = nil)
        case resource_type
        when "Spree::Taxon"
          base_taxon_link(resource&.permalink)
        when "Spree::Product"
          return if resource&.slug.blank?
          if listing.present?
            [
              "/#{::Spree::Config[:storefront_products_path]}",
              "/#{resource.variants.first&.id || resource.master.id}",
              "/#{listing.id}",
              "/#{resource.slug}",
            ].join("")
          elsif resource.last_active_listing.present?
            [
              "/#{::Spree::Config[:storefront_products_path]}",
              "/#{resource.variants.first&.id || resource.master.id}",
              "/#{resource.last_active_listing.id}",
              "/#{resource.slug}",
            ].join("")
          else
            [
              "/#{::Spree::Config[:storefront_products_path]}",
              "/#{resource.variants.first&.id || resource.master.id}",
              "/#{resource.slug}",
            ].join("")
          end
        when "Spree::CmsPage"
          return if resource&.slug.blank?

          "/#{::Spree::Config[:storefront_pages_path]}/#{resource.slug}"
        when "Home Page"
          "/"
        when "URL"
          destination
        end
      end

      def link
        base_link(linked_resource_type, linked_resource)
      end
    end
  end
end

Spree::DisplayLink.prepend(AxelSpree::Spree::DisplayLinkDecorator)
