# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Elasticsearch
    class Base
      include AxelSpree::Elasticsearch::Search::Statement

      def initialize
        @purpose = AxelSpree::Elasticsearch::Search::Purpose.new # default is "frontend"
        @query_params = {}
        @query = { match_all: {} }
        @order = {}
        @mode = AxelSpree::Elasticsearch::Search::Mode.new # default is "standard"
        @from = 0
        @size = 25
        @store_id = 1
        @indludes_files = []
      end

      def st_time(field)
        ::AxelSpree::Elasticsearch::Search::StatementTime.new(field)
      end

      def index_nested(klass)
        obj = klass.new
        {
          "type": "nested",
          "properties": obj.index_mapping_properties,
        }
      end

      def index_mapping_properties
        ret = index_mapping
        ret.dig(:mappings, :properties)
      end

      def mode(value)
        @mode = AxelSpree::Elasticsearch::Search::Mode.new(value)
        self
      end

      def purpose(value)
        unless value.is_a?(AxelSpree::Elasticsearch::Purpose)
          raise ArgumentError, "Invalid purpose: #{value}"
        end

        @purpose = value
        self
      end

      def query_params(value)
        # make sure value is a hash, otherwise rais an ArgumentError
        unless value.is_a?(Hash)
          raise ArgumentError, "Invalid query params: #{value}"
        end

        @query_params = value
      end

      def from(value)
        # make sure value is an integer, otherwise rais an ArgumentError
        unless value.is_a?(Integer)
          raise ArgumentError, "Invalid from: #{value}"
        end

        @from = value
        self
      end

      def size(value)
        # make sure value is an integer, otherwise rais an ArgumentError
        unless value.is_a?(Integer)
          raise ArgumentError, "Invalid size: #{value}"
        end

        @size = value
        self
      end

      def order(value)
        # make sure value is an Hash, otherwise rais an ArgumentError
        unless value.is_a?(Hash)
          raise ArgumentError, "Invalid order: #{value}"
        end

        @order = value
        self
      end

      def store_id(value)
        @store_id = value
        self
      end

      def frontend
        @purpose.set_as_frontend
        self
      end

      def backend(value)
        @query_params = value
        @purpose.set_as_backend
        self
      end

      def includes(value)
        unless value.is_a?(Array)
          raise ArgumentError, "Invalid query params: #{value}"
        end

        @indludes_files = value
        self
      end

      def format_time(tm)
        # tm.present? ? tm.utc.strftime("%Y-%m-%dT%H:%M:%S.%6N") : Time.at(0).utc.strftime("%Y-%m-%dT%H:%M:%S.%6N")
        tm.present? ? tm.utc.strftime("%Y-%m-%dT%H:%M:%S.%6N") : nil
      end

      def format_date(date)
        # date.present? ? date.to_time.utc.strftime("%Y-%m-%d") : Time.at(0).utc.strftime("%Y-%m-%d")
        date.present? ? date.to_time.utc.strftime("%Y-%m-%d") : nil
      end

      def delete(tenant, obj)
        if obj.class.name == "Integer"
          id = "#{tenant}-#{obj}"
          Rails.logger.info("Elasticsearch, delete from #{index_name} id:#{id}")
          ElasticsearchClient.delete(index: index_name, id: id, ignore: 404)
        elsif obj.class.name == model_class.name
          id = "#{tenant}-#{obj.id}"
          Rails.logger.info("Elasticsearch, delete from #{index_name} id:#{id}")
          ElasticsearchClient.delete(index: index_name, id: id, ignore: 404)
        else
          raise ArgumentError, "Invalid obj class: #{obj.class.name} != #{model_class.name}"
        end
      end

      def recreate_index
        cli = ElasticsearchClient
        if cli.indices.exists(index: index_name)
          cli.indices.delete(index: index_name)
        end
        unless cli.indices.exists(index: index_name)
          cli.indices.create(index: index_name, body: index_mapping)
        end
      end

      def check_and_create_index
        cli = ElasticsearchClient
        unless cli.indices.exists(index: index_name)
          cli.indices.create(index: index_name, body: index_mapping)
        end
      end

      def has_records_in_elasticsearch
        ret = ElasticsearchClient.count(index: index_name)
        ret["count"] > 0
      end

      def latest_record_from_db
        products = model_class.order(updated_at: :desc).limit(1)
        products.empty? ? nil : products[0]
      end

      def latest_record_updated_at_in_elasticsearch(cli, tenant)
        latest_updated_at = nil
        response = cli.search(
          index: index_name,
          body: {
            query: { match: { tenant: tenant } },
            sort: [{ "updated_at": { order: "desc" } }],
            size: 1,
          },
        )

        if response["hits"]["hits"].any?
          latest_record = response["hits"]["hits"].first["_source"]
          latest_updated_at = latest_record["updated_at"]
        end

        latest_updated_at
      end

      def gen_query_statement
        raise("#{self.class} should implement gen_query_statement method")
      end

      def sync_latest_records
        raise("#{self.class} should implement sync_latest_records method")
      end

      def sync_all
        raise("#{self.class} should implement sync_all method")
      end

      def sync
        raise("#{self.class} should implement sync method")
      end

      def fill_as_includes(records)
        return records if @indludes_files.blank?

        ret = []
        records.each do |record|
          rec = {}
          record.each do |k, v|
            if k == "_source"
              source = record["_source"]
              rec[k] = {}
              source.each do |s_k, s_v|
                if @indludes_files.include?(s_k)
                  rec[k][s_k] = s_v
                end
              end
            else
              rec[k] = v
            end
          end
          ret.push(rec)
        end
        ret
      end

      def search(query = {}, order = {}, opt = {})
        from = opt[:from].nil? ? @from : opt[:from]
        size = opt[:size].nil? ? @size : opt[:size]

        gen_query_statement
        body = { query: @query }
        if query.present?
          body[:query] = query
        end
        if @order.present? || order.present?
          body[:sort] = order.presence || @order
        end

        if opt[:aggs].present?
          body[:aggs] = opt[:aggs]
        end

        Rails.logger.info("Elasticsearch search from #{from} size #{size}: Body: #{body}")
        records = ElasticsearchClient.search(
          index: [index_name],
          from: from,
          size: size,
          body: body,
        )

        if opt[:aggs].present?
          records["aggregations"]
        else
          fill_as_includes(records["hits"]["hits"])
        end
      end

      def ids
        gen_query_statement
        records = search
        ids = []
        records.each do |doc|
          source = doc["_source"]
          ids.push(source["id"])
        end
        ids
      end

      def names
        gen_query_statement
        records = search
        names = []
        records.each do |doc|
          source = doc["_source"]
          names.push(source["name"])
        end
        names
      end

      def count(query = {})
        gen_query_statement
        body = { query: @query }
        if query.present?
          body[:query] = query
        end
        ret = ElasticsearchClient.count(
          index: [index_name],
          body: body,
        )
        Rails.logger.info("Elasticsearch count: #{body}")
        ret["count"]
      end

      def any?
        # check if count > 0
        Rails.logger.info("Elasticsearch any?")
        count > 0
      end
    end
  end
end
