# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Elasticsearch
    class Listing < AxelSpree::Elasticsearch::Base
      def initialize
        super
        @purpose = AxelSpree::Elasticsearch::Search::Purpose.new("backend")
        @currency = "USD"
      end

      def index_name
        "#{Rails.application.config.host_alias}_Listing".downcase
      end

      def model_class
        ::Spree::Listing
      end

      def index_mapping
        {
          "mappings": {
            "properties": {
              "updated_at": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "created_at": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "start_time": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "end_time": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "keyword_title": { "type": "keyword" },
              # "keyword_product_name": { "type": "keyword" },
              "keyword_sku": { "type": "keyword" },
              "product": index_nested(::AxelSpree::Elasticsearch::Product),
            },
          },
        }
      end

      # The between_date looks like "2020-01-01 to 2020-01-02"
      def split_date_between(between_date)
        return if between_date.blank?

        start_date, end_date = between_date.split(" to ")
        start_date.nil? || end_date.nil? ? {} : { begin: start_date, end: end_date }
      end

      def frontend(value = {})
        unless value.is_a?(Hash)
          raise ArgumentError, "Invalid frontend: #{value}, it should be a hash"
        end

        @query_params = value
        @purpose.set_as_frontend
        self
      end

      def name(value)
        @query_params[:name] = value
        self
      end

      def currency(value)
        @currency = value
        self
      end

      def gen(tenant, listing, included = [])
        es_product = ::AxelSpree::Elasticsearch::Product.new
        product = listing.product
        doc = {
          tenant: tenant,
          id: listing.id,

          title: listing.title,
          sku: listing.sku,
          # product_name: product.name.downcase,

          keyword_title: listing.title.downcase,
          # keyword_product_name: product.name.downcase,
          keyword_sku: (listing.sku || "").downcase,

          # product_id: listing.product_id,
          description: Loofah.fragment((listing.description || "").gsub(/[\r]/, "")).text,

          sale_channel_id: listing.sale_channel_id,

          updated_at: format_time(listing.updated_at),
          created_at: format_time(listing.created_at),
          start_time: format_date(listing.start_time),
          end_time: format_date(listing.end_time),

          brand: listing.sale_channel.brand,

          status: listing.status,
          store_id: listing.store_id,
        }
        # Check if :product in the inclued filed
        doc[:product] = es_product.gen(tenant, product) if included.include?(:product) && product.present?
        doc
      end

      def add(tenant, listing)
        doc = gen(tenant, listing, [:product])
        ElasticsearchClient.index(index: index_name, id: "#{tenant}-#{listing.id}", body: doc)
      end

      def sync_latest_records
        check_and_create_index

        tenant = Apartment::Tenant.current
        scope = ::Spree::ElasticsearchSyncRecord.where({ record_type: model_class.name }).order(id: :asc)
        scope.find_each(batch_size: 100) do |record|
          case record.op
          when "op_update"
            listing = record.target_record
            if listing.present?
              Rails.logger.info("Sync latest listings to elasticsearch, tenant:#{tenant}, id:#{listing.id}, name:\"#{listing.title}\"")
              add(tenant, listing)
            else
              Rails.logger.info("Sync latest listings to elasticsearch, tenant:#{tenant}, id:#{listing.id} not exist.")
            end
          when "op_delete"
            delete(tenant, record.record_id)
          else
            Rails.logger.info("Unknown Elasticsearch Sync op: #{record.op}")
          end
          record.destroy
        end
      end

      def sync_all
        check_and_create_index

        # sync from Spree::Listing table
        tenant = Apartment::Tenant.current
        model_class.find_each(batch_size: 100) do |listing|
          Rails.logger.info("Sync all listings to elasticsearch, tenant:#{tenant}, id:#{listing.id}, title:\"#{listing.title}\"")
          add(tenant, listing)
        end
      end

      def sync
        check_and_create_index

        if has_records_in_elasticsearch
          sync_latest_records
        else
          sync_latest_records
          Rails.logger.info("Create a EsListingJob ...")
          # sync all the tenant prodcts in Job
          EsListingJob.perform_later
        end
      end

      def backend_match_level_standard(params)
        title_cond = nil
        product_cond = nil
        sale_channel_id_cond = nil
        start_time_cond = nil
        end_time_cond = nil
        status_cond = nil

        if params[:title].present?
          title_cond = gen_bool_should(1, [
            gen_wildcard(:keyword_title, params[:title]),
            gen_match(:title, params[:title]),
            gen_match(:description, params[:title]),
          ])
        end

        if params[:product_name].present?
          product_name_cond = gen_bool_should(1, [
            gen_wildcard("product.keyword_name", params[:product_name]),
            gen_match("product.name", params[:product_name]),
          ])
          product_cond = gen_nested("product", product_name_cond)
        end

        start_time = split_date_between(params[:start_time_between])
        end_time = split_date_between(params[:end_time_between])

        # sale_channel_id_cond = gen_match(:sale_channel_id, params[:sale_channel_id]) if params[:sale_channel_id].present?
        sale_channel_id_cond = add_sale_channel_id_query(params[:sale_channel_id]) if params[:sale_channel_id].present?
        start_time_cond = st_time(:start_time).must_exist.between(start_time[:begin], start_time[:end]) if start_time.present?
        end_time_cond = st_time(:end_time).must_exist.between(end_time[:begin], end_time[:end]) if end_time.present?
        status_cond = add_status_query(params[:status]) if params[:status].present?

        @query = backend_basic_match(
          params[:store_id] || @store_id,
          [
            title_cond,
            product_cond,
            sale_channel_id_cond,
            start_time_cond,
            end_time_cond,
            status_cond,
          ],
        )
        @query
      end

      def frontend_match_level_standard(params, store_id, currency)
        product_cond = nil
        if params[:name].present?
          product_name_cond = gen_bool_should(1, [
            gen_wildcard("product.keyword_meta_title", params[:name]),
            gen_match("product.meta_title", params[:name]),
            gen_match("product.meta_keywords", params[:name]),
            gen_match("product.meta_description", params[:name]),
          ])
          product_cond = gen_bool_should(1, [
            gen_wildcard(:keyword_title, params[:name]),
            gen_match(:title, params[:name]),
            gen_match(:description, params[:name]),
            gen_nested("product", product_name_cond),
          ])
        end
        @query = frontend_basic_match(store_id, currency, [product_cond])
        @query
      end

      def gen_query_statement_for_backend(query_params)
        # Only support standard mode
        raise ArgumentError, "Unsupported mode: #{value}" unless @mode.standard?

        backend_match_level_standard(query_params)
      end

      def gen_query_statement_for_storefront(query_params)
        # Only support standard mode
        raise ArgumentError, "Unsupported mode: #{value}" unless @mode.standard?

        frontend_match_level_standard(query_params, @store_id, @currency)
      end

      def gen_query_statement
        if @purpose.frontend?
          gen_query_statement_for_storefront(@query_params)
        else
          gen_query_statement_for_backend(@query_params)
        end
      end

      def product_ids
        gen_query_statement
        records = search
        ids = []
        records.each do |doc|
          source = doc["_source"]
          id = source.dig("product", "id")
          ids.push(id) if id.present?
        end
        ids.uniq!
        ids
      end

      # This function does not support pagination
      def storefront_product_ids
        gen_query_statement
        aggs = {
          distinct_product_ids: {
            nested: { path: "product" },
            aggs: { product_ids: { terms: { field: "product.id", size: @size } } },
          },
        }
        records = search({}, {}, { from: 0, size: 0, aggs: aggs })

        ids = []
        product_ids = records.dig("distinct_product_ids", "product_ids", "buckets") || []

        product_ids.each do |doc|
          id = doc.dig("key")
          ids.push(id) if id.present?
        end
        ids.uniq!
        ids
      end

      private

      def add_status_query(status)
        status_query = {
          bool: {
            should: [],
            minimum_should_match: 1,
          },
        }
        status.each do |tx|
          status_query[:bool][:should].push(gen_match("status", tx)) if tx.present?
        end
        status_query
      end

      def add_sale_channel_id_query(sale_channel_id)
        sale_channel_id_query = {
          bool: {
            should: [],
            minimum_should_match: 1,
          },
        }
        sale_channel_id.each do |sc|
          sale_channel_id_query[:bool][:should].push(gen_match("sale_channel_id", sc)) if sc.present?
        end
        sale_channel_id_query
      end

      def backend_basic_match(store_id, must_list = [])
        gen_bool_must([
          { match: { tenant: Apartment::Tenant.current } },
          store_id.present? ? gen_match(:store_id, store_id) : nil,
        ] + must_list)
      end

      def frontend_basic_match(store_id, currency, must_list = [])
        gen_bool_must([
          { match: { tenant: Apartment::Tenant.current } },
          { match: { brand: "storefront" } },
          { match: { status: "Active" } },
          gen_nested(
            "product",
            gen_bool_must([
              gen_with_field("product.prices.#{currency}"),
              gen_match("product.active", true),
              gen_without_field("product.deleted_at"),
              st_time("product.available_on").must_exist.lte,
              st_time("product.discontinue_on").allown_absent.gt,
            ]),
          ),
          store_id.present? ? gen_match(:store_id, store_id) : nil,
        ] + must_list)
      end
    end
  end
end
