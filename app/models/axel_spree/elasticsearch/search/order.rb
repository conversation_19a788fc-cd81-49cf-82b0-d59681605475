# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Elasticsearch
    module Search
      class Order
        def initialize(field, sort = "asc", missing = "_first")
          @field = field
          @sort = sort
          @missing = missing
        end

        def missing(value)
          if ["_last", "_first", "_key", "_value", "_count", "_score", "_none"].exclude?(value)
            raise ArgumentError, "Invalid Order missing value #{value}"
          end

          @missing = value
          self
        end

        def sort(value)
          if ["desc", "asc"].exclude?(value)
            raise ArgumentError, "Invalid Order sort value #{value}"
          end

          @sort = value
          self
        end

        def to_hash
          part_hash = {}
          part_hash[@field] = { order: @sort, missing: @missing }
          part_hash
        end
      end
    end
  end
end
