# frozen_string_literal: true

require "time"

module <PERSON><PERSON><PERSON>
  module Elasticsearch
    module Search
      class StatementTime
        include Statement

        def initialize(field_name)
          @field_name = field_name
          @field_allown_absent = false
        end

        def allown_absent
          @field_allown_absent = true
          self
        end

        def must_exist
          @field_allown_absent = false
          self
        end

        def should_num
          @field_allown_absent ? 1 : 2
        end

        def gt(tm = Time.now.utc.iso8601)
          gen_bool_should(should_num, [
            exist_statement,
            { range: { @field_name => { gt: tm } } },
          ])
        end

        def gte(tm = Time.now.utc.iso8601)
          gen_bool_should(should_num, [
            exist_statement,
            { range: { @field_name => { gte: tm } } },
          ])
        end

        def lte(tm = Time.now.utc.iso8601)
          gen_bool_should(should_num, [
            exist_statement,
            { range: { @field_name => { lte: tm } } },
          ])
        end

        def between(tm1, tm2)
          range = {}
          range[:gte] = tm1 if tm1.present?
          range[:lte] = tm2 if tm2.present?
          gen_bool_should(should_num, [
            exist_statement,
            { range: { @field_name => range } },
          ])
        end

        private

        def exist_statement
          if @field_allown_absent
            { bool: { must_not: { exists: { field: @field_name } } } }
          else
            { bool: { must: { exists: { field: @field_name } } } }
          end
        end
      end
    end
  end
end
