# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Elasticsearch
    module Search
      class Mode
        def initialize(value = "standard")
          raise ArgumentError, "Invalid purpose: #{value}" unless valid?(value)

          @value = value
        end

        def valid?(value)
          value.is_a?(String) && ["standard", "fuzzy", "lucene"].include?(value)
        end

        def invalid?(value)
          !valid?(value)
        end

        def standard?
          @value == "standard"
        end

        def fuzzy?
          @value == "fuzzy"
        end

        def lucene?
          @value == "lucene"
        end
      end
    end
  end
end
