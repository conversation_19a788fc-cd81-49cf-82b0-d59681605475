# frozen_string_literal: true

require "time"

module <PERSON><PERSON><PERSON>
  module Elasticsearch
    module Search
      module Statement
        def gen_wildcard(field, value)
          part_hash = {}
          part_hash[field] = "*#{value.downcase}*"
          { wildcard: part_hash }
        end

        def gen_match(field, value)
          part_hash = {}
          part_hash[field] = { query: value, operator: "and" }
          { match: part_hash }
        end

        def gen_fuzzy(field, value)
          part_hash = {}
          part_hash[field] = value
          { fuzzy: part_hash }
        end

        def gen_terms(field, value)
          part_hash = {}
          part_hash[field] = [value]
          { terms: part_hash }
        end

        def gen_query_string(field, value)
          { query_string: { default_field: field, query: value } }
        end

        def gen_with_field(field_name)
          { exists: { field: field_name } }
        end

        def gen_without_field(field_name)
          { bool: { must_not: { exists: { field: field_name } } } }
        end

        def gen_bool_should(min, should_list)
          should_list.compact!
          { bool: { should: should_list, minimum_should_match: min } }
        end

        def gen_bool_must(must_list)
          must_list.compact!
          { bool: { must: must_list } }
        end

        def gen_nested(path, statement)
          { nested: { path: path, query: statement } }
        end
      end
    end
  end
end
