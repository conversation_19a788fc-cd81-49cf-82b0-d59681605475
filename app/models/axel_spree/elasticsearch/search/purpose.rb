# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Elasticsearch
    module Search
      class Purpose
        def initialize(value = "frontend")
          raise ArgumentError, "Invalid purpose: #{value}" unless valid?(value)

          @value = value
        end

        def valid?(value)
          value.is_a?(String) && ["frontend", "backend"].include?(value)
        end

        def invalid?(value)
          !valid?(value)
        end

        def backend?
          @value == "backend"
        end

        def frontend?
          @value == "frontend"
        end

        def set_as_backend
          @value = "backend"
        end

        def set_as_frontend
          @value = "frontend"
        end
      end
    end
  end
end
