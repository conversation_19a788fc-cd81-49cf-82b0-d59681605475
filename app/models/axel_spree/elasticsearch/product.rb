# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Elasticsearch
    class Product < AxelSpree::Elasticsearch::Base
      def initialize
        super
        @currency = "USD"
      end

      def index_name
        "#{Rails.application.config.host_alias}_Product".downcase
      end

      def model_class
        ::Spree::Product
      end

      def index_mapping
        {
          "mappings": {
            "properties": {
              "updated_at": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "created_at": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "deleted_at": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "discontinue_on": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "available_on": { "type": "date", "format": "strict_date_optional_time||epoch_millis" },
              "keyword_name": { "type": "keyword" },
              "keyword_sku": { "type": "keyword" },
              "keyword_upc": { "type": "keyword" },
              "keyword_meta_title": { "type": "keyword" },
            },
          },
        }
      end

      def currency(value)
        @currency = value
        self
      end

      def frontend(value = {})
        unless value.is_a?(Hash)
          raise ArgumentError, "Invalid frontend: #{value}, it should be a hash"
        end

        @query_params = value
        @purpose.set_as_frontend
        self
      end

      def name(value)
        @query_params[:name] = value
        self
      end

      def get_upc(product)
        upc = []
        sku = []
        variant_ids = []
        product.variants_including_master.each do |variant|
          upc.push(variant.upc) if variant.upc.presence
          sku.push(variant.sku) if variant.sku.presence
          variant_ids.push(variant.id)
        end
        [upc, sku, variant_ids]
      end

      def get_prices(product)
        prices = {}
        product.prices_including_master.each do |price|
          if prices.key?(price.currency)
            prices[price.currency].push({ variant_id: price.variant_id, amount: price.amount })
          else
            prices[price.currency] = [{ variant_id: price.variant_id, amount: price.amount }]
          end
        end
        prices
      end

      def gen(tenant, product)
        upc, sku, variant_ids = get_upc(product)
        {
          tenant: tenant,
          id: product.id,
          name: product.name,
          keyword_name: product.name.downcase,
          keyword_upc: upc.join(" ").downcase,
          keyword_sku: sku.join(" ").downcase,
          keyword_meta_title: (product.meta_title || "").downcase,
          variant_ids: variant_ids,
          prices: get_prices(product),
          all_upc: upc.join(" ").downcase,
          all_sku: sku.join(" ").downcase,
          description: Loofah.fragment((product.description || "").gsub(/[\r]/, "")).text,
          meta_title: product.meta_title,
          meta_description: product.meta_description,
          meta_keywords: product.meta_keywords,
          updated_at: format_time(product.updated_at),
          created_at: format_time(product.created_at),
          deleted_at: format_time(product.deleted_at),
          discontinue_on: format_time(product.discontinue_on),
          available_on: format_time(product.available_on),

          active: product.active?,
          taxons: product.taxon_ids,
          store_ids: product.store_ids,
        }
      end

      def add(tenant, product)
        doc = gen(tenant, product)
        ElasticsearchClient.index(index: index_name, id: "#{tenant}-#{product.id}", body: doc)
      end

      def sync_latest_records
        check_and_create_index

        tenant = Apartment::Tenant.current
        scope = ::Spree::ElasticsearchSyncRecord.where({ record_type: model_class.name }).order(id: :asc)
        scope.find_each(batch_size: 100) do |record|
          case record.op
          when "op_update"
            product = record.record_type.constantize.with_deleted.find_by(id: record.record_id)
            if product.present?
              Rails.logger.info("Sync latest products to elasticsearch, tenant:#{tenant}, id:#{product.id}, name:\"#{product.name}\"")
              add(tenant, product)
            end
          when "op_delete"
            delete(tenant, record.record_id)
          else
            Rails.logger.info("Unknown Elasticsearch Sync op: #{record.op}")
          end
          record.destroy
        end
      end

      def sync_all
        check_and_create_index

        tenant = Apartment::Tenant.current
        scope = model_class.with_deleted
        scope.find_each(batch_size: 100) do |product|
          Rails.logger.info("Sync all products to elasticsearch, tenant:#{tenant}, id:#{product.id}, name:\"#{product.name}\"")
          add(tenant, product)
        end
      end

      def sync
        check_and_create_index

        if has_records_in_elasticsearch
          sync_latest_records
        else
          sync_latest_records
          Rails.logger.info("Create a EsProductJob ...")
          # sync all the tenant prodcts in Job
          EsProductJob.perform_later
        end
      end

      def storefront_match_level_fuzzy(params, store_id, currency)
        name_cond = []
        if params[:name].present?
          name = params[:name]
          name_cond = [
            gen_match(:name, name),
            gen_match(:description, name),
            gen_match(:meta_title, name),
            gen_match(:meta_keywords, name),
            gen_match(:meta_description, name),
          ]
        end
        @query = storefront_basic_match(
          name_cond,
          store_id,
          currency,
        )
      end

      def storefront_match_level_standard(params, store_id, currency)
        name_cond = []
        if params[:name].present?
          name = params[:name]
          name_cond = [
            gen_wildcard(:keyword_name, name),
            gen_wildcard(:keyword_meta_title, name),
            gen_match(:name, name),
            gen_match(:description, name),
            gen_match(:meta_title, name),
            gen_match(:meta_keywords, name),
            gen_match(:meta_description, name),
          ]
        end
        @query = storefront_basic_match(
          name_cond,
          store_id,
          currency,
        )
      end

      def storefront_match_level_lucene(params, store_id, currency)
        name_cond = []
        if params[:name].present?
          name = params[:name]
          name_cond = [
            gen_query_string(:name, name),
            gen_query_string(:description, name),
            gen_query_string(:meta_title, name),
            gen_query_string(:meta_keywords, name),
            gen_query_string(:meta_description, name),
          ]
        end
        @query = storefront_basic_match(
          name_cond,
          store_id,
          currency,
        )
      end

      def gen_query_statement_for_storefront
        if @mode.fuzzy?
          storefront_match_level_fuzzy(@query_params, @store_id, @currency)
        elsif @mode.lucene?
          storefront_match_level_lucene(@query_params, @store_id, @currency)
        else
          storefront_match_level_standard(@query_params, @store_id, @currency)
        end
      end

      def backend_match_level_fuzzy(params)
        name_cond = nil
        sku_cond = nil
        upc_cond = nil
        taxons_cond = nil
        deleted_at_cond = nil
        discontinued_cond = nil

        if params[:name].present?
          name_cond = gen_bool_should(1, [
            gen_fuzzy(:name, params[:name]),
            gen_fuzzy(:description, params[:name])
          ])
        end

        if params[:sku].present?
          sku_cond = gen_bool_should(1, [
            gen_wildcard(:keyword_sku, params[:sku]),
            gen_fuzzy(:all_sku, params[:sku]),
          ])
        end

        if params[:upc].present?
          upc_cond = gen_bool_should(1, [
            gen_wildcard(:keyword_upc, params[:upc]),
            gen_fuzzy(:all_upc, params[:upc]),
          ])
        end

        taxons_cond = add_taxons_query(params[:taxons]) if params[:taxons].present? && params[:taxons].empty?
        deleted_at_cond = gen_without_field("deleted_at") if params[:with_deleted] == false
        discontinued_cond = query_discontinued if params[:with_discontinued] == false

        @query = backend_basic_match(
          params[:store_id] || @store_id,
          [
            name_cond,
            sku_cond,
            upc_cond,
            taxons_cond,
            deleted_at_cond,
            discontinued_cond,
          ],
        )

        @query
      end

      def backend_match_level_standard(params)
        name_cond = nil
        sku_cond = nil
        upc_cond = nil
        taxons_cond = nil
        deleted_at_cond = nil
        discontinued_cond = nil

        if params[:name].present?
          name_cond = gen_bool_should(1, [
            gen_wildcard(:keyword_name, params[:name]),
            gen_match(:name, params[:name]),
            gen_match(:description, params[:name]),
          ])
        end

        if params[:sku].present?
          sku_cond = gen_bool_should(1, [
            gen_wildcard(:keyword_sku, params[:sku]),
            gen_match(:all_sku, params[:sku]),
          ])
        end

        if params[:upc].present?
          upc_cond = gen_bool_should(1, [
            gen_wildcard(:keyword_upc, params[:upc]),
            gen_match(:all_upc, params[:upc]),
          ])
        end

        taxons_cond = add_taxons_query(params[:taxons]) if params[:taxons].present? && !params[:taxons].empty?
        deleted_at_cond = gen_without_field("deleted_at") if params[:with_deleted] == false
        discontinued_cond = query_discontinued if params[:with_discontinued] == true

        @query = backend_basic_match(
          params[:store_id] || @store_id,
          [
            name_cond,
            sku_cond,
            upc_cond,
            taxons_cond,
            deleted_at_cond,
            discontinued_cond,
          ],
        )

        @query
      end

      def backend_match_level_lucene(params)
        @query = backend_basic_match(
          params[:store_id] || @store_id,
          [
            params[:name].present? ? gen_query_string(:name, params[:name]) : nil,
            params[:name].present? ? gen_query_string(:description, params[:name]) : nil,
            params[:upc].present? ? gen_query_string(:all_upc, params[:upc].downcase) : nil,
            params[:sku].present? ? gen_query_string(:all_sku, params[:sku].downcase) : nil,
            params[:taxons].present? && !params[:taxons].empty? ? add_taxons_query(params[:taxons]) : nil,
            params[:with_deleted] == false ? gen_without_field("deleted_at") : nil,
            params[:with_discontinued] == true ? query_discontinued : nil,
          ],
        )

        @query
      end

      def gen_query_statement_for_backend(query_params)
        if @mode.fuzzy?
          backend_match_level_fuzzy(query_params)
        elsif @mode.lucene?
          backend_match_level_lucene(query_params)
        else
          backend_match_level_standard(query_params)
        end
      end

      def gen_query_statement
        if @purpose.frontend?
          gen_query_statement_for_storefront
        else
          gen_query_statement_for_backend(@query_params)
        end
      end

      private

      def query_discontinued
        st_time("discontinue_on").allown_absent.gt
      end

      def query_available_on
        st_time("available_on").must_exist.lte
      end

      def add_taxons_query(taxons)
        taxons_query = {
          bool: {
            should: [],
            minimum_should_match: 1,
          },
        }
        taxons.each do |taxon_id|
          taxons_query[:bool][:should].push({ term: { taxons: taxon_id } }) if taxon_id.present?
        end
        taxons_query
      end

      def storefront_basic_match(should_list, store_id, currency)
        gen_bool_must([
          { match: { active: true } },
          { match: { tenant: Apartment::Tenant.current } },
          { bool: { must_not: { exists: { field: "deleted_at" } } } },
          { "exists": { "field": "prices.#{currency}" } },
          query_discontinued,
          query_available_on,
          store_id.present? ? gen_terms(:store_ids, store_id) : nil,
          should_list.present? ? gen_bool_should(1, should_list) : nil,
        ])
      end

      def backend_basic_match(store_id, must_list = [])
        gen_bool_must([
          { match: { tenant: Apartment::Tenant.current } },
          store_id.present? ? gen_terms(:store_ids, store_id) : nil,
        ] + must_list)
      end
    end
  end
end
