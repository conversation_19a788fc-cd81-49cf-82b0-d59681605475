# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Cms
      module Sections
        module SideBySideImagesDecorator
          def self.prepended(base)
            base.store(
              :content,
              accessors: [
                :link_type_one,
                :link_one,
                :title_one,
                :subtitle_one,
                :link_type_two,
                :link_two,
                :title_two,
                :subtitle_two,
                :link_type_three,
                :link_three,
                :title_three,
                :subtitle_three,
              ],
              coder: JSON,
            )
          end

          # img_three sizing
          def img_three_md(dimensions = "387x250>")
            super
          end

          def img_three_lg(dimensions = "734x476>")
            super
          end

          def img_three_xl(dimensions = "1468x952>")
            super
          end

          def reset_multiple_link_attributes
            return if Rails::VERSION::STRING < "6.0"

            if link_type_one_changed?
              return if link_type_one_was.nil?

              self.link_one = nil
            end

            if link_type_two_changed?
              return if link_type_two_was.nil?

              self.link_two = nil
            end

            if link_type_three_changed?
              return if link_type_three_was.nil?

              self.link_three = nil
            end
          end

          def default_values
            self.gutters ||= "Gutters"
            self.fit ||= "Container"
            self.link_type_one ||= "Spree::Taxon"
            self.link_type_two ||= "Spree::Taxon"
            self.link_type_three ||= "Spree::Taxon"
          end
        end
      end
    end
  end
end

Spree::Cms::Sections::SideBySideImages.prepend(AxelSpree::Spree::Cms::Sections::SideBySideImagesDecorator)
