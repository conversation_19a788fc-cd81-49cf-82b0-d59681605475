# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module StockItemDecorator
      def self.prepended(base)
        base._validators.reject! { |key, _| key == :variant_id }
        base._validate_callbacks.each do |callback|
          if callback.raw_filter.respond_to?(:attributes)
            callback.raw_filter.attributes.reject! { |key| key == :variant_id }
          end
        end

        base.scope(:in_stock, -> { where('spree_stock_items.count_on_hand > 0') })
        base.scope(:available_stock_locations, -> { with_active_stock_location.in_stock
                                                    .select('spree_stock_locations.id, spree_stock_locations.name')
                                                    .distinct
                                                  })

        base.has_many(:stock_item_units, dependent: :destroy)
        base.has_many(:activity_logs, as: :loggable, class_name: 'Spree::ActivityLog')
        base.belongs_to(:stock_location_section, optional: true, inverse_of: :stock_items)
        base.validates(:variant_id, uniqueness: { scope: [:stock_location_id, :stock_location_section_id, :deleted_at] })
      end

      def should_track_inventory?
        false
      end

      ::Spree::StockItem.prepend(self)
    end
  end
end
