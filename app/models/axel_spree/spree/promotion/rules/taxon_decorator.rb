# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Promotion
      module Rules
        module TaxonDecorator
          NEW_MATCH_POLICIES = ["any", "all", "none"]

          def eligible?(order, _options = {})
            if preferred_match_policy == "all"
              unless (taxons.to_a - taxons_in_order_including_parents(order)).empty?
                eligibility_errors.add(:base, eligibility_error_message(:missing_taxon))
              end
            elsif preferred_match_policy == "any"
              order_taxons = taxons_in_order_including_parents(order)
              unless taxons.any? { |taxon| order_taxons.include?(taxon) }
                eligibility_errors.add(:base, eligibility_error_message(:no_matching_taxons))
              end
            else
              order_taxons = taxons_in_order_including_parents(order)
              unless taxons.any? { |taxon| order_taxons.exclude?(taxon) }
                eligibility_errors.add(:base, eligibility_error_message(:has_excluded_product))
              end
            end

            eligibility_errors.empty?
          end
        end
      end
    end
  end
end

Spree::Promotion::Rules::Taxon.prepend(AxelSpree::Spree::Promotion::Rules::TaxonDecorator)
