# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Stock
      module PackageDecorator
        def shipping_methods
          shipping_categories.includes(:shipping_methods).map(&:shipping_methods).reduce(:&)&.in_store(order.store_id).to_a
        end

        def api_key
          @api_key ||= order.store.easypost_setting.key
          @api_key
        end

        def easypost_parcel
          ::EasyPost::Client.new(api_key: api_key)
            .parcel.create(weight: weight)
        end

        def stock_location_returns
          ::Spree::StockLocation.find_by(id: order.store.easypost_setting.returns_stock_location_id)
        end

        def easypost_customs_info
          return unless customs_required?

          customs_items = contents.map do |item|
            variant = item.variant
            product = variant.product

            ::EasyPost::Client.new(api_key: api_key)
              .customs_item.create(
                description: product.taxons.map(&:name).join(" "),
                quantity: item.quantity,
                value: variant.price * item.quantity,
                weight: variant.weight,
                hs_tariff_number: product.easy_post_hs_tariff_number,
                origin_country: stock_location.country.try(:iso),
              )
          end

          raise "Contact Support For EEL/PFC" if order.total > 2500

          ::EasyPost::Client.new(api_key: api_key).customs_info.create(
            eel_pfc: order.store.easypost_setting.customs_eel_pfc,
            customs_certify: true,
            customs_signer: order.store.easypost_setting.customs_signer,
            contents_type: order.store.easypost_setting.customs_contents_type,
            customs_items: customs_items,
          )
        end

        def carrier_accounts
          order.store.easypost_setting.carrier_accounts_shipping.split(",")
        end

        def shipment_options
          {
            print_custom_1: ref_number,
            print_custom_1_barcode: true,
            print_custom_2: build_sku_list,
            print_custom_2_barcode: false,
            endorsement: order.store.easypost_setting.endorsement_type,
          }
        end

        def easypost_shipment(parcel = nil)
          ::EasyPost::Client.new(api_key: api_key)
            .shipment.create(
              to_address: order.ship_address.try(:easypost_address, api_key),
              from_address: stock_location.try(:easypost_address, api_key),
              return_address: stock_location_returns.try(:easypost_address, api_key),
              parcel: {
                weight: parcel.present? && parcel[:weight].present? ? parcel[:weight] : weight,
                length: parcel.present? && parcel[:length].present? ? parcel[:length] : depth,
                width: parcel.present? && parcel[:width].present? ? parcel[:width] : width,
                height: parcel.present? && parcel[:height].present? ? parcel[:height] : height,
              },
              customs_info: easypost_customs_info,
              reference: order.number, # ref_number,
              carrier_accounts: carrier_accounts,
              options: shipment_options,
            )
            .tap { |shipment_easypost_res| Rails.logger.info(shipment_easypost_res) }
        end

        def depth
          max_depth = contents.filter_map { |content_item| content_item.variant.depth }.max
          max_depth.nil? || max_depth.zero? ? 1 : max_depth
        end

        def width
          max_width = contents.filter_map { |content_item| content_item.variant.width }.max
          max_width.nil? || max_width.zero? ? 1 : max_width
        end

        def height
          max_height = contents.filter_map { |content_item| content_item.variant.height }.max
          max_height.nil? || max_height.zero? ? 1 : max_height
        end

        def weight
          contents.sum { |content_item| content_item.variant.weight }
        end

        def shipping_categories
          listing_ids = present_listing_ids

          if listing_ids.any?
            ::Spree::ShippingCategory
              .joins(:listings)
              .where(spree_listings: { id: listing_ids })
              .distinct
          else
            ::Spree::ShippingCategory.where(name: 'Free Shipping')
          end
        end

        def present_listing_ids
          ::Spree::Listing.where(id: extracted_listing_ids).pluck(:id)
        end

        def extracted_listing_ids
          contents.filter_map do |item|
            item.inventory_unit&.line_item&.listing_id
          end.uniq
        end
      end
    end
  end
end

Spree::Stock::Package.prepend(AxelSpree::Spree::Stock::PackageDecorator)
