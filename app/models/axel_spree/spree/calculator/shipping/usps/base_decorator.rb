# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Calculator
      module Shipping
        module Usps
          module BaseDecorator
            def compute_package(package)
              order = package.order
              stock_location = package.stock_location

              origin = build_location(stock_location)
              destination = build_location(order.ship_address)

              rates_result = retrieve_rates_from_cache(package, origin, destination)

              return if rates_result.is_a?(Spree::ShippingError)
              return if rates_result.empty?

              rate = rates_result[self.class.service_code]

              return unless rate

              rate = rate.to_f + (order.store.active_shipping_setting.general_settings["handling_fee"].to_f || 0.0)

              # divide by 100 since active_shipping rates are expressed as cents
              rate / 100.0
            end

            # Extracting store specific shipping carrier's config values
            def carrier(order)
              active_shipping_setting = order.store.active_shipping_setting
              carrier_details = {
                login: active_shipping_setting.usps["usps_login"],
                test: active_shipping_setting.general_settings["test_mode"],
              }

              ::ActiveShipping::USPS.new(carrier_details)
            end

            def rate_options(order)
              if active_shipping_setting.usps["usps_commercial_plus"]
                { commercial_plus: true }
              elsif active_shipping_setting.usps["usps_commercial_base"]
                { commercial_base: true }
              else
                {}
              end
            end

            def retrieve_rates(origin, destination, shipment_packages, package)
              order = package.order
              response = carrier(order).find_rates(origin, destination, shipment_packages, rate_options(order))
              # turn this beastly array into a nice little hash
              service_code_prefix_key = response.params.keys.first == "IntlRateV2Response" ? :international : :domestic
              rates = response.rates.collect do |rate|
                service_code = "#{SERVICE_CODE_PREFIX[service_code_prefix_key]}:#{rate.service_code}"
                [service_code, rate.price]
              end
              rate_hash = Hash[*rates.flatten]
              rate_hash
            rescue ::ActiveShipping::Error => e
              if e.class == ::ActiveShipping::ResponseError && e.response.is_a?(::ActiveShipping::Response)
                params = e.response.params
                message = params&.dig("Response", "Error", "ErrorDescription").presence ||
                  params&.dig("eparcel", "error", "statusMessage").presence ||
                  e.message
              else
                message = e.message
              end

              error = Spree::ShippingError.new("#{I18n.t(:shipping_error)}: #{message}")
              Rails.cache.write(@cache_key, error) # write error to cache to prevent constant re-lookups
              raise error
            end
          end
        end
      end
    end
  end
end

Spree::Calculator::Shipping::Usps::Base.prepend(AxelSpree::Spree::Calculator::Shipping::Usps::BaseDecorator)
