# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Calculator
      module Shipping
        module CanadaPost
          module BaseDecorator
            # Extracting store specific shipping carrier's config values
            def carrier
              active_shipping_setting = order.store.active_shipping_setting
              canada_post_options = {
                login: active_shipping_setting.canada_post["canada_post_login"],
                french: I18n.locale.to_sym.eql?(:fr),
              }
              ::ActiveShipping::CanadaPost.new(canada_post_options)
            end
          end
        end
      end
    end
  end
end

Spree::Calculator::Shipping::CanadaPost::Base.prepend(AxelSpree::Spree::Calculator::Shipping::CanadaPost::BaseDecorator)
