# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Calculator
      module Shipping
        module AustraliaPost
          module BaseDecorator
            def carrier(order)
              active_shipping_setting = order.store.active_shipping_setting
              australia_post_options = {
                api_key: active_shipping_setting.australia_post["australia_post_login"],
              }
              ::ActiveShipping::AustraliaPost.new(australia_post_options)
            end
          end
        end
      end
    end
  end
end

Spree::Calculator::Shipping::AustraliaPost::Base.prepend(AxelSpree::Spree::Calculator::Shipping::AustraliaPost::BaseDecorator)
