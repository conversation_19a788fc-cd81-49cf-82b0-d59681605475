# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Calculator
      module Shipping
        module ActiveShipping
          module BaseDecorator
            def compute_package(package)
              order = package.order
              stock_location = package.stock_location

              origin = build_location(stock_location)
              destination = build_location(order.ship_address)

              rates_result = retrieve_rates_from_cache(package, origin, destination)

              return if rates_result.is_a?(::Spree::ShippingError)
              return if rates_result.empty?

              rate = rates_result[self.class.description]

              return unless rate

              # override - get store specific handling fees
              rate = rate.to_f + (active_shipping_setting(package).general_settings["handling_fee"].to_f || 0.0)

              # divide by 100 since active_shipping rates are expressed as cents
              rate / 100.0
            end

            # Method to extract active shipping setting
            def active_shipping_setting(package)
              @active_shipping_setting ||= package.order.store.active_shipping_setting
            end

            def packages(package)
              # override - get store specific units
              units = active_shipping_setting(package).general_settings["units"].to_sym
              packages = []
              weights = convert_package_to_weights_array(package)
              max_weight = get_max_weight(package)
              dimensions = convert_package_to_dimensions_array(package)
              item_specific_packages = convert_package_to_item_packages_array(package)

              if max_weight <= 0
                packages << ::ActiveShipping::Package.new(weights.sum, dimensions, units: units)
              else
                package_weight = 0
                weights.each do |content_weight|
                  if package_weight + content_weight <= max_weight
                    package_weight += content_weight
                  else
                    packages << ::ActiveShipping::Package.new(package_weight, dimensions, units: units)
                    package_weight = content_weight
                  end
                end
                packages << ::ActiveShipping::Package.new(
                  package_weight,
                  dimensions,
                  units: units,
                ) if package_weight > 0
              end

              item_specific_packages.each do |package|
                packages << ::ActiveShipping::Package.new(
                  package.at(0),
                  [package.at(1), package.at(2), package.at(3)],
                  units: :imperial,
                )
              end

              Rails.logger.info(packages.inspect)

              packages
            end

            def convert_package_to_weights_array(package)
              # override - get store specific Unit Multiplier
              multiplier = active_shipping_setting(package).general_settings["unit_multiplier"].to_i
              # override - get store specific Default Weight
              default_weight = active_shipping_setting(package).general_settings["default_weight"].to_i

              max_weight = get_max_weight(package)
              weights = []

              package.contents.each do |content_item|
                item_weight = content_item.variant.weight.to_f
                item_weight = default_weight if item_weight <= 0
                item_weight *= multiplier

                if max_weight <= 0 || item_weight < max_weight
                  content_item.quantity.times { weights << item_weight }
                else
                  message = "#{I18n.t(:shipping_error)}: The maximum per package weight for the selected service from \
                  the selected country is #{max_weight} ounces."

                  raise ::Spree::ShippingError, message
                end
              end
              weights.sort
            end

            def get_max_weight(package)
              order = package.order
              max_weight = max_weight_for_country(order.ship_address.country)
              # override - get store specific Max Weight per Package
              settings = active_shipping_setting(package).general_settings
              max_weight_per_package = settings["max_weight_per_package"].to_i * settings["unit_multiplier"].to_i
              if (max_weight == 0) && (max_weight_per_package > 0)
                max_weight = max_weight_per_package
              elsif (max_weight > 0) && (max_weight_per_package < max_weight) && (max_weight_per_package > 0)
                max_weight = max_weight_per_package
              end

              max_weight
            end

            def convert_package_to_item_packages_array(package)
              # override - get store specific Unit Multiplier
              multiplier = active_shipping_setting(package).general_settings["unit_multiplier"].to_i
              max_weight = get_max_weight(package)
              packages = []

              package.contents.each do |content_item|
                variant  = content_item.variant
                quantity = content_item.quantity
                product  = variant.product

                product.product_packages.each do |product_package|
                  if (product_package.weight.to_f <= max_weight) || (max_weight == 0)
                    quantity.times do
                      packages << [
                        product_package.weight * multiplier,
                        product_package.length,
                        product_package.width,
                        product_package.height,
                      ]
                    end
                  else
                    message = "#{I18n.t(:shipping_error)}: The maximum per package weight for the selected service \
                    from the selected country is #{max_weight} ounces."

                    raise ::Spree::ShippingError, message
                  end
                end
              end
              packages
            end

            def retrieve_rates(origin, destination, shipment_packages, package)
              # Added additional argument for retrieving the carrier infomation store specific - carrier(package.order)
              response = carrier(package.order).find_rates(origin, destination, shipment_packages)
              Rails.logger.debug { "RATES RESPONSE: #{response.inspect}" }
              # turn this beastly array into a nice little hash
              rates = response.rates.collect do |rate|
                service_name = rate.service_name.encode("UTF-8")
                [CGI.unescapeHTML(service_name), rate.price]
              end
              rate_hash = Hash[*rates.flatten]

              Rails.logger.info("FINAL RATES: #{rate_hash}")

              rate_hash
            rescue ::ActiveShipping::Error => e
              Rails.logger.error("ERROR FETCHING RATES: #{e.inspect}")

              if e.class == ::ActiveShipping::ResponseError && e.response.is_a?(::ActiveShipping::Response)
                params = e.response.params
                message = params&.dig("Response", "Error", "ErrorDescription").presence ||
                  params&.dig("eparcel", "error", "statusMessage").presence ||
                  e.message
              else
                message = e.message
              end
              error = ::Spree::ShippingError.new("#{I18n.t(:shipping_error)}: #{message}")
              Rails.cache.write(@cache_key, error) # write error to cache to prevent constant re-lookups
              raise error
            end

            def cache_key(package)
              order = package.order
              ship_address = package.order.ship_address
              stock_location = package.stock_location
              stock_location_state = fetch_best_state_from_address(stock_location) if stock_location

              contents_hash = Digest::MD5.hexdigest(
                package.contents.map do |content_item|
                  content_item.variant.id.to_s + "_" +
                  content_item.quantity.to_s + "_" +
                  content_item.variant.weight.to_s + "_" +
                  content_item.variant.height.to_s + "_" +
                  content_item.variant.width.to_s + "_" +
                  content_item.variant.depth.to_s
                end.join("|"),
              )

              parts = [
                stock_location&.id,
                stock_location&.country&.iso,
                stock_location_state,
                stock_location&.city,
                stock_location&.zipcode,
                carrier(package.order).name,
                order.number,
                ship_address.country.iso,
                fetch_best_state_from_address(ship_address),
                ship_address.city,
                ship_address.zipcode,
                contents_hash,
                I18n.locale,
              ]

              @cache_key = parts.compact_blank.join("-")
            end

            def retrieve_rates_from_cache(package, origin, destination)
              Rails.cache.fetch(cache_key(package)) do
                shipment_packages = packages(package)
                if shipment_packages.empty?
                  {}
                else
                  # Added additional argument for retrieving the carrier infomation store specific-- package
                  retrieve_rates(origin, destination, shipment_packages, package)
                end
              end
            end
          end
        end
      end
    end
  end
end

Spree::Calculator::Shipping::ActiveShipping::Base.prepend(AxelSpree::Spree::Calculator::Shipping::ActiveShipping::BaseDecorator)
