# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Calculator
      module Shipping
        module Ups
          module BaseDecorator
            # Extracting store specific shipping carrier's config values
            def carrier(order)
              active_shipping_setting = order.store.active_shipping_setting
              carrier_details = {
                login: active_shipping_setting.ups["ups_login"],
                password: active_shipping_setting.ups["ups_password"],
                key: active_shipping_setting.ups["ups_key"],
                test: active_shipping_setting.general_settings["test_mode"],
              }

              if (shipper_number = active_shipping_setting.ups["shipper_number"])
                carrier_details[:origin_account] = shipper_number
              end

              ::ActiveShipping::UPS.new(carrier_details)
            end
          end
        end
      end
    end
  end
end

Spree::Calculator::Shipping::Ups::Base.prepend(AxelSpree::Spree::Calculator::Shipping::Ups::BaseDecorator)
