# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Calculator
      module Shipping
        module Fedex
          module BaseDecorator
            # Extracting store specific shipping carrier's config values
            def carrier(order)
              active_shipping_setting = order.store.active_shipping_setting
              carrier_details = {
                key: active_shipping_setting.fedex["fedex_key"],
                password: active_shipping_setting.fedex["fedex_password"],
                account: active_shipping_setting.fedex["fedex_account"],
                login: active_shipping_setting.fedex["fedex_login"],
                test: active_shipping_setting.general_settings["test_mode"],
              }
              ::ActiveShipping::FedEx.new(carrier_details)
            end
          end
        end
      end
    end
  end
end
Spree::Calculator::Shipping::Fedex::Base.prepend(AxelSpree::Spree::Calculator::Shipping::Fedex::BaseDecorator)
