# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module PaymentDecorator
      def self.prepended(base)
        base.scope(:paypal, -> { joins(:payment_method).where(payment_method: { type: "Spree::Gateway::PayPalExpress" }) })
        base.state_machine.after_transition(to: :completed, do: :fetch_fees)
      end

      def fetch_fees
        @fees_job_scheduled ||= false
        return if @fees_job_scheduled

        case payment_method.type
        when "Spree::Gateway::PayPalExpress"
          FetchFeesJob.set(wait: 20.minutes).perform_later(order.id, :paypal)
        when "Spree::Gateway::StripeElementsGateway"
          FetchFeesJob.set(wait: 20.minutes).perform_later(order.id, :stripe)
        end

        @fees_job_scheduled = true
      end
    end
  end
end
Spree::Payment.prepend(AxelSpree::Spree::PaymentDecorator)
