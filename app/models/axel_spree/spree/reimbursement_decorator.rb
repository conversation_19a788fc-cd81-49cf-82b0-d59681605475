# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module ReimbursementDecorator
      def remove_tax_for_returned_items
        return unless order.store.taxjar_setting&.taxjar_enabled
        return unless taxjar_applicable?(order)

        client = ::Spree::Taxjar.new(order, self)
        client.create_refund_transaction_for_order
      end

      def calculated_total
        # rounding every return item individually to handle edge cases for consecutive partial
        # returns where rounding might cause us to try to reimburse more than was originally billed
        total = return_items.sum { |ri| ri.total.to_d.round(2) }
        # adding the shipment fees for refund
        if return_items.try(:first).try(:return_authorization).try(:refund_shipping_fee)
          total += order.shipments.sum { |sh| sh.shipping_cost_with_tax.to_d.round(2) }
        end

        total
      end
    end
  end
end

Spree::Reimbursement.prepend(AxelSpree::Spree::ReimbursementDecorator)
