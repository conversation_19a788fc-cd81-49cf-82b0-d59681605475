# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module ReturnAuthorizationDecorator
      def self.prepended(base)
        base.after_commit(:send_return_authorization_mail, on: [:create])
        base.state_machine(initial: :pending) do
          event(:approve) do
            transition(to: :authorized, from: [:pending, :canceled])
          end
        end
        base.after_commit(:authorize_request, on: [:update])
      end

      def send_label(customer_shipment)
        send_return_authorization_label_mail(customer_shipment) # no ok
      end

      private

      def send_return_authorization_mail
        if state == "authorized"
          ::Spree::ReturnAuthorizationMailer.return_authorization_email(id).deliver_later
        end
      end

      def send_return_authorization_label_mail(customer_shipment)
        ::Spree::ReturnAuthorizationSendLabelMailer.return_authorization_send_label_email(id, customer_shipment).deliver_later
      end

      def authorize_request
        if authorized && state != "authorized"
          approve!
        end
      end
    end
  end
end

Spree::ReturnAuthorization.prepend(AxelSpree::Spree::ReturnAuthorizationDecorator)
