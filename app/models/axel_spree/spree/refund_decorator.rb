# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module RefundDecorator
      def perform!
        return true if transaction_id.present?

        credit_cents = ::Spree::Money.new(amount.to_f, currency: payment.currency).amount_in_cents

        @response = process!(credit_cents)

        self.transaction_id = if @response.respond_to?(:RefundTransactionID)
          @response.RefundTransactionID
        else
          @response.authorization
        end
        update!(transaction_id: transaction_id)
        update_order
      end
    end
  end
end

Spree::Refund.prepend(AxelSpree::Spree::RefundDecorator)
