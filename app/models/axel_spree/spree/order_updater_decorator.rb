# frozen_string_literal: true

module Axel<PERSON>pree
  module Spree
    module OrderUpdaterDecorator
      def update
        update_item_count
        update_totals
        if order.completed?
          update_shipments
          update_shipment_state
          update_shipment_total
          update_payment_state
        end
        run_hooks
        persist_totals
      end

      def update_item_total
        line_items&.reload
        if line_items&.take&.storefront_sale_channel?
          amount_total = 0
          line_items.each do |line_item|
            # quantity = line_item.quantity / (line_item.listing&.pack_size_value || 1)
            quantity = line_item.quantity / line_item.pack_size
            amount_total += (line_item.price * quantity)
          end
          order.item_total = amount_total
        else
          order.item_total = line_items.sum('price * quantity')
        end
        update_order_total
      end

      def update_payment_state
        order.tracker_logs.create(details: "Storefront Order (#{order.number}) has been successfully called update_payment_state with payment_state #{order.payment_state}.")

        last_state = order.payment_state

        order.tracker_logs.create(details: "payments.present? (#{payments.present?}), payments.valid.empty? (#{payments&.valid.empty?})")

        if payments.present? && payments.valid.empty?
          order.payment_state = 'failed'
        elsif order.canceled? && order.payment_total == 0
          order.tracker_logs.create(details: "payment_state (#{order.payment_state}), order canceled? (#{order.canceled?}), payment_total (#{order.payment_total})")
          order.payment_state = 'void'
        else
          order.tracker_logs.create(details: "payment_state (#{order.payment_state}), order canceled? (#{order.canceled?}), payment_total (#{order.payment_total})")

          order.tracker_logs.create(details: "outstanding_balance > 0 (#{order.outstanding_balance > 0}), < 0 (#{order.outstanding_balance < 0}), present? (#{order.outstanding_balance?})")

          order.payment_state = 'balance_due' if order.outstanding_balance > 0
          order.payment_state = 'credit_owed' if order.outstanding_balance < 0
          order.payment_state = 'paid' unless order.outstanding_balance?
        end

        order.state_changed('payment') if last_state != order.payment_state
        order.tracker_logs.create(details: "Final payment_state: #{order.payment_state}")

        order.payment_state
      end

      def persist_totals
        order.tracker_logs.create(details: "persist_totals method payment_state: #{order.payment_state}, shipment_state: #{order.shipment_state}")
        order.update_columns(
          payment_state: order.payment_state,
          shipment_state: order.shipment_state,
          item_total: order.item_total,
          item_count: order.item_count,
          adjustment_total: order.adjustment_total,
          included_tax_total: order.included_tax_total,
          additional_tax_total: order.additional_tax_total,
          payment_total: order.payment_total,
          shipment_total: order.shipment_total,
          promo_total: order.promo_total,
          total: order.total,
          updated_at: Time.current
        )
      end

      def update_shipment_state
        order.tracker_logs.create(details: "update_shipment_state 1 method payment_state: #{order.payment_state}, shipment_state: #{order.shipment_state}")
        if order.backordered?
          order.shipment_state = 'backorder'
        else
          # get all the shipment states for this order
          shipment_states = shipments.states
          order.shipment_state = if shipment_states.size > 1
                                   # multiple shipment states means it's most likely partially shipped
                                   'partial'
                                 else
                                   # will return nil if no shipments are found
                                   shipment_states.first
                                   # TODO: inventory unit states?
                                   # if order.shipment_state && order.inventory_units.where(shipment_id: nil).exists?
                                   #   shipments exist but there are unassigned inventory units
                                   #   order.shipment_state = 'partial'
                                   # end
                                 end
        end

        order.state_changed('shipment')
        order.tracker_logs.create(details: "update_shipment_state 2 method payment_state: #{order.payment_state}, shipment_state: #{order.shipment_state}")
        order.shipment_state
      end

      ::Spree::OrderUpdater.prepend(self)
    end
  end
end
