# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module ShippingRateDecorator
      include ::Spree::Admin::LineItemsExtHelper
      def self.prepended(base)
        base.delegate(:admin_name, to: :shipping_method, prefix: true)
        base.belongs_to(:order_package, class_name: "Spree::OrderPackage")
        base.delegate :order, :currency, :free?, to: :shipment, allow_nil: true
        base.after_update(:check_line_items_ext_after_update)
      end

      # Safe version of free? method with proper fallback
      def free?
        shipment&.free? || false  # Safely return false if shipment is nil
      end

      # Optional method: to ensure shipments are correctly associated with shipping rates
      def shipment
        order_package&.shipments&.first || ::Spree::Shipment.find_by(id: shipment_id) 
      end

      def check_line_items_ext_after_update
        order_package = self.order_package
        return unless order_package.present?
        Rails.logger.info("=====entering ShippingRateDecorator check_line_items_ext_after_update -2: #{order_package&.id}")
        order = order_package&.order
        return unless order.present?
        Rails.logger.info("=====entering ShippingRateDecorator check_line_items_ext_after_update -1: #{order&.id}")
        if order&.line_items.present?
          Rails.logger.info("=====entering ShippingRateDecorator check_line_items_ext_after_update 0: #{order&.id}")
          order&.line_items.each do |item|
            Rails.logger.info("=====entering ShippingRateDecorator check_line_items_ext_after_update 1: #{item&.id}")
            line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: item&.id)
            process_line_items_ext(line_items_ext, item, order) if line_items_ext
          end
        end
      end

    end
  end
end

Spree::ShippingRate.prepend(AxelSpree::Spree::ShippingRateDecorator)
