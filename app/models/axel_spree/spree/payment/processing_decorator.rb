# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Payment
      module ProcessingDecorator
        def create_intent!
          gateway_action(payment_method, :create_intent, :pend)
        end

        def handle_response(response, success_state, failure_state)
          record_response(response)
          if response.success?
            # if response.respond_to?(:params)
            #  self.intent_client_key = response.params['client_secret'] if response.params['client_secret']
            #  self.intent_id = response.params['id'] if response.params['id']
            # end
            if response.respond_to?(:authorization) && !response.authorization.nil?
              self.response_code = response.authorization
              self.avs_response = response.avs_result["code"]

              if response.cvv_result
                self.cvv_response_code = response.cvv_result["code"]
                self.cvv_response_message = response.cvv_result["message"]
              end
            end
            send(:"#{success_state}!")
          else
            send(failure_state)
            gateway_error(response)
          end
        end

        def cancel!
          # response = payment_method.cancel(response_code, self)
          response = payment_method.cancel(response_code)
          handle_response(response, :void, :failure)
        end
      end
    end
  end
end

Spree::Payment::Processing.prepend(AxelSpree::Spree::Payment::ProcessingDecorator)
