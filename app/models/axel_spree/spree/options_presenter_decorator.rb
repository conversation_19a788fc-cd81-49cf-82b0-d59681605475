# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module OptionsPresenterDecorator
      private

      def sort_options(options)
        options.sort_by { |o| o.option_type&.position }
      end

      def present_options(options)
        options.map do |ov|
          method = "present_#{ov.option_type&.name}_option"

          respond_to?(method, true) ? send(method, ov) : present_option(ov)
        end
      end

      def present_color_option(option)
        "#{option.option_type&.presentation}: #{option.name}"
      end

      def present_option(option)
        "#{option.option_type&.presentation}: #{option.presentation}"
      end
    end
  end
end

Spree::Variants::OptionsPresenter.prepend(AxelSpree::Spree::OptionsPresenterDecorator)
