# frozen_string_literal: true

module Axel<PERSON>pree
  module Spree
    module CustomerShipmentDecorator
      def carrier_accounts
        order.store.easypost_setting.carrier_accounts_returns.split(",")
      end

      def api_key
        @api_key ||= order.store.easypost_setting.key
        @api_key
      end

      def generate_label
        client = ::EasyPost::Client.new(api_key: api_key)
        shipment = client.shipment
        ep_shipment = shipment.retrieve(easypost_shipment_id)
        shipment.buy(ep_shipment.id, rate: easypost_shipment.lowest_rate) unless easypost_shipment.postage_label
        self.easypost_shipment_id = easypost_shipment.id
        self.tracking = easypost_shipment.tracking_code
        self.tracking_label = get_label_pdf
        self.weight = easypost_shipment.parcel.weight
      end

      def easypost_shipment
        @ep_shipment ||= if easypost_shipment_id
          ::EasyPost::Client.new(api_key: api_key).shipment.retrieve(easypost_shipment_id)
        else
          build_easypost_shipment
        end
        @ep_shipment
      end

      def build_easypost_shipment
        ::EasyPost::Client.new(api_key: api_key).shipment.create(
          from_address: stock_location.easypost_address(api_key),
          to_address: order.ship_address.easypost_address(api_key),
          reference: return_authorization.number,
          parcel: build_parcel,
          carrier_accounts: carrier_accounts,
          options: {
            print_custom_1: return_authorization.number,
            print_custom_1_barcode: true,
            print_custom_2: build_sku_list,
            print_custom_2_barcode: false,
          },
          is_return: true,
        )
      end

      def build_parcel
        parcel_weight = if !return_authorization.custom_weight.nil? && return_authorization.custom_weight > 0
          return_authorization.custom_weight
        else
          return_authorization.inventory_units.joins(:variant).sum(:weight)
        end

        ::EasyPost::Client.new(api_key: api_key)
          .parcel.create(weight: parcel_weight)
      end
    end
  end
end

Spree::CustomerShipment.prepend(AxelSpree::Spree::CustomerShipmentDecorator)
