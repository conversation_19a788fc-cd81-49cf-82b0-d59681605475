# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module OptionTypeDecorator
      def self.prepended(base)
        base._validators.reject! { |key, _| key == :name }
        base._validate_callbacks.each do |callback|
          callback.raw_filter.attributes.reject! { |key| key == :name } if callback.raw_filter.respond_to?(:attributes)
        end

        base.validates(
          :name,
          uniqueness: { case_sensitive: true, scope: base.spree_base_uniqueness_scope.push(:store_id) },
        )

        base.belongs_to(:store, class_name: "Spree::Store")

        def base.default_scope
          i18n.where.not(name: "~").order(:position)
        end
      end
    end
  end
end

Spree::OptionType.prepend(AxelSpree::Spree::OptionTypeDecorator)
