# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module VariantDecorator
      class << self
        def prepended(base)
          base.has_many(:listing_inventories, class_name: "Spree::ListingInventory", dependent: :destroy)
          base.has_many(:recommended_prices, class_name: "Spree::RecommendedPrice", dependent: :destroy)
          base.scope(:filter_by_name_or_upc, ->(query) {
            joins(:product).join_translation_table(::Spree::Product)
              .where(
                "#{::Spree::Product.translation_table_alias}.name ILIKE ? OR spree_variants.upc ILIKE ?",
                "%#{query}%",
                "%#{query}%",
              )
          })
          base.whitelisted_ransackable_attributes << "upc"
          base::MEMOIZED_METHODS.then do |methods|
            base.send(:remove_const, :MEMOIZED_METHODS)
            base.const_set(:MEMOIZED_METHODS, (methods + ["compare_to_price"]).uniq)
          end
        end
      end

      def current_listing_qty
        # listing_inventories.joins(:listing).where.not(listing: { status: "draft" }).sum("total_listed_qty - total_sold_qty")
        listing_inventories.joins(:listing).where(listing: { status: "Active" }).sum("total_listed_qty - total_sold_qty")
      end

      def full_options_text
        @full_options_text ||= ::Spree::Variants::FullOptionsPresenter.new(self).to_sentence
      end

      def total_available
        pending_commited_count = ::Spree::InventoryUnit.joins(:shipment, :line_item)
          .where.not(spree_shipments: { state: "canceled" })
          .where.not(state: "shipped")
          .where(pending: false, variant_id: id)
          .sum("spree_inventory_units.quantity * spree_line_items.pack_size")
        total_on_hand - pending_commited_count
      end

      def total_committed
        ::Spree::InventoryUnit.joins(:shipment, :line_item)
          .where.not(spree_shipments: { state: "canceled" })
          .where.not(state: "shipped")
          .where(pending: false, variant_id: id)
          .sum("spree_inventory_units.quantity * spree_line_items.pack_size")
      end

      def variant_total_available
        pending_commited_count = ::Spree::InventoryUnit.joins(:shipment, :line_item)
          .where.not(spree_shipments: { state: "canceled" })
          .where.not(state: "shipped")
          .where(pending: false, variant_id: id)
          .sum("spree_inventory_units.quantity * spree_line_items.pack_size")
        variant_total_on_hand - pending_commited_count
      end

      def variant_total_on_hand
        if track_inventory? == false
          Float::INFINITY
        else
          stock_items.sum(:count_on_hand)
        end
      end

      def to_option
        { id: id, text: sku }
      end

      def compute_volume_price(volume_price)
        case volume_price.discount_type
        when "price"
          volume_price.amount <= price ? volume_price.amount : price
        when "dollar"
          price - volume_price.amount > 0 ? price - volume_price.amount : price
        when "percent"
          price * (1 - volume_price.amount.to_f / 100) > 0 ? price * (1 - volume_price.amount.to_f / 100) : price
        end
      end

      def compare_to_amount_in(currency)
        price_in(currency).try(:compare_to_amount)
      end

      def compare_to_price
        @compare_to_price ||= price_in(cost_currency).try(:compare_to_amount)
      end

      def available_in_stock
        ret = []
        ::Spree::StockItemUnit
          .where(stock_item_id: stock_item_ids)
          .where(state: "stock")
          .pluck(:vendor_inventory_cost, :pack_size).each do |cost, size|
          if cost.present?
            size_f = size.present? ? size.to_f : 1.0
            ret << (cost.to_f / size_f).round(2)
          end
        end
        ret
      end

      def max_vic
        available_in_stock.max
      end

      def min_vic
        available_in_stock.min
      end

      def avg_vic
        vic_sum = 0.0
        pack_size_sum = 0.0
        ::Spree::StockItemUnit
          .where(stock_item_id: stock_item_ids)
          .where(state: "stock")
          .pluck(:vendor_inventory_cost, :pack_size).each do |cost, size|
            if cost.present?
              vic_sum += cost.to_f
              pack_size_sum += size.present? ? size.to_f : 1.0
            end
          end
        pack_size_sum.zero? ? "0.0" : (vic_sum / pack_size_sum).round(2)
      end
    end
  end
end

Spree::Variant.prepend(AxelSpree::Spree::VariantDecorator)
