# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module ScanFormDecorator
      def self.prepended(base)
        base.belongs_to(:store, class_name: "Spree::Store")
      end

      def generate_scan_form
        if @easy_post_shipments.blank?
          errors.add(:base, 'No EasyPost Shipments Found')
          throw(:abort)
        end

        begin
          client = ::EasyPost::Client.new(api_key: store.easypost_setting.key)
          @scan_form = client.scan_form.create(shipments: @easy_post_shipments)
          self.easy_post_scan_form_id = @scan_form.id
          self.scan_form = @scan_form.form_url
        rescue EasyPost::Errors::InvalidRequestError,
               EasyPost::Errors::BadRequestError,
               EasyPost::Errors => e
          errors.add(:base, e.message)
          throw(:abort)
        end
      end
    end
  end
end

Spree::ScanForm.prepend(AxelSpree::Spree::ScanFormDecorator)
