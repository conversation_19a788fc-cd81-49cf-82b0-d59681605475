# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module OptionValueDecorator
      def self.prepended(base)
        def base.find_or_initialize_by_downcase_name(name)
          option_val = join_translation_table(::Spree::OptionValue)
            .where("LOWER(#{::Spree::OptionValue.translation_table_alias}.name) = ?", name.downcase).take
          option_val = find_or_initialize_by(name: name) if option_val.blank?
          option_val
        end
      end
    end
  end
end

Spree::OptionValue.prepend(AxelSpree::Spree::OptionValueDecorator)
