# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module CmsSectionDecorator
      def self.prepended(base)
        base.belongs_to(:listing, optional: true)
      end

      def content_link(link_type, link)
        if link.present?
          if link_type == "Spree::Product"
            listing = ::Spree::Listing.find_by(title: link)
            product = listing&.product
            base_link(link_type, product, listing) if listing.present?
          elsif link_type == "Spree::Taxon"
            base_taxon_link(link)
          end
        end
      end

      def gen_content
        if is_a?(::Spree::Cms::Sections::SideBySideImages) || is_a?(::Spree::Cms::Sections::ImageGallery)
          {
            title_one: content[:title_one],
            subtitle_one: content[:subtitle_one],
            link_type_one: content[:link_type_one],
            link_one: content_link(content[:link_type_one], content[:link_one]),

            title_two: content[:title_two],
            subtitle_two: content[:subtitle_two],
            link_type_two: content[:link_type_two],
            link_two: content_link(content[:link_type_two], content[:link_two]),

            title_three: content[:title_three],
            subtitle_three: content[:subtitle_three],
            link_type_three: content[:link_type_three],
            link_three: content_link(content[:link_type_three], content[:link_three]),
          }
        else
          content
        end
      end

      def link
        base_link(linked_resource_type, linked_resource, listing)
      end
    end
  end
end

Spree::CmsSection.prepend(AxelSpree::Spree::CmsSectionDecorator)
