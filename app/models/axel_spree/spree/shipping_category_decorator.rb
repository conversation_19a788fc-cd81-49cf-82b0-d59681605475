# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module ShippingCategoryDecorator
      def self.prepended(base)
        base._validators.reject! { |key, _| key == :name }
        base._validate_callbacks.each do |callback|
          callback.raw_filter.attributes.reject! { |key| key == :name } if callback.raw_filter.respond_to?(:attributes)
        end
        base.belongs_to(:store, class_name: "Spree::Store")
        base.has_many(:listings, class_name: "Spree::Listing")
        base.validates(
          :name,
          presence: true,
          uniqueness: { case_sensitive: true },
        )
      end
    end
  end
end

Spree::ShippingCategory.prepend(AxelSpree::Spree::ShippingCategoryDecorator)
