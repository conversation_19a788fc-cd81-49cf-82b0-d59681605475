# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module PriceDecorator
      def self.prepended(base)
        base.validates(
          :compare_to_amount,
          allow_nil: true,
          numericality: {
            greater_than_or_equal_to: 0,
            less_than_or_equal_to: base::MAXIMUM_AMOUNT,
          },
        )
        base.alias_attribute(:compare_to_price, :compare_to_amount)
        base.money_methods(:compare_to_amount)
        base.alias_method(:display_compare_to_price, :display_compare_to_amount)
        base.whitelisted_ransackable_attributes = ["amount", "compare_at_amount", "compare_to_amount"]
      end

      def compare_at_amount=(compare_at_amount)
        self[:compare_at_amount] = ::Spree::LocalizedNumber.parse(compare_at_amount.presence)
      end

      def compare_to_money
        ::Spree::Money.new(compare_to_amount || 0, currency: currency)
      end

      def compare_to_amount=(compare_to_amount)
        self[:compare_to_amount] = ::Spree::LocalizedNumber.parse(compare_to_amount.presence)
      end

      def compare_to_price_including_vat_for(price_options)
        options = price_options.merge(tax_category: variant.tax_category)
        gross_amount(compare_to_price, options)
      end

      def display_compare_to_price_including_vat_for(price_options)
        ::Spree::Money.new(compare_to_price_including_vat_for(price_options), currency: currency)
      end
    end
  end
end

Spree::Price.prepend(AxelSpree::Spree::PriceDecorator)
