# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module RoleUserDecorator
      def self.prepended(base)
        base.belongs_to(:store)
        base.scope(:for_store, ->(store) { where(store_id: store.id) })

        def base.has_store_role(user, store, role_name)
          joins(:role).for_store(store).exists?(user: user, role: { name: role_name })
        end
      end
    end
  end
end

Spree::RoleUser.prepend(AxelSpree::Spree::RoleUserDecorator)
