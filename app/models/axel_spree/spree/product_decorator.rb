# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module ProductDecorator
      def self.prepended(base)
        base.validate(:abbreviation_length)
        base.validate(:abbreviation_format)
        base.validates(:abbreviation, presence: true, uniqueness: { case_sensitive: true })
        base.has_many(:match_its, dependent: :destroy, class_name: "Spree::MatchIt")
        base.has_many(:stock_item_units, through: :stock_items, class_name: "Spree::StockItemUnit")
        base.has_many(:featured_products, inverse_of: :product, class_name: "Spree::FeaturedProduct", dependent: :destroy)
        base.has_many(:daily_sales_records, class_name: "Spree::DailySalesRecord", dependent: :destroy)
        base.has_one(:recent_thirty_days_sales_records, class_name: "Spree::RecentThirtyDaysSalesRecord", dependent: :destroy)
        base.has_many(:image_uploads, as: :imageable, dependent: :destroy)
        base.has_many(:action_logs, class_name: "Spree::ActionLog", dependent: :destroy)
        base.has_many(
          :prices_including_master,
          -> { order("spree_variants.position, spree_variants.id, currency") },
          through: :variants_including_master,
          source: :prices,
        )

        base.before_validation(:generate_unique_abbreviation, on: :create)
        base.before_save(:check_master_price_changed)

        base.after_save(:update_product_and_listing_in_elasticsearch_with_check_save)
        base.after_save(:send_available_notification)
        base.after_create(:update_product_in_elasticsearch)
        base.after_destroy(:update_product_in_elasticsearch)
        base.after_touch(:update_product_and_listing_in_elasticsearch)
        base.after_touch(:update_listings_timestamp)
        base.after_save(:update_listings_timestamp)

        [
          :receipt_number,
          :inventory_number,
          :vendor_receipt_date,
          :compare_to_price,
          :compare_to_amount_in,
          :display_compare_to_price,
        ].each do |method_name|
          base.delegate(method_name, :"#{method_name}=", to: :find_or_build_master)

          unless ::Spree::Product.respond_to?(:searchkick_index)
            base.searchkick word_start: [:name], match: :word_start
            end
        end
      end

      def search_data
        {
          name: name
        }
      end

      def tax_category
        @tax_category ||= super || (!stores.empty? && ::Spree::TaxCategory.for_store(stores[0]).find_by(is_default: true))
      end

      def listing_name
        listings.Active&.reverse&.find { |x| x.sale_channel.brand == "storefront" }&.title
      end

      def empty_option_values?
        options.empty? || options.any? do |opt|
          opt.option_type&.option_values&.empty?
        end
      end

      def listing_pack_size_price(variant_id)
        last_active_listing&.listing_inventories&.find_by(variant_id:)&.reload&.price
      end

      def last_active_listing
        all_active_product_listings&.last
      end

      def all_active_product_listings
        listings&.Active&.joins(:sale_channel)&.where(spree_sale_channels: { brand: "storefront" })
      end

      def listing_pack_size
        last_active_listing&.pack_size_value || 1
      end

      def total_available
        [total_on_hand - total_committed, 0].max
      end

      def total_committed
        @total_committed ||= begin
          scope = ::Spree::InventoryUnit.joins(:shipment, :line_item)
          scope = scope.where.not(spree_shipments: { state: :canceled })
          scope = scope.where.not(spree_inventory_units: { state: :shipped })
          scope = scope.where(pending: false, variant_id: variants_including_master.map(&:id))
          scope.sum("spree_inventory_units.quantity * spree_line_items.pack_size")
        end
      end

      def total_on_hand
        @total_on_hand ||= if any_variants_not_track_inventory?
          Float::INFINITY
        else
          stock_items.sum(:count_on_hand)
        end
      end

      def abbreviation_length
        return if abbreviation.blank?

        errors.add(:abbreviation, "maximum length is 6") if abbreviation&.length&.> 6
      end

      def abbreviation_format
        return if abbreviation.blank?

        errors.add(
          :abbreviation,
          ": Invalid input! Please refrain from using special characters in this field",
        ) if abbreviation.include?("-")
      end

      def generate_unique_abbreviation
        return if abbreviation.present?

        loop do
          self.abbreviation = SecureRandom.hex(3).upcase
          break abbreviation unless ::Spree::Product.exists?(abbreviation: abbreviation)
        end
      end

      def check_master_price_changed
        if changes.count <= 0
          master_variant = find_or_build_master
          master_price = master_variant&.find_or_build_default_price
          if master_price&.changes&.any?
            master_variant.updated_at = Time.current
          end
        end
      end

      def update_product_and_listing_in_elasticsearch
        ::Spree::ElasticsearchSyncRecord.find_or_create(self) if id.present?
        ::Spree::ElasticsearchSyncRecord.find_or_create(listings.to_ary)
      end

      def update_product_and_listing_in_elasticsearch_with_check_save
        update_product_and_listing_in_elasticsearch if previous_changes.any?
      end

      def update_listings_timestamp
        listings.update_all(updated_at: Time.current) if listings.present?
      end

      def update_product_in_elasticsearch
        ::Spree::ElasticsearchSyncRecord.find_or_create(self) if id.present?
      end

      # for adding products which are closely related to existing ones
      # define "duplicate_extra" for site-specific actions, eg for additional fields
      def api_duplicate(params)
        duplicator = ::Spree::ProductDuplicator.new(self)
        duplicator.api_duplicate(params)
      end

      def duplicate_extra(product)
        # Remove exists abbreviation when duplicate.
        self.abbreviation = nil
      end

      def available_in_stock
        ret = []
        stock_item_units.where(state: "stock").pluck(:vendor_inventory_cost, :pack_size).each do |cost, size|
          if cost.present?
            size_f = size.present? ? size.to_f : 1.0
            ret << (cost.to_f / size_f).round(2)
          end
        end
        ret
      end

      def send_available_notification
        if saved_change_to_temporary_unavailable? && !temporary_unavailable
          notify_customers
        end
      end

      def notify_customers
        product = self
        variant_ids = product.variants.any? ? product.variants.pluck(:id) : [product.master.id] # Fallback to master ID if no variants

        action = "temporaryUnavailable"
        customers_to_notify = ::Spree::ProductNotification.where(variant_id: variant_ids, notify: true, action: action)
        return if customers_to_notify.blank?

        customers_to_notify.each do |to_notify|
          var = to_notify.variant
          next if to_notify.email.blank? || var.nil?

          CustomerNotifyJob.perform_later(::Spree::Store.default, [to_notify.email], var, action)
        end

        customers_to_notify.update_all(notify: false) # rubocop:disable Rails/SkipsModelValidations
      end

      def min_vic
        available_in_stock.min
      end

      def max_vic
        available_in_stock.map(&:to_f).max
      end

      def avg_vic
        vic_sum = 0.0
        pack_size_sum = 0.0
        stock_item_units
          .where(state: "stock")
          .pluck(:vendor_inventory_cost, :pack_size).each do |cost, size|
            if cost.present?
              vic_sum += cost.to_f
              pack_size_sum += size.present? ? size.to_f : 1.0
            end
          end
        pack_size_sum.zero? ? "0.0" : (vic_sum / pack_size_sum).round(2)
      end
    end
  end
end

Spree::Product.prepend(AxelSpree::Spree::ProductDecorator)
