# frozen_string_literal: true

module AxelSpree
  module Spree
    module AbilityDecorator
      def initialize(user)
        alias_cancan_delete_action

        user ||= ::Spree.user_class.new
        if user.persisted? && user.try(:spree_admin?)
          apply_admin_permissions(user)
        elsif user.respond_to?(:has_spree_role?) && user.has_spree_role?(:store_owner)
          apply_store_owner_permissions(user)
        elsif user.respond_to?(:has_spree_role?) && user.has_spree_role?(:store_employee)
          apply_store_employee_permissions(user)
        else
          apply_user_permissions(user)
        end

        # Include any abilities registered by extensions, etc.
        # this is legacy behaviour and should be removed in Spree 5.0
        ::Spree::Ability.abilities.merge(abilities_to_register).each do |clazz|
          merge(clazz.new(user))
        end

        protect_admin_role
        protect_store_role
      end

      protected

      def apply_store_owner_permissions(user)
        can(:manage, :all)
        cannot([:create, :update, :destroy], ::Spree::Store)
        cannot(:manage, ::Spree::Config)
        # cannot(:manage, ::Spree::Role)
      end

      def apply_store_employee_permissions(user)
        can(:manage, :all)
        cannot(:manage, ::Spree::Store)
        cannot(:manage, ::Spree::Admin::ReportsController)
        cannot(:manage, ::Spree::Promotion)
        cannot(:manage, ::Spree.user_class)
        cannot(:manage, ::Spree::Menu)
        cannot(:manage, ::Spree::Webhooks::Subscriber)
        cannot(:manage, ::Spree::OauthApplication)
        cannot(:manage, ::Spree::Config)
        cannot(:manage, ::Spree::TaxCategory)
        cannot(:manage, ::Spree::TaxRate)
        cannot(:manage, ::Spree::Zone)
        cannot(:manage, ::Spree::Country)
        cannot(:manage, ::Spree::PaymentMethod)
        cannot(:manage, ::Spree::ShippingMethod)
        cannot(:manage, ::Spree::ShippingCategory)
        cannot(:manage, ::Spree::StoreCreditCategory)
        cannot(:manage, ::Spree::RefundReason)
        cannot(:manage, ::Spree::ReimbursementType)
        cannot(:manage, ::Spree::ReturnAuthorizationReason)
        cannot(:manage, ::Spree::Role)
        cannot(:manage, ::Spree::TaxjarSetting)
        can(:read, :all)
        # cannot :read, ::Spree::Store do |store|
        #  !user.user_stores.any? { |ustore| ustore.id == store.id && ustore.managed && !store.default }
        # end
        # can :manage, ::Spree::Order
        # can :manage, ::Spree::Adjustment
        # can :manage, ::Spree::Payment
        # can :manage, ::Spree::ReturnAuthorization
        # can :manage, ::Spree::ReturnItem
        # can :manage, ::Spree::ReimbursementType
        # can :manage, ::Spree::CustomerReturn
        # can :manage, ::Spree::Product
        # can :manage, ::Spree::Image
        # can :manage, ::Spree::Variant
        # can :manage, ::Spree::ProductProperty
        # can :manage, ::Spree::StockItem
        # can :manage, ::Spree::Price
        # can :manage, ::Spree::Digital
        # can :manage, ::Spree::StockTransfer
        # can :manage, ::Spree::StockLocation
        apply_user_permissions(user)
      end

      def protect_store_role
        cannot([:update, :destroy], ::Spree::Role, name: [:store_owner, :store_employee])
      end

      def apply_user_permissions(user)
        if user.valid?
          can(:manage, :all)
        end
        can(:read, ::Spree::Country)
        can(:read, ::Spree::Menu)
        can(:read, ::Spree::CmsPage)
        can(:read, ::Spree::OptionType)
        can(:read, ::Spree::OptionValue)
        can(:create, ::Spree::Order)
        # can(:show, ::Spree::Order) do |order, token|
        #   order.user == user || order.token && token == order.token
        # end
        can(:show, ::Spree::Order)
        can(:update, ::Spree::Order) do |order, token|
          !order.completed? && (order.user == user || order.token && token == order.token)
        end
        can(:manage, ::Spree::Address, user_id: user.id)
        can([:read, :destroy], ::Spree::CreditCard, user_id: user.id)
        can(:read, ::Spree::Product)
        can(:read, ::Spree::ProductProperty)
        can(:read, ::Spree::Property)
        can(:create, ::Spree.user_class)
        can([:show, :update, :destroy], ::Spree.user_class, id: user.id)
        can(:read, ::Spree::State)
        can(:read, ::Spree::Store)
        can(:read, ::Spree::Taxon)
        can(:read, ::Spree::Taxonomy)
        can(:read, ::Spree::Variant)
        can(:read, ::Spree::Zone)
        can(:manage, ::Spree::Wishlist, user_id: user.id)
        can(:show, ::Spree::Wishlist) do |wishlist|
          wishlist.user == user || wishlist.is_private == false
        end
        can([:create, :update, :destroy], ::Spree::WishedItem) do |wished_item|
          wished_item.wishlist.user == user
        end
      end
    end
  end
end

Spree::Ability.prepend(AxelSpree::Spree::AbilityDecorator)
