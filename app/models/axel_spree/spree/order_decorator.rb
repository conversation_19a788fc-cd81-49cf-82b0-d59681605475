# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module OrderDecorator
      include ::Spree::Admin::SubscribersHelper
      include ::Spree::Admin::LineItemsExtHelper
      def self.prepended(base)
        base.has_one(:order_invoice, dependent: :destroy) # , class_name: 'Spree::OrderInvoice'
        base.has_and_belongs_to_many(:subscriptions, join_table: "spree_orders_subscriptions")
        base.has_many(:order_packages, class_name: "Spree::OrderPackage", dependent: :destroy)
        base.after_create_commit(:create_invoice)
        base.has_many(:tracker_logs, as: :trackable, dependent: :delete_all)
        base.has_many_attached(:images)
        base.has_many(:image_uploads, as: :imageable, dependent: :destroy)
        base.has_many(:listings, through: :line_items)
        base.has_many(:paypal_payments, class_name: "Spree::PaypalExpressRequest", dependent: :destroy)
        base.belongs_to(:sale_channel, class_name: 'Spree::SaleChannel')
        base.state_machine.after_transition(to: :complete, do: :sync_total_available_on_ebay)
        base.state_machine.after_transition(to: :complete, do: :create_subscriptions)
        base.state_machine.after_transition(to: :complete, do: :check_subscriber)
        base.state_machine.after_transition(to: :complete, do: :check_line_items_ext)
        base.scope(:fetch_last_three_days_subscriptions_order, -> {
          joins(:subscriptions)
          where(
            "created_at BETWEEN ? AND ? AND payment_state = ?",
            Time.current.beginning_of_day - 3.days,
            Time.current.end_of_day - 3.days,
            "pending",
          ).distinct
        })
        base.scope(:with_subscriptions, -> { joins(:subscriptions).distinct })

        base.extend(ClassMethods)
        base.whitelisted_ransackable_associations = %w[shipments user created_by approver canceler promotions bill_address ship_address line_items store sale_channel]
      end

      def create_invoice
        invoice = ::Spree::OrderInvoice.new(order: self)
        invoice.save
      end

      def valid_promotion_ids
        all_adjustments.eligible.nonzero.promotion.map { |a| a.source&.promotion_id }.uniq
      end

      def create_payment_intent!
        process_payments_with(:create_intent!)
      end

      def delete_taxjar_transaction
        return unless store.taxjar_setting
        return unless store.taxjar_setting.taxjar_enabled
        return unless taxjar_applicable?(self)

        client = ::Spree::Taxjar.new(self)
        client.delete_transaction_for_order
      end

      def capture_taxjar
        return unless store.taxjar_setting
        return unless store.taxjar_setting.taxjar_enabled
        return unless taxjar_applicable?(self)

        client = ::Spree::Taxjar.new(self)
        client.create_transaction_for_order
      end

      def shipment_ready?
        shipment_state && shipment_state == "ready"
      end

      def payment_paid?
        payment_state && payment_state == "paid"
      end

      def sync_total_available_on_ebay
        if line_items.present?
          line_items.each do |li|
            SyncListingsAvailable.perform_now(store.id, nil, li.quantity, li.variant_id) if store.risky_listing.present?
          end
        end
      end

      def create_subscriptions
        user = ::Spree::User.find_by(email: email)

        return if user.blank?
        return if line_items.blank?

        line_items.each do |line_item|
          next if line_item.subscription_enabled.blank?

          subscription = ::Spree::Subscription.find_or_create_by!(user_id: user.id, line_item_id: line_item.id)
          next_reorder_date = fetch_next_reorder_date(line_item.subscription_interval, nil)
          subscription.update(
            interval: line_item.subscription_interval,
            next_delivery_date: next_reorder_date,
            status: 0,
            quantity: line_item.subscription_quantity,
            initial_price: initial_price(line_item),
            recurring_price: recurring_price(line_item),
          )
          next if subscriptions.pluck(:id).include?(subscription.id)

          subscriptions << subscription

          ::Spree::SubscriptionMailer.subscription_email(subscription).deliver_now
        end
      end

      def check_subscriber
        if email.present?
          subscriber = ::Spree::Subscriber.find_or_create_by(email: email)
          process_subscriber_action(subscriber, 'placed order', self) if subscriber
        end
      end

      def check_line_items_ext
        if line_items.present?
          Rails.logger.info("=====entering OrderDecorator check_line_items_ext 0: #{self&.id}")
          line_items.each do |item|
            Rails.logger.info("=====entering OrderDecorator check_line_items_ext 1: #{item&.id}")
            line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: item&.id)
            process_line_items_ext(line_items_ext, item, self) if line_items_ext
          end
        end
      end

      def fetch_next_reorder_date(subscription_interval, order)
        return order.created_at if subscription_interval.nil?
        numeric_part = subscription_interval.split("_")[0].to_i
        interval = subscription_interval.split("_")[1]
        date = order.present? ? order.created_at : Time.zone.today

        # Determine the appropriate interval method
        case interval
        when "day", "days"
          date + numeric_part.days
        when "week", "weeks"
          date + numeric_part.weeks
        when "month", "months"
          date + numeric_part.months
        when "year", "years"
          date + numeric_part.years
        end
      end

      def initial_price(line_item)
        variant_id = line_item.variant.id
        active_listing = line_item.listing.presence || line_item.product.listings.Active.joins(:sale_channel).where(sale_channel: { brand: "storefront" }).last
        if active_listing.subscription_details.present? && active_listing.subscription_details[variant_id.to_s].present?
          active_listing.subscription_details[variant_id.to_s]["first_price_per_item"].to_f
        else
          line_item.reload.price
        end
      end

      def recurring_price(line_item)
        variant_id = line_item.variant.id
        active_listing = line_item.listing.presence || line_item.product.listings.Active.joins(:sale_channel).where(sale_channel: { brand: "storefront" }).last
        if active_listing.subscription_details.present? && active_listing.subscription_details[variant_id.to_s].present?
          active_listing.subscription_details[variant_id.to_s]["recurring_price_per_item"].to_f
        else
          line_item.reload.price
        end
      end

      def finalize!
        tracker_logs.create(details: "Storefront Order (#{number}) has been successfully finalized and synced and payment_state is #{payment_state}, shipment_state: #{shipment_state} ")
        super
        update(channel: "Storefront")
      end

      def create_proposed_shipments
        ApplicationRecord.transaction do
          # Touch this order here can make sure mutex on DB for this transaction.
          update(updated_at: Time.current)
          super
        end
      end

      def is_walmart?
        channel == "walmart"
      end

      def is_amazon?
        channel == "amazon"
      end

      def is_ebay?
        channel == "eBay"
      end

      def is_storefront?
        channel == "Storefront"
      end

      module ClassMethods
        def brand_to_order_channel(brand)
          case brand.to_s.downcase
          when "storefront"
            "Storefront"
          when "ebay"
            "eBay"
          when "amazon"
            "amazon"
          when "walmart"
            "walmart"
          else
            "AXEL Market"
          end
        end
      end
    end
  end
end

Spree::Order.prepend(AxelSpree::Spree::OrderDecorator)
