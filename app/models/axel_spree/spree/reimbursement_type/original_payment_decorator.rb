# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module ReimbursementType
      module OriginalPaymentDecorator
        def self.prepended(base)
          def base.reimburse(reimbursement, return_items, simulate)
            unpaid_amount = return_items.sum { |ri| ri.total.to_d.round(2) }
            # adding the shipment fees for refund
            if return_items.first.return_authorization.refund_shipping_fee
              unpaid_amount += reimbursement.order.shipments.sum do |sh|
                sh.shipping_cost_with_tax.to_d.round(2)
              end
            end

            payments = reimbursement.order.payments.completed
            reimbursement_list, _unpaid_amount = create_refunds(reimbursement, payments, unpaid_amount, simulate)
            reimbursement_list
          end
        end
      end
    end
  end
end
Spree::ReimbursementType::OriginalPayment.prepend(AxelSpree::Spree::ReimbursementType::OriginalPaymentDecorator)
