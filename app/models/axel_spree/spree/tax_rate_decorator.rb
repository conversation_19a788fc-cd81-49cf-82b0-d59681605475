# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module TaxRateDecorator
      def self.prepended(base)
        base.include(::Spree::SingleStoreResource)
        base.belongs_to(:store)
        base.validators_on(:amount).each { |val| val.attributes.delete(:amount) }
        base.validators_on(:tax_category).each { |val| val.attributes.delete(:tax_category) }

        # Deletes all tax adjustments, then applies all applicable rates
        # to relevant items.
        def base.adjust(order, items)
          rates = for_store(order.store).match(order.tax_zone)
          tax_categories = rates.map(&:tax_category)

          # using destroy_all to ensure adjustment destroy callback fires.
          ::Spree::Adjustment.where(adjustable: items).tax.destroy_all

          relevant_items = items.select do |item|
            tax_categories.include?(nil) ||
              tax_categories.include?(item.tax_category)
          end

          relevant_items.each do |item|
            relevant_rates = rates.select do |rate|
              rate.tax_category == item.tax_category
            end
            store_pre_tax_amount(item, relevant_rates)
            relevant_rates.each do |rate|
              rate.adjust(order, item)
            end
          end

          # updates pre_tax for items without any tax rates
          remaining_items = items - relevant_items
          remaining_items.each do |item|
            store_pre_tax_amount(item, [])
          end
        end
      end

      def amount_for_label
        return "" unless !amount.nil? && show_rate_in_label?

        " " + ActiveSupport::NumberHelper::NumberToPercentageConverter.convert(
          amount * 100,
          locale: I18n.locale,
        )
      end
    end
  end
end

Spree::TaxRate.prepend(AxelSpree::Spree::TaxRateDecorator)
