# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module VolumePriceDecorator
      def self.prepended(base)
        base.validate(:validate_amount)
        base.validate(:validate_range)
      end

      def validate_amount
        return if discount_type.blank?
        return if amount.blank?

        errors.add(:amount, "can't be negative or zero") if amount&.negative? || amount.zero?
        if discount_type == "percent"
          errors.add(:amount, "can't be greater than 100 for Percent Discount") if amount > 100
        end
      end

      def validate_range
        return if range.blank?

        split_val = range.to_s.split(".")
        return errors.add(:range, I18n.t(:"activerecord.errors.messages.must_be_in_format")) if split_val.compact_blank.count > 2

        if split_val.count == 3 || split_val.count == 4
          errors.add(
            :range,
            I18n.t(:"activerecord.errors.messages.must_be_in_format"),
          ) if split_val[-1].include?("+") || split_val[0].to_f > split_val[-1].to_f
        end
      end
    end
  end
end
Spree::VolumePrice.prepend(AxelSpree::Spree::VolumePriceDecorator)
