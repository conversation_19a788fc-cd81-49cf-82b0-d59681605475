# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module StripeGateway
      module StripeElementsGatewayDecorator
        delegate :create_intent, to: :provider

        def source_required?
          if get_preference(:intents)
            # Source is not present as the payment intent is created prior
            # to payment details being entered. Therefore must be set to false.
            false
          else
            true
          end
        end

        def payment_profiles_supported?
          # Stripe API does not support adding a customer to a payment method AFTER
          # payment intent has being created (as is the case with the 'store' method
          # in the current Spree implementation.
          # Instead need to create customer at the time the payment intent is created.
          if get_preference(:intents)
            false
          else
            true
          end
        end

        def credit(money, response_code, gateway_options)
          provider.refund(money, response_code, {})
        end

        def void(response_code, gateway_options)
          provider.void(response_code, {})
        end
      end
    end
  end
end

Spree::Gateway::StripeElementsGateway.prepend(AxelSpree::Spree::StripeGateway::StripeElementsGatewayDecorator)
