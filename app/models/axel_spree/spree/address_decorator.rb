# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module AddressDecorator
      def self.prepended(base)
        base.attr_accessor(:validate_phone)
        base._validators.reject! { |key, _| key == :lastname }
        base._validate_callbacks.each do |callback|
          callback.raw_filter.attributes.reject! do |key|
            key == :lastname
          end if callback.raw_filter.respond_to?(:attributes)
        end
      end

      def easypost_address(api_key, attributes = {})
        address_attributes = {
          street1: address1,
          street2: address2,
          city: city,
          zip: zipcode,
          phone: phone,
        }.merge(attributes)

        address_attributes[:company] = company if respond_to?(:company)
        address_attributes[:name] = respond_to?(:full_name) ? full_name : name
        address_attributes[:state] = state ? state.abbr : state_name
        address_attributes[:country] = country.try(:iso)

        key_digest = Digest::SHA256.hexdigest(api_key.to_s)
        cache_key = "easypost_address:#{key_digest}:#{Digest::SHA256.hexdigest(address_attributes.sort.to_h.to_json)}"

        Rails.cache.fetch(cache_key, expires_in: 2.weeks) do
          address = ::EasyPost::Client.new(api_key: api_key).address.create(address_attributes)
          Rails.logger.info("EasyPost shipment address created: #{address.id}")
          address.to_hash
        end
      end

      def display_name
        [
          full_name,
          company,
          address1,
          address2,
          "#{city}, #{state_text} #{zipcode}",
          country.to_s,
        ].compact_blank.map { |attribute| ERB::Util.html_escape(attribute) }.join(" / ")
      end

      def easypost_address_validate
        return true unless SpreeEasypost::Config.validate_address_with_easypost
        api_key = ::Spree::EasypostSetting.first&.key
        return { errors: "EasyPost api_key is missing" } if api_key.nil?

        ep_address = easypost_address(api_key, { verify: ["zip4", "delivery"] })
        verifications = ep_address.verifications

        if success?(verifications.delivery) && success?(verifications.zip4)
          { suggestions: address_suggestions(ep_address) }
        else
          { errors: get_errors(verifications) }
        end
      end

      def require_phone?
        @validate_phone = true if @validate_phone.nil?
        ::Spree::Config[:address_requires_phone] && validate_phone
      end

      private

      # if validate_address is set to true
      # then we do not want to run Spree's built-in validations
      def use_spree_validations?
        # Here we do not get store to retrieve easypost settings
        true
      end
    end
  end
end

Spree::Address.prepend(AxelSpree::Spree::AddressDecorator)
Spree::StockLocation.prepend(AxelSpree::Spree::AddressDecorator)
