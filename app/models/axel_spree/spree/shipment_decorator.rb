# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module ShipmentDecorator
      include ::Spree::Admin::LineItemsExtHelper
      def self.prepended(base)
        base.attr_accessor(:current_user)
        base.attr_accessor(:action_place)
        base.attr_accessor(:action)
        base.belongs_to(:scan_form, class_name: "Spree::Scan<PERSON>orm")
        base.belongs_to(:order_package, class_name: "Spree::OrderPackage")
        base.belongs_to(:email_template, class_name: ::Spree::EmailTemplate.to_s, optional: true)

        base.state_machine.before_transition(
          to: :shipped,
          do: :buy_easypost_rate,
        )

        base.state_machine.after_transition(
          to: :shipped,
          do: :check_line_items_ext,
        )

        base.after_update(:check_line_items_ext_after_update)

        base.has_one(
          :buyer_selected_shipping_rate,
          -> {
            where(buyer_selected: true).order(:cost)
          },
          class_name: ::Spree::ShippingRate.to_s,
          dependent: :destroy,
          inverse_of: :shipment,
        )
        base.has_many(:stock_item_units, dependent: :nullify)
        base.has_one_attached :shipping_label
        base.acts_as_paranoid

        base.attr_accessor(:buy_postage_shipment)
      end

      def easypost_client
        @easypost_client ||= ::EasyPost::Client.new(api_key: api_key)
      end

      def easypost_shipment
        if order_package_selected_easy_post_shipment_id
          @ep_shipment ||= easypost_client.shipment.retrieve(order_package_selected_easy_post_shipment_id)
        elsif selected_easy_post_shipment_id
          @ep_shipment ||= easypost_client.shipment.retrieve(selected_easy_post_shipment_id)
        else
          @ep_shipment = to_package.easypost_shipment
        end
      end

      def api_key
        @api_key ||= order.store.easypost_setting.key
        @api_key
      end

      def refresh_rates(shipping_method_filter = ::Spree::ShippingMethod::DISPLAY_ON_FRONT_END, parcel = nil)
        return shipping_rates if shipped?
        return [] unless can_get_rates?

        order.tracker_logs.create(details: "Shipping Rates has been refreshed for shipment - #{number}")

        # StockEstimator.new assignment below will replace the current shipping_method
        original_shipping_method_id = shipping_method.try(:id)

        if to_package.shipping_categories.map(&:name).include?("Free Shipping")
          # Clone the free shipping rate and assign it back to the shipping_rate generated by easypost.
          free_shipping_rate = shipping_rates.map { |a| a if a.shipping_method.name == "Free Shipping" }.first.dup
        end

        refreshed_shipping_rates = ::Spree::Stock::Estimator.new(order)
          .shipping_rates(to_package, shipping_method_filter, parcel)
        self.shipping_rates = refreshed_shipping_rates

        # Save the free shipping
        if free_shipping_rate && to_package.shipping_categories.map(&:name).include?("Free Shipping") && 
           shipping_rates.map { |rate| rate if rate.shipping_method.name == "Free Shipping" }.first.nil?

          # Check for existing shipping rates with the same `shipment_id` and `shipping_method_id`
          existing_rate = shipping_rates.find_by(shipment_id: free_shipping_rate.shipment_id, shipping_method_id: free_shipping_rate.shipping_method_id)

          # Destroy the existing rate if present
          existing_rate&.destroy

          # Update the free_shipping_rate
          free_shipping_rate.update!(
            buyer_selected: true,
            selected: false,
          )
        end

        if free_shipping_rate && to_package.shipping_categories.map(&:name).include?("Free Shipping") &&
            refreshed_shipping_rates.present? &&
            !refreshed_shipping_rates.detect { |it| it.shipping_method.name == "Free Shipping" }
          shipping_rates << free_shipping_rate.reload
        end

        if shipping_method
          selected_rate = shipping_rates.reload.detect do |rate|
            if original_shipping_method_id
              rate.shipping_method_id == original_shipping_method_id
            else
              rate.selected
            end
          end
          save!
          self.selected_shipping_rate_id = selected_rate.id if selected_rate
          reload
        end

        shipping_rates
      end

      def buy_easypost_rate(optional = nil)
        # return incase this is getting shipped from buy_postage process.
        return if buy_postage_shipment

        optional = optional.is_a?(Hash) ? optional : {}
        if order.store.easypost_setting&.buy_postage_when_shipped && selected_shipping_rate.present? && selected_easy_post_rate_id.present?
          raise "can only buy postage when order is ready" unless state == "ready" || state == "shipped"

          # regenerate the rates so we get updated data
          # shiping rates refreshed manually before buy postage
          refresh_rates(::Spree::ShippingMethod::DISPLAY_ON_BACK_END, optional)
          update_amounts
          @ep_shipment = nil

          # Get the selected rate
          rate = easypost_shipment.rates.find do |rate|
            rate.id == selected_easy_post_rate_id
          end

          # Purchase the postage unless it was purchased before
          unless tracking?
            bought_shipment = if insure.present?
              easypost_client.shipment.buy(easypost_shipment.id, rate: rate, insurance: insure_for_amount)
            else
              easypost_client.shipment.buy(easypost_shipment.id, rate: rate)
            end
            self.tracking = bought_shipment.tracking_code
            details = "Tracking Label with tracking number - #{tracking} has been generated for order - #{order.number} \
            and shipment - #{number} and return insurance: #{bought_shipment.insurance}"
            order.tracker_logs.create(details:)
            self.tracking_label = bought_shipment.postage_label.label_url
            self.insure_for_amount = bought_shipment.insurance
            bought_shipment.fees&.each do |fee|
              self.cost_to_insure = fee.amount if fee.type == "InsuranceFee"
            end
          end
        end
      end

      def shipping_cost_with_tax
        tax_total + cost
      end

      def tracking_url
        # TODO: Need to make an approach for shipment specific url
        # @tracking_url = "https://track.easypost.com/djE6dHJrXzE2NWYzYTBhYWQwNzQ3MjdhYjgxYjRmOTY5ZDFmMDli"
        @tracking_url ||= shipping_method&.build_tracking_url(tracking)
      end

      def transfer_to_location(variant, quantity, stock_location, new_variant = nil)
        transfer_to_shipment(
          variant,
          quantity,
          order.shipments.build(stock_location: stock_location),
          new_variant,
        )
      end

      def transfer_to_shipment(variant, quantity, shipment_to_transfer_to, new_variant = nil)
        ::Spree::FulfilmentChanger.new(
          current_stock_location: stock_location,
          desired_stock_location: shipment_to_transfer_to.stock_location,
          current_shipment: self,
          desired_shipment: shipment_to_transfer_to,
          variant: variant,
          new_variant: new_variant.nil? ? variant : new_variant,
          quantity: quantity,
        )
      end

      def update_shipment_status_on_ebay
        order = self.order
        tracking = reload.tracking
        if order.sale_channel_metadata.present? && tracking.present? && order.channel == "eBay"
          CompleteSaleJob.perform_later(order, tracking)
          details = "CompleteSaleJob has been triggered successfully for order - #{order.number} and shipment - #{number}
          and tracking - #{tracking}"
          order.tracker_logs.create(details:)
        end
      end

      def after_cancel
        super
        stock_item_units.update(state: "stock", line_item_id: nil, shipment_id: nil)
        stock_item_units.each { |unit| record_activity_log(unit) }
      end

      def after_resume
        super
        # stock_item_units.update(state: "locked")
      end

      def after_ship
        update_shipment_status_on_ebay
        ship_walmart_order

        if order.store.number_tracking
          stock_item_units.update(state: "shipped")
          stock_item_units.each { |unit| record_activity_log(unit) }
        else
          manifest.each do |item|
            stock_item = ::Spree::StockItem.find_by(stock_location_id: stock_location_id, variant_id: item.variant.id)
            next unless stock_item

            stock_item.stock_item_units.order(created_at: :asc).first(item.quantity).each do |stock_item_unit|
              stock_item_unit.update!(state: :shipped, shipment_id: id)
              record_activity_log(stock_item_unit)
            end
          end
        end
        super
      end

      def ship_walmart_order
        Rails.logger.info("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ After ship called ship Walmart order method@@@@@@@@@@@@@@@@:")
        oauth_application = ::Spree::SaleChannel.find_by(id: order.sale_channel_id)&.oauth_application
        return if oauth_application.blank? || !shipped? || order.channel != "walmart" || !any_shipment_shipped?(order_package)

        response = Walmart::OrderApis::ShipOrder.new(order.store_id, oauth_application.id).ship_order(order, order_package)

        if response["order"].blank?
          errors.add(:base, "No shipments are marked as shipped.")
          throw(:abort) # Halts the state transition process
        end
      end

      def any_shipment_shipped?(order_package)
        order_package.shipments.any?(&:shipped?)
      end

      def default_email
        Rails.logger.info("Resource state before reload and ship default email method call}")
        return email_template if email_template.present?

        sale_channel_template = order.sale_channel_id.present? && ::Spree::SaleChannel.find_by(id: order.sale_channel_id)&.email_template
        sale_channel_template || ::Spree::EmailTemplate.current_store(store.id).default_shipping_email.last
      end

      def check_line_items_ext
        order = self.order
        return unless order.present?
        Rails.logger.info("=====entering ShipmentDecorator check_line_items_ext -1: #{order&.id}")
        if order&.line_items.present?
          Rails.logger.info("=====entering ShipmentDecorator check_line_items_ext 0: #{order&.id}")
          order&.line_items.each do |item|
            Rails.logger.info("=====entering ShipmentDecorator check_line_items_ext 1: #{item&.id}")
            line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: item&.id)
            process_line_items_ext(line_items_ext, item, order) if line_items_ext
          end
        end
      end

      def check_line_items_ext_after_update
        order = self.order
        return unless order.present?
        Rails.logger.info("=====entering ShipmentDecorator check_line_items_ext_after_update -1: #{order&.id}")
        if order&.line_items.present?
          Rails.logger.info("=====entering ShipmentDecorator check_line_items_ext_after_update 0: #{order&.id}")
          order&.line_items.each do |item|
            Rails.logger.info("=====entering ShipmentDecorator check_line_items_ext_after_update 1: #{item&.id}")
            line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: item&.id)
            process_line_items_ext(line_items_ext, item, order) if line_items_ext
          end
        end
      end

      private

      def selected_easy_post_rate_id
        selected_shipping_rate.easy_post_rate_id
      end

      def selected_easy_post_shipment_id
        selected_shipping_rate&.easy_post_shipment_id
      end

      def order_package_selected_easy_post_shipment_id
        order_package&.selected_easy_post_shipment_id
      end

      def record_activity_log(stock_item_unit)
        return unless current_user

        product = stock_item_unit.stock_item&.product

        ::Spree::ActivityLog.create!(
          loggable: stock_item_unit,
          user_id: current_user.id,
          action: action,
          role: current_user.spree_roles.first&.name,
          date: Time.current,
          email: current_user.email,
          action_place: action_place,
          action_name: stock_item_unit.number,
          product_id: product&.id
        )
      end
    end
  end
end

Spree::Shipment.prepend(AxelSpree::Spree::ShipmentDecorator)
