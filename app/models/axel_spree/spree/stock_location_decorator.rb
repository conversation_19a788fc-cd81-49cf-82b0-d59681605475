# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module StockLocationDecorator
      def self.prepended(base)
        base.belongs_to(:store, class_name: "Spree::Store")
        base.has_many(
          :sections,
          class_name: "StockLocationSection",
          foreign_key: :stock_location_id,
          dependent: :destroy,
          inverse_of: :stock_location,
        )
        base.has_many(
          :stock_item_units,
          through: :stock_items,
        )
      end

      def display_name
        [
          name,
          address1,
          address2,
          "#{city}, #{state_text} #{zipcode}",
          country.to_s,
        ].compact_blank.map { |attribute| ERB::Util.html_escape(attribute) }.join(" / ")
      end

      def to_option
        { id: id, text: name }
      end
    end
  end
end

Spree::StockLocation.prepend(AxelSpree::Spree::StockLocationDecorator)
