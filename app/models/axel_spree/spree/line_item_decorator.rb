# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module LineItemDecorator
      include ::Spree::Admin::LineItemsExtHelper
      def self.prepended(base)
        base.has_many(:stock_item_units, dependent: :nullify)
        # rubocop:disable Rails/HasManyOrHasOneDependent
        base.has_one(:subscription)
        base.has_one(:listing)
        base.has_one(:line_items_ext, foreign_key: 'id', class_name: 'LineItemsExt')
        # rubocop:enable Rails/HasManyOrHasOneDependent
        base.belongs_to(:listing, optional: true)

        base.before_save(:update_order_sale_channel_id)
        base.after_update(:check_line_items_ext_after_update)

        old_copy_price = base.instance_method(:copy_price)

        define_method(:copy_price) do
          old_copy_price.bind_call(self)
          return if order.sale_channel_metadata.present?
          return unless variant
          
          if storefront_sale_channel?
            if subscription_enabled
              self.price = is_recurring ? subscription_recurring_price : subscription_first_price
            else
              self.price = get_line_item_price

              if changed? && ["quantity", "price"].any? { |key| changes.key?(key) }
                # pack_size = listing&.pack_size_value || 1
                pack_quantity = quantity / pack_size
                vprice = listing.listing_inventories&.find_by(variant_id:)&.volume_price(pack_quantity, order.user)

                self.price = vprice if price.present? && vprice <= get_line_item_price
                self.price ||= get_line_item_price
                return
              end
            end
          elsif changed? && ["quantity", "price"].any? { |key| changes.key?(key) }
            vprice = variant.volume_price(quantity, order.user)

            self.price = vprice if price.present? && vprice <= variant.price
            self.price ||= variant.price
            return
          end

          self.price ||= get_line_item_price || variant.price
        end
      end

      def amount
        # price * (quantity / (listing&.pack_size_value || 1))
        price * (quantity / pack_size)
      end
      
      def subscription_first_price
        return get_line_item_price if listing.subscription_details.blank?
        listing.subscription_details[variant.id.to_s]["first_price_per_item"]&.to_f
      end

      def subscription_recurring_price
        return get_line_item_price if listing.subscription_details.blank?
        listing.subscription_details[variant.id.to_s]["recurring_price_per_item"]&.to_f
      end

      def get_line_item_price
        listing&.listing_inventories&.find_by(variant_id:)&.price
      end

      def subscription_price
        listing.subscription_details[variant.id.to_s]["first_price_per_item"]&.to_f
      end

      def storefront_sale_channel?
        listing&.sale_channel&.brand == 'storefront'
      end

      def sku_in_walmart_listing(variant_id)
        if listing&.walmart_variant_id_sku_hash.present?
          listing&.walmart_variant_id_sku_hash[variant_id]
        else
          nil
        end
      end

      def update_order_sale_channel_id
        return unless order.present? && listing.present? && listing.sale_channel.present?

        if order.sale_channel.nil? || order.channel.blank?
          order.update_columns(sale_channel_id: listing.sale_channel_id, channel: listing.sale_channel.brand_to_order_channel)
        end
      end

      def check_line_items_ext_after_update
        order = self.order
        return unless order.present?
        Rails.logger.info("=====entering LineItemDecorator check_line_items_ext_after_update -1: #{order&.id}")
        if order&.line_items.present?
          Rails.logger.info("=====entering LineItemDecorator check_line_items_ext_after_update 1: #{self&.id}")
          line_items_ext = ::Spree::LineItemsExt.find_or_create_by(id: self&.id)
          process_line_items_ext(line_items_ext, self, order) if line_items_ext
        end
      end

      ::Spree::LineItem.prepend(self)
    end
  end
end
