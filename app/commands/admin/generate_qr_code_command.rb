# frozen_string_literal: true

module Admin
  class GenerateQrCodeCommand
    prepend SimpleCommand

    attr_reader :store, :user, :request

    def initialize(store:, user:, request:)
      @store = store
      @user = user
      @request = request
    end

    def call
      case request.parameters[:id]
      when "upc_scanner"
        generate_upc_scanner_qrcode
      when "image_scanner"
        generate_image_qrcode
      else
        raise "Unsupported QR code type"
      end
    end

    private

    def generate_upc_scanner_qrcode
      url = "https://#{request.host_with_port}/api/v2/storefront/barcode_readers/scan"
      query_string = sanitized_params.map { |k, v| "#{k}=#{v}" }.join("&")
      url = "#{url}?#{query_string}"

      RQRCode::QRCode.new(url)
    end

    def generate_image_qrcode
      url = "https://#{request.host_with_port}/old_admin/image_uploader/new"
      query_string = sanitized_params.map { |k, v| "#{k}=#{v}" }.join("&")
      url = "#{url}?#{query_string}"

      RQRCode::QRCode.new(url)
    end

    def sanitized_params
      {
        user_token: user&.user_token,
        type: request.parameters[:type],
        store_id: store.id,
        sale_channel_id: request.parameters[:sale_channel_id],
        order_id: request.parameters[:order_id],
        selector: request.parameters[:selector],
        resource_id: request.parameters[:resource_id],
        resource: request.parameters[:resource],
        variant_id: request.parameters[:variant_id],
      }.compact_blank
    end
  end
end
