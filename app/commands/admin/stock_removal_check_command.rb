# frozen_string_literal: true

module Admin
  class StockRemovalCheckCommand
    prepend SimpleCommand

    attr_reader :product, :stock_item_units

    def initialize(product:, stock_item_units:)
      @product = product
      @stock_item_units = stock_item_units
    end

    def call
      variant_stocks = Spree::StockItemUnit.joins(stock_item: :variant).where(spree_variants: { product_id: product.id })
      variant_stocks = variant_stocks.stock.group("spree_stock_items.variant_id").sum("spree_stock_item_units.pack_size")

      variant_stocks.each do |variant_id, count|
        remaining = count - variant_stock_removes[variant_id].to_i

        variant = Spree::Variant.find(variant_id)
        current_listed = variant.current_listing_qty
        on_hold = variant.total_committed

        next if remaining >= current_listed + on_hold

        max_allowed = count - current_listed - on_hold
        errors.add(:base, "Cannot remove stock for #{variant.sku} more than #{max_allowed} since it is on hold or listed on sale channel")
      end
    end

    private

    def variant_stock_removes
      @variant_stock_removes ||= build_variant_stock_removes
    end

    def build_variant_stock_removes
      id_list = stock_item_units.map(&:id)
      scope = Spree::StockItemUnit.joins(stock_item: :variant)
      scope = scope.where(spree_stock_item_units: { id: id_list })
      scope.group("spree_stock_items.variant_id").sum("spree_stock_item_units.pack_size")
    end
  end
end
