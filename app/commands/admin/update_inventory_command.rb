# frozen_string_literal: true

module Admin
  class UpdateInventoryCommand
    prepend SimpleCommand

    ENDPOINT = "#{Rails.application.credentials.price_compare_base_url}/updateinventory/"
    HEADERS = {
      "Content-Type" => "application/json",
      "Cache-Control" => "no-cache",
    }

    attr_reader :vin, :qty, :accounttype, :ordernum, :envelopefee, :complimentary, :store

    def initialize(vin:, qty:, order:, store:)
      @vin = vin
      @qty = qty
      @accounttype = order.channel
      @ordernum = order.number
      @envelopefee = order.envelope_fee
      @complimentary = order.complimentary_items
      @store = store
    end

    def call
      if response.status.success?
        {
          message: "Inventory updated successfully",
          success: true,
          data: response_json,
        }
      else
        errors.add(:base, "API call failed with status #{response.status}")
        { message: "Failed to update inventory", success: false }
      end
    end

    private

    def response
      @response ||= HTTP.headers(auth_headers).post(ENDPOINT, json: request_body)
    end

    def response_json
      @response_json ||= JSON.parse(response.body.to_s)
    end

    def request_body
      {
        vin: vin,
        qty: qty,
        accounttype: accounttype,
        ordernum: ordernum,
        envelopefee: envelopefee,
        complimentary: complimentary,
      }.compact
    end

    def generate_x_auth_token
      oauth_application = store.oauth_application.find_by(name: "pricecompare_key")
      return "" if oauth_application.blank?

      req_json = {
        grant_type: "client_credentials",
        client_id: oauth_application.uid,
        client_secret: oauth_application.secret,
        scope: "admin read",
      }

      res = HTTP.headers(HEADERS).post("https://#{@store.url}/spree_oauth/token", json: req_json)
      if res.status.success?
        JSON.parse(res.body.to_s)["access_token"]
      else
        errors.add(:base, "Failed to retrieve authorization token")
        nil
      end
    end

    def auth_headers
      HEADERS.merge("x-authorization" => generate_x_auth_token)
    end
  end
end
