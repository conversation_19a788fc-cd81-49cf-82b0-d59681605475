# frozen_string_literal: true

module Admin
  class GetPricecompareInfoCommand
    prepend SimpleCommand

    ENDPOINT = "https://pricecompare.axel.org/pdid/productbyinventory"
    HEADERS = {
      "x-api-key" => ENV["SYGIC_API_KEY"],
      "cache-control" => "no-cache",
    }

    attr_reader :vin

    def initialize(vin:)
      @vin = vin
    end

    def call
      unless response.status.success?
        errors.add(:base, "Api call failed")
        return { message: "VIN code not found", success: false }
      end

      if response_json.blank?
        errors.add(:base, "Product not found")
        return { message: "VIN code not found", success: false }
      end

      {
        message: "Product scanned successfully",
        success: true,
        vendor_receipt_number:,
        vendor_receipt_date:,
        expiry_date:,
        vendor_inventory_cost:,
        pack_size:,
        quantity: 1,
        expiry_type:,
        data: response_json,
      }
    end

    private

    def response
      @response ||= HTTP.headers(HEADERS).follow.get(ENDPOINT, params: { iid: vin })
    end

    def response_json
      @response_json ||= JSON.parse(response.body.to_s).first&.with_indifferent_access
    end

    def pack_size
      response_json[:quantity]
    end

    def vendor_receipt_number
      response_json[:receipt_number]
    end

    def vendor_inventory_cost
      response_json[:total_amount]
    end

    def vendor_receipt_date
      str = [
        response_json[:year],
        response_json[:month],
        response_json[:day],
      ].join("-")

      Date.strptime(str, "%Y-%m-%d").strftime("%Y-%m-%d")
    end

    def expiry_date
      str = response_json[:expirydate].to_s
      return if str.blank?

      parts = str.split("-")

      date = if parts.length == 2
        Date.strptime(str, "%b-%y").end_of_month
      else
        Date.strptime(str, "%m-%d-%Y")
      end

      date.strftime("%Y-%m-%d")
    end

    def expiry_type
      "fixed"
    end
  end
end
