# frozen_string_literal: true

module Admin
  class TransferStockCommand
    prepend SimpleCommand

    attr_reader :store, :source_location, :destination_location, :reference, :variants

    def initialize(store:, source_location:, destination_location:, reference:, variants:)
      @store = store
      @source_location = source_location
      @destination_location = destination_location
      @reference = reference
      @variants = variants
    end

    def call
      ApplicationRecord.transaction do
        stock_transfer.save!

        variants.each_pair do |variant_id, quantity|
          stock_item_units = unstock_from_source_location!(variant_id: variant_id, quantity: quantity)
          restock_to_destination_location!(variant_id: variant_id, stock_item_units: stock_item_units)
        end

        stock_transfer
      end
    end

    private

    def unstock_from_source_location!(variant_id:, quantity:)
      unless source_location
        return Array.new(quantity) { Spree::StockItemUnit.new }
      end

      scope = source_location.stock_items
      stock_item = scope.where(variant_id: variant_id).order(:id).first || scope.create!(variant_id: variant_id)

      stock_item.stock_movements.create!(quantity: -quantity, originator: stock_transfer)

      stock_item_units = stock_item.stock_item_units.stock.order(:id).first(quantity)
      raise "Not enough stock to transfer" unless stock_item_units.size == quantity

      stock_item_units
    end

    def restock_to_destination_location!(variant_id:, stock_item_units:)
      scope = destination_location.stock_items
      stock_item = scope.where(variant_id: variant_id).order(:id).first || scope.create!(variant_id: variant_id)

      stock_item.stock_movements.create!(quantity: stock_item_units.size, originator: stock_transfer)

      stock_item.stock_item_units << stock_item_units
    end

    def stock_transfer
      @stock_transfer ||= build_stock_transfer
    end

    def build_stock_transfer
      store.stock_transfers.build(
        reference: reference,
        source_location: source_location,
        destination_location: destination_location,
      )
    end
  end
end
