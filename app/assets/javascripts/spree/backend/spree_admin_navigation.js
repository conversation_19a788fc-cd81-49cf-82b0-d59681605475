//= require spree/backend
const ADMIN_URL = '/old_admin'

document.addEventListener('spree:load', function () {
  const url = window.location.href.split('admin/')[1].split('/')[0].split('?')[0]

  /* init header */
  const initHeader = () => {
    const navbar = document.querySelector('#navbar')
    const navtab = document.querySelector('#nav-tab')
    const tabActive = navtab?.querySelector(`a[href='#${url}']`)
    const key = localStorage.getItem('activeTab')
    const links = {
      dashboards: 'dashboards',
      products: 'products',
      listings: 'listings',
      orders: 'orders',
      customer: 'users',
      reports_sidebar: 'analysis/promotional_cost?report_category=promotion_analysis',
      store: 'general_settings/edit',
      settings: 'gallery_items'
    }

    Array.from(navtab?.children || []).forEach(el => {
      el.querySelector('a')?.classList.remove('active')
    })

    if (tabActive) {
      localStorage.setItem('activeTab', `#${url}`)
      tabActive.classList.add('active')
    } else if (key) {
      navtab?.querySelector(`a[href='${key}']`)?.classList.add('active')
    } else if (document.querySelector('.header.logged-in')) {
      localStorage.setItem('activeTab', '#orders')

      location.href = `${ADMIN_URL}/${links.orders}`
    }

    // bind nav menu click event
    navtab?.querySelectorAll('.nav-item-text').forEach(el => {
      el.addEventListener('click', evt => {
        const target = evt.currentTarget
        const href = target.getAttribute('href') || ''

        if (links[href.replace('#', '')]) {
          localStorage.setItem('activeTab', href)

          location.href = `${ADMIN_URL}/${links[href.replace('#', '')]}`
        }

        evt.stopPropagation()
        evt.preventDefault()
      })
    })

    // click right icon
    navtab?.querySelectorAll('.sub-ricon').forEach(el => {
      el.addEventListener('click', evt => {
        const target = evt.currentTarget
        const href = target.parentNode?.getAttribute('href') || ''

        evt.stopPropagation()
        evt.preventDefault()

        if (href) {
          document.querySelector('#main-sidebar').classList.remove('d-none')
          document.querySelector('#menu-text').innerText = target.previousElementSibling.innerText

          initAside(href)
        }
      })
    })

    // toggle sidebar for mobile side
    navbar?.querySelector('.navbar-close')?.addEventListener('click', () => {
      document.querySelector('#multi-backdrop')?.click()
    })
  }

  /* init event */
  const initEvent = () => {
    // close aside menu for mobile
    document.querySelector('#menu-back')?.addEventListener('click', () => {
      document.querySelector('#main-sidebar')?.classList.add('d-none')
    })

    document.querySelector('#sidebar-close')?.addEventListener('click', () => {
      document.querySelector('#multi-backdrop')?.click()
      document.querySelector('#main-sidebar')?.classList.add('d-none')
    })

    document.querySelector('#multi-backdrop')?.addEventListener('click', () => {
      document.querySelector('#main-sidebar')?.classList.add('d-none')
    })
  }

  /* init layout */
  const initLayout = () => {
    if (['dashboards', 'password'].includes(url)) {
      document.getElementById('custom-show').classList.add('d-none')
      document.getElementById('custom-main-part').classList.add('col-12', 'main', 'pt-2')
    } else {
      document.getElementById('custom-main-part').classList.add('col-12', 'col-lg-9', 'col-xl-10', 'offset-lg-3', 'offset-xl-2', 'main', 'pt-2')
    }
  }

  /* init aside menu */
  const initAside = (activeTab) => {
    let menuTab = document.querySelector(`.tab-pane#${url}`)

    if (activeTab) {
      menuTab = document.querySelector(`.tab-pane${activeTab}`)
    } else {
      if (!menuTab) {
        activeTab = localStorage.getItem('activeTab')

        if (activeTab) {
          menuTab = document.querySelector(`.tab-pane${activeTab}`)
        }
      }
    }

    if (menuTab) {
      document.querySelectorAll('#side-menu .tab-pane').forEach(el => {
        el.classList[el === menuTab ? 'add' : 'remove']('active')
      })
    } else {
      document.querySelectorAll('#side-menu .tab-pane').forEach(el => {
        let isActive = false

        el.querySelectorAll('a').forEach(a => {
          const href = (a.getAttribute('href') || '').split('?')[0]

          if (href === location.pathname) {
            isActive = true
          }
        })

        el.classList[isActive ? 'add' : 'remove']('active')
      })
    }

    // check if the aside is not selected, then selected it by pathname
    const activeSide = document.querySelector('#side-menu .tab-pane.active')

    if (activeSide) {
      const current = activeSide.querySelector('.sidebar-menu-item.selected')
      const node = activeSide.querySelector(`.sidebar-submenu-item[href="${location.pathname}"]`)

      if (!current || (node && !current.contains(node))) {
        Array.from(activeSide.querySelectorAll('.sidebar-submenu-item')).forEach(a => {
          const parent = a.parentNode
          const href = (a.getAttribute('href') || '').split('?')[0]

          if (href === location.pathname) {
            if (href !== '/old_admin/metabase_reports/metabase_report') {
              parent.classList.add('selected')
            }
          } else if (parent.classList.contains('selected')) {
            parent.classList.remove('selected')
          }
        })
      }
    }
  }

  /* aside collapse */
  const handleSidebarCollapse = () => {
    // return false if it does not contain an aside
    if (document.querySelector('.nav-item-text.hide-sidemenu.active')) {
      return false
    }

    const KEY = 'asideExpanded'
    const sidebar = document.querySelector('#main-sidebar')
    const content = document.querySelector('#custom-main-part')
    const btn = document.querySelector('#sidebar-collapse')

    if (sidebar && btn) {
      const isExpanded = localStorage.getItem(KEY)

      if (isExpanded !== '0') {
        const width = window.innerWidth || document.body.clientWidth

        btn.classList.remove('is-collapse')
        sidebar.classList.remove('is-collapse')
        content.classList.remove('is-collapse')

        btn.style.left = `calc(${width >= 1200 ? '16.66666667%' : '25%'} - 16px)`
      } else {
        btn.classList.add('is-collapse')
        sidebar.classList.add('is-collapse')
        content.classList.add('is-collapse')

        btn.style.left = '4px'
      }

      btn.onclick = function () {
        localStorage.setItem(KEY, this.classList.contains('is-collapse') ? '1' : '0')

        handleSidebarCollapse()
      }
    }
  }

  initHeader()
  initEvent()
  initLayout()
  initAside()
  handleSidebarCollapse()

  window.addEventListener('resize', () => {
    initHeader()
    handleSidebarCollapse()
  })
})

window.showLoading = () => {
  const el = document.querySelector('#page-loading')

  el && el.classList.remove('d-none')
}

window.hideLoading = () => {
  const el = document.querySelector('#page-loading')

  el && el.classList.add('d-none')
}

// freeze table header
document.addEventListener('spree:load', () => {
  const freezeTableHeader = () => {
    if (window.jQuery) {
      const tables = window.jQuery('table.table')

      tables.each((_, table) => {
        const $t = $(table)
        const key = 'data-freeze-initiated'

        if ($t.find('thead').length && !$t.parents('.modal').length && !$t.parents('.table').length) {
          try {
            $t.freezeHeader().attr(key, '1')
          } catch (error) {
            console.warn(error)
          }
        } else {
          $t.attr('data-freeze-skipped', '1')
        }
      })
    } else {
      const script = document.createElement('script')

      script.src = 'https://cdn.staticfile.org/jquery/3.7.0/jquery.min.js'
      script.onload = freezeTableHeader

      document.body.appendChild(script)
    }
  }

  setTimeout(freezeTableHeader, 1000)
})
