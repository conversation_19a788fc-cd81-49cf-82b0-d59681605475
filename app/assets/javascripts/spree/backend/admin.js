/* global Spree, AUTH_TOKEN, order_number, show_flash */

document.addEventListener('spree:load', function () {
  /**
    OBSERVE FIELD:
  **/
  $('.observe_field').on('change', function () {
    const target = $(this).data('update')
    $(target).hide()
    $.ajax({
      dataType: 'html',
      url: $(this).data('base-url') + encodeURIComponent($(this).val()),
      type: 'GET'
    }).done(function (data) {
      $(target).html(data)
      $(target).show()
    })
  })

  /**
    ADD FIELDS
  **/
  let uniqueId = 1
  $('.spree_add_fields').click(function () {
    const target = $(this).data('target')
    const newTableRow = $(target + ' tr:visible:last').clone()
    const newId = new Date().getTime() + (uniqueId++)
    newTableRow.find('input, select').each(function () {
      const el = $(this)
      el.val('')
      el.prop('id', el.prop('id').replace(/\d+/, newId))
      el.prop('name', el.prop('name').replace(/\d+/, newId))
    })

    // When cloning a new row, set the href of all icons to be an empty "#"
    // This is so that clicking on them does not perform the actions for the
    // duplicated row
    newTableRow.find('a').each(function () {
      const el = $(this)
      el.prop('href', '#')
    })
    $(target).prepend(newTableRow)
  })

  $('.listing_volume_prices_add_fields').click(function () {
    const target = $(this).data('target')
    const newTableRow = $(target + ' tr:visible:last').clone()
    const newId = new Date().getTime() + (uniqueId++)
    newTableRow.find('input, select').each(function () {
      const el = $(this)
      if (el.prop('class') != 'variant_id') {
        el.val('')
      }
      el.prop('id', el.prop('id').replace(/\d+/, newId))
      el.prop('name', el.prop('name').replace(/\d+/, newId))
    })

    // When cloning a new row, set the href of all icons to be an empty "#"
    // This is so that clicking on them does not perform the actions for the
    // duplicated row
    newTableRow.find('a').each(function () {
      const el = $(this)
      el.prop('href', '#')
    })
    $(target).prepend(newTableRow)
  })

  /**
    DELETE RESOURCE
  **/
  $('body').on('click', '.delete-resource', function () {
    const el = $(this)
    if (confirm(el.data('confirm'))) {
      $.ajax({
        type: 'POST',
        url: $(this).prop('href'),
        data: {
          _method: 'delete',
          authenticity_token: AUTH_TOKEN
        },
        dataType: 'script',
        complete: function () {
          el.blur()
        }
      }).done(function () {
        const $flashElement = $('#FlashAlertsContainer span[data-alert-type="success"]')
        if ($flashElement.length) {
          el.parents('tr').fadeOut('hide', function () {
            $(this).remove()
          })
          el.closest('.removable-dom-element').fadeOut('hide', function () {
            $(this).remove()
          })
          const livePreview = document.getElementById('pageLivePreview')
          if (livePreview) { livePreview.contentWindow.location.reload() }
        }
      }).fail(function (response) {
        show_flash('error', response.responseText)
      })
    } else {
      el.blur()
    }
    return false
  })

  /**
    REMOVE FIELDS
  **/
  $('body').on('click', 'a.spree_remove_fields', function () {
    const el = $(this)
    el.prev('input[type=hidden]').val('1')
    if (el.prop('href').split('/').includes('volume_prices')) {
      el.closest('.fields').hide()
    } else {
      el.closest('.fields').remove()
    }
    if (el.prop('href').substr(-1) === '#') {
      el.parents('tr').fadeOut('hide')
    } else if (el.prop('href')) {
      $.ajax({
        type: 'POST',
        url: el.prop('href'),
        data: {
          _method: 'delete',
          authenticity_token: AUTH_TOKEN
        }
      }).done(function () {
        el.parents('tr').fadeOut('hide', function () {
          if (el.prop('href').split('/').includes('volume_prices')) {
            $(this)[0].nextElementSibling.remove()
          }
          $(this).remove()
        })
      }).fail(function (response) {
        show_flash('error', response.responseText)
      })
    }
    return false
  })

  /**
    SELECT PROPERTIES FROM PROTOTYPE
  **/
  $('body').on('click', '.select_properties_from_prototype', function () {
    $('#busy_indicator').show()
    const clickedLink = $(this)
    $.ajax({
      dataType: 'script',
      url: clickedLink.prop('href'),
      type: 'GET'
    }).done(function () {
      clickedLink.parent('td').parent('tr').hide()
      $('#busy_indicator').hide()
    })
    return false
  })

  /**
    UTILITY
  **/
  window.Spree.advanceOrder = function () {
    $.ajax({
      type: 'PATCH',
      async: false,
      headers: Spree.apiV2Authentication(),
      url: Spree.url(Spree.routes.orders_api_v2 + '/' + order_number + '/advance')
    }).done(function () {
      window.location.reload()
    })
  }
})

/**
  Image Popup for Gallery Items
**/
function getGMTFormat (timezoneOffset) {
  const minutes = timezoneOffset % 60
  console.log('getGMTFormat timezoneOffset:', timezoneOffset)
  let hours = 0
  if (minutes === 0) {
    hours = timezoneOffset / 60
  } else if (timezoneOffset < 0) {
    hours = Math.floor(timezoneOffset / 60) + 1
  } else {
    hours = Math.floor(timezoneOffset / 60)
  }
  const sign = hours > 0 ? '-' : '+'
  const absHours = Math.abs(hours)
  const absMinutes = Math.abs(minutes)

  const formattedMinutes = absMinutes < 10 ? '0' + absMinutes : absMinutes
  const formattedHours = absHours < 10 ? '0' + absHours : absHours
  return '(GMT' + sign + formattedHours + ':' + formattedMinutes + ')'
}

document.addEventListener('DOMContentLoaded', function () {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  document.cookie = `timezone=${timezone}; path=/`
  const timezoneOffset = new Date().getTimezoneOffset()
  const gmtFormat = getGMTFormat(timezoneOffset)
  const tz = document.getElementById('timezone_display')
  tz.innerHTML = `Time Zone: ${gmtFormat} ${timezone}`

  // Function to toggle the visibility of the popup
  function togglePopup (orderId) {
    const popup = document.getElementById(`popup-order-${orderId}`)
    if (popup) {
      popup.classList.toggle('d-none')
    }
  }

  // Event listener to handle clicks on buttons
  document.addEventListener('click', function (event) {
    const button = event.target.closest('[data-order-id]')
    if (button) {
      const orderId = button.getAttribute('data-order-id')
      togglePopup(orderId)
    }

    const expandAllButton = document.querySelector('.expanded-all')
    const collapseAllButton = document.querySelector('.collapsed-all')

    // Check if the clicked element is the expand all button
    if (event.target === expandAllButton) {
      // Get all collapsible elements
      console.log('Inside Role popup expanded')
      const collapsibleItems = document.querySelectorAll('.collapse')
      // Iterate over each collapsible item and expand it
      collapsibleItems.forEach(item => {
        if (!item.classList.contains('show')) {
          item.classList.add('show')
        }
      })
    }

    // Check if the clicked element is the collapse all button
    if (event.target === collapseAllButton) {
      // Get all collapsible elements
      console.log('Inside Role popup collapsed')
      const collapsibleItems = document.querySelectorAll('.collapse')
      // Iterate over each collapsible item and collapse it
      collapsibleItems.forEach(item => {
        if (item.classList.contains('show')) {
          item.classList.remove('show')
        }
      })
    }
  })
})

// eslint-disable-next-line no-unused-vars
function deleteListingImage (event, options) {
  event.preventDefault()
  const imageId = options.ImageId
  const url = options.url
  $.ajax({
    url,
    type: 'DELETE',
    data: { image_id: imageId },
    success: function (response) {
      if (response.status === 'success') {
        if ($('#list_product tbody tr').length === 1) {
          const noImageFoundHtml = '<div class="alert alert-info no-objects-found mt-24 text-center"><div class="alert-info--msg">No Image found</div></div>'
          $('#list_product').parent().html(noImageFoundHtml)
        } else {
          $('#row_' + imageId).remove()
        }
      } else {
        alert('Failed to delete image.')
      }
    },
    error: function (xhr, status, error) {
      console.error('An error occurred:', error)
      alert('An error occurred while deleting the image.')
    }
  })
}

// eslint-disable-next-line no-unused-vars
function getSubdomainField (btn) {
  const subdomainBlock = document.getElementById('user-added-subdomain')
  if (btn.checked) {
    subdomainBlock.classList.remove('d-none')
  } else {
    const subdomainField = document.getElementById('subdomain_field')
    subdomainField.value = ''
    subdomainBlock.classList.add('d-none')
  }
}

// eslint-disable-next-line no-unused-vars
function deleteOrderImage (event, options) {
  event.preventDefault()
  const imageId = options.ImageId
  const url = options.url
  $.ajax({
    url,
    type: 'DELETE',
    data: { image_id: imageId },
    success: function (response) {
      if (response.status === 'success') {
        if ($('#list_product tbody tr').length === 1) {
          const noImageFoundHtml = '<div class="alert alert-info no-objects-found mt-24 text-center"><div class="alert-info--msg">No Image found</div></div>'
          $('#list_product').parent().html(noImageFoundHtml)
        } else {
          $('#row_' + imageId).remove()
        }
      } else {
        alert('Failed to delete image.')
      }
    },
    error: function (xhr, status, error) {
      console.error('An error occurred:', error)
      alert('An error occurred while deleting the image.')
    }
  })
}

/**
  Download Image for Gallery Items
**/

function handleDownloadClick (event) {
  event.preventDefault()

  const link = event.currentTarget
  const filename = link.getAttribute('data-filename')
  const downloadUrl = link.getAttribute('data-download-url')

  downloadFile(downloadUrl, filename)
}

function downloadFile (downloadUrl, filename) {
  const a = document.createElement('a')
  a.href = downloadUrl
  a.download = filename
  a.style.display = 'none'
  document.body.appendChild(a)

  a.click()

  document.body.removeChild(a)
}

document.addEventListener('DOMContentLoaded', function () {
  const downloadIcons = document.querySelectorAll('.download-icon')

  downloadIcons.forEach(icon => {
    icon.addEventListener('click', handleDownloadClick)
  })

  console.log('Download icons set up')
})

/**
  Restricts user to select only one role
**/

function handleRoleCheckboxChange (event) {
  if (event.target.classList.contains('single-role-checkbox')) {
    const checkboxes = document.querySelectorAll('.single-role-checkbox')
    checkboxes.forEach(cb => {
      if (cb !== event.target) {
        cb.checked = false
      }
    })
  }
}

document.addEventListener('change', handleRoleCheckboxChange)

function initializeRoleCheckboxes () {
  const checkboxes = document.querySelectorAll('.single-role-checkbox')
  let checkedCount = 0
  checkboxes.forEach(checkbox => {
    if (checkbox.checked) {
      checkedCount++
      if (checkedCount > 1) {
        checkbox.checked = false
      }
    }
  })
}

document.addEventListener('DOMContentLoaded', initializeRoleCheckboxes)
document.addEventListener('turbolinks:load', initializeRoleCheckboxes)
$(document).on('ajaxSuccess', initializeRoleCheckboxes)
