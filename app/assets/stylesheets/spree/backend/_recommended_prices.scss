@media (min-width: 992px) {
  .modal-lg, .modal-xl {
    max-width: 950px;
  }
}

#recommended-prices, #recommended-prices-sub {
  .info-date {
    color: #1C77FF;
  }
  .text-gray {
    color: #858585;
  }

  table.table {
    font-size: 13px;

    thead th {
      font-family: 'Open Sans Bold';
      background-color: #f7f7f7;
    }
  }

  .btn svg {
    margin-top: -2px;
    margin-bottom: 0px;
    margin-right: 0.125rem;
  }

  .btn-apply, .btn-applied {
    line-height: 20px;
    border-radius: 4px;
    min-width: 100px;
  }

  .btn-apply {
    color: #000;
  }

  .modal-header {
    border-bottom: 2px solid #dee2e6;

    .modal-title {
      font-family: 'Open Sans Bold';
    }
  }

  .modal-body-head {
    overflow-y: hidden;
    padding-top: 0;
    padding-bottom: 5px;
  }

  .modal-footer {
    input[type='checkbox'] {
      transform: scale(1.5);
      opacity: 0.9;
      cursor: pointer;
    }
  }

  .nav-tabs {
    border-bottom: 2px solid #dee2e6;

    .nav-link {
      border: 0;
      border-bottom: 2px solid transparent;
      margin-bottom: -2px;
      padding-left: 2rem;
      padding-right: 2rem;

      &.active {
        font-family: 'Open Sans Bold';
        border-bottom: 2px solid #000;
      }

      &:hover, &:focus {
        color: #383838;
      }
    }
  }

  .alert {
    border-radius: 0;
    margin-bottom: 0;

    .caption {
      text-align: center;
    }

    .value {
      font-family: 'Open Sans Bold';
      font-size: 16px;
      font-weight: 700;
      text-align: center;
    }
  }
}
