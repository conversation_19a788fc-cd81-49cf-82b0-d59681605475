/*
 *= require spree/backend
*/

#contentHeaderRow {
  padding: 0;
  margin-bottom: 24px !important;
}

.main-part {
  font-size: 14px;
  margin-top: 96px !important;
}

.main-part .content-header {
  margin: 0 15px;
}

.main-part .contextual-title {
  font-size: 32px;
  font-weight: bold;
  line-height: 40px;
  color: #383838;
}

.main-part .btn-black {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  font-size: 14px;
  color: #fff;
  margin: 0;
  padding: 0 24px;
  border-radius: 8px;
  border-color: #383838;
  background-color: #383838;
  transition: all linear .2s;
}

.main-part .btn-black[disabled="disabled"] {
  cursor: not-allowed;
  border-color: #B8B8B8;
  background-color: #B8B8B8;
}

.main-part .btn-black.is-small {
  height: 40px;
  padding: 0 16px;
}

.main-part .btn-black:hover,
.main-part .btn-black:focus {
  color: #fff;
  border-color: #383838;
  background-color: #383838;
  opacity: .8;
}

.main-part .btn-black svg {
  margin-right: 8px;
  margin-bottom: 0;
}

.main-part .btn-black+.btn-black {
  margin-left: 24px;
}

.main-part .btn-cancel {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  font-size: 14px;
  color: #7A8599;
  margin: 0;
  padding: 0 24px;
  border-radius: 8px;
  border-color: #858585;
  background-color: #fff;
  transition: all linear .2s;
}

.main-part .btn-cancel[disabled="disabled"] {
  cursor: not-allowed;
  opacity: .5;
}

.main-part .btn-cancel.is-small {
  height: 40px;
  padding: 0 16px;
}

.main-part .btn-cancel:hover,
.main-part .btn-cancel:focus {
  opacity: .65;
}

.main-part .btn-cancel svg {
  margin-right: 8px;
  margin-bottom: 0;
}

.main-part .btn-cancel+.btn-black {
  margin-left: 24px;
}

.main-part .small-img {
	display: inline-flex;
  flex-grow: 0;
  flex-shrink: 0;
	margin: 0;
}

.main-part .small-img + .text {
	margin-left: 16px;
}

.main-part .contextual-side-menu {
  border-radius: 0;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.main-part .contextual-side-menu + .with-sidebar {
  border-radius: 8px;
  border-left: 1px solid #E6EAF2;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding: 24px;
  background-color: #fff;
}

.main-part .main-left-sidebar {
  height: auto;
  padding: 24px 16px 24px 24px;
}

.main-part .main-left-sidebar .nav-link {
  font-size: 16px;
  font-weight: 700;
  padding: 8px;
  line-height: 24px;
  margin-bottom: 8px;
}

.main-part .admin-product-image-container {
  border: none;
  border-radius: 8px;
  background-color: transparent;
}

.main-part .admin-product-image-container.small-img,
.main-part .admin-product-image-container .noimage {
  width: 56px;
  height: 56px;
}

.main-part .admin-product-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1);
  border-radius: 5px;
}

.main-part #insights-div.container-fluid {
  display: none;
}

.page-actions .dropdown-menu {
  z-index: 9999;
}

.main-part .gallery-tabs {
  font-size: 17px;
}

.main-part .gallery-tabs .sidebar-menu-item a {
  padding-right: 1rem;
  border-radius: 8px;
}

.main-part .gallery-tabs .sidebar-menu-item.selected a {
  background-color: #666666;
  color: #fff;
}

.expand-options {
  gap: 15px;
  color: #006bff;
}

.bg-setting {
  height: calc(100vh - 400px);
}

.bg-setting .card-header {
  background-color: #F5F5F5 !important;
}

.bg-setting .card-body input {
  height: 18px;
  width: 18px;
}

.bg-setting .custom-control label {
  font-size: 0;
}

.bg-setting .custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #00f842;
  background-color: #00f842;
  box-shadow: none;
}

.bg-setting .custom-control-label::before {
  background-color: #000;
}

.bg-setting .custom-switch .custom-control-label::after {
  background-color: #fff;
}

@media screen and (max-width: 992px) {
  .main-part {
    margin-top: 78px !important;
  }

  .main-part .contextual-title {
    font-weight: 600;
    font-size: 20px;
    line-height: 32px;
  }

  .main-part .contextual-side-menu + .with-sidebar {
    padding: 16px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  .main-part #contextualSideMenu {
    height: auto;
    max-height: 99vh;
  }

  .main-part .main-left-sidebar {
    padding: 12px 0;
  }
}

/* image editor start */
.image-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-controls,
.image-editor-modal .tui-image-editor-container .tui-image-editor-help-menu {
  background-color: #fff;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-menu > .tui-image-editor-item.active,
.image-editor-modal .tui-image-editor-container .tui-image-editor-help-menu > .tui-image-editor-item.active {
  background-color: #666;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-submenu .tui-image-editor-submenu-item .tui-image-editor-button.apply label,
.image-editor-modal .tui-image-editor-container .tui-image-editor-submenu .tui-image-editor-submenu-item .tui-image-editor-button.cancel label {
  vertical-align: 0;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-menu > .tui-image-editor-item,
.image-editor-modal .tui-image-editor-container .tui-image-editor-help-menu > .tui-image-editor-item {
  display: inline-flex;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-menu > .tui-image-editor-item {
  padding: 4px 8px;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-help-menu > .tui-image-editor-item {
  margin: 2px 4px;
}
.image-editor-modal .tui-image-editor-container.top .tui-image-editor-controls-logo {
  padding: 0 17px;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-checkbox > label > span,
.image-editor-modal .tui-image-editor-container .tui-image-editor-range-wrap label {
  color: #2E384D;
}
.image-editor-modal .tie-mask-apply.apply.active .tui-image-editor-button.apply label {
  color: #2E384D;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-range-value {
  color: #2E384D;
  border: 1px solid #d5d5d5;
  background-color: #f5f5f5;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-crop .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-resize .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-flip .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-rotate .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-shape .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-text .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-mask .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-icon .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-draw .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-filter .tui-image-editor-wrap,
.image-editor-modal .tui-image-editor-container .tui-image-editor-main.tui-image-editor-menu-zoom .tui-image-editor-wrap {
  margin-top: 150px;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-partition > div {
  border-left: 1px solid #ADB8CC;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-header-buttons button,
.image-editor-modal .tui-image-editor-container .tui-image-editor-header-buttons div,
.image-editor-modal .tui-image-editor-container .tui-image-editor-controls-buttons button,
.image-editor-modal .tui-image-editor-container .tui-image-editor-controls-buttons div {
  width: 86px;
  line-height: 38px;
  border-radius: 8px;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-header-buttons .tui-image-editor-save-btn,
.image-editor-modal .tui-image-editor-container .tui-image-editor-controls-buttons .tui-image-editor-save-btn {
  border-color: #383838;
  background-color: #383838;
  color: #fff;
  font-size: 12px;
  margin-left: 6px;
}
.image-editor-modal .tui-image-editor-container .tui-image-editor-header-buttons .tui-image-editor-exit-btn,
.image-editor-modal .tui-image-editor-container .tui-image-editor-controls-buttons .tui-image-editor-exit-btn {
  border-color: #F2460C;
  background-color: #F2460C;
  color: #fff;
  font-size: 12px;
  margin-left: 6px;
}
/* image editor end */

.confirm_order .modal-title {
  font-size: 26px;
  font-weight: 700;
  color: #2E384D;
}
.confirm_order .scan-qr-btn {
  display: flex;
  height: 40px;
  padding: 16px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid #858585;
  background: var#FFF;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0.7px;
  text-transform: capitalize;
}

.confirm_order .modal-header{
  width: 100%;
  padding: 24px;
}

.modal-content.confirm_order {
    border-radius: 16px;
}

.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 3.5rem);
}

.confirm_order .modal-header .ml-auto a input {
    height: 40px;
    padding: 9px 24px;
    border-radius: 8px;
    border: solid 1px #858585;
    background: #FFF;
}

.confirm_order .modal-header .ml-auto a {
    margin-right: 8px;
}

.confirm_order .modal-header .close {
    margin-top: 5px;
    padding-top: 0;
    padding-bottom: 0;
    font-size: 33px;
}
.confirm_order .modal-body {
  padding: 24px;
}

.confirm_order table.table {
  margin-top: 5px;
}

.confirm_order table.table thead th {
  border-color: #EAEAEA;
}

.confirm_order table.table tbody td {
  padding: 16px 8px;
  font-weight: 400;
}

.confirm_order table.table tbody td input {
  width: 20px;
  height: 20px;
}

.confirm_order table.table thead th:first-child {
  width: 65px;
}

.confirm_order .modal-footer {
  padding: 24px;
}
.confirm_order .modal-footer button.btn.btn-secondary {
  border: solid 1px #858585;
  background: #fff;
  color: #383838;
}

.confirm_order .modal-footer button.btn.btn-primary {
  background: #383838  !important;
  border-color: #B8B8B8;
  color: #EAEAEA;
  opacity: 1 !important;
}

.confirm_order .modal-footer button.btn.btn-primary:disabled {
  background: #B8B8B8  !important;
}

/* .confirm_order .modal-footer button.btn.btn-primary:hover{
  background: #383838 !important;
  color: #fff;
} */

.modal-dialog-scrollable{
  margin-top: 110px;
}

.data-p {
  /* margin: 0; */
  font-family: Open Sans;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px; /* Remove default margin to avoid extra space around the text */
}
.select-text {
  font-family: Open Sans;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px;
}

.orange-text {
  color: #FF8C00 !important;
}

.table-active-filters{
  margin-top: 32px;
  margin-bottom: 8px;
}

.table-active-filters .js-filter{
  height: 32px;
  padding: 6px 4px 6px 16px;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  justify-content: center;
  align-items: center;
  gap: 8px;
  display: flex;
  background-color: #858585;
  border-radius: 16px;
  height: auto;
  width: auto;
  margin-left: 16px;
  margin-bottom: 16px;
}


.qr-code-modal h5{
  font-size: 26px;
  font-style: normal;
  font-weight: 700;
}
.qr-code-modal .modal-header{
  padding: 24px;
}
.qr-code-modal.modal-content{
  border-radius: 16px;
}
.qr-code-modal .modal-header .close{
  font-size: 36px;
}
.qr-code-modal .modal-body{
  padding: 24px;
}
.qr-code-modal .modal-footer{
  padding: 24px;
}
.qr-code-modal .modal-footer .close{
  border-radius: 8px;
  border: 1px solid #858585;
  background: #FFF;
  padding: 16px 24px;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0.7px;
  color: #383838;
  min-width: 100px;
  text-align: center;
  opacity: 1;
}
.text-scan{
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  color: #2E384D;
  margin-top: 10px;
}
.page-content .hover td:last-child {
  gap: 5px;
}

table.table.activity-log tbody tr td span {
  line-height: 40px;
}

.logs-form-section {
  gap: 25px;
}

.custom-border {
  border: 1px solid #ADB8CC;
  border-radius: 5px;
  padding: 0;
}

.inside-checkbox {
  position: relative;
  left: -12px;
}

.custom-border ::-webkit-calendar-picker-indicator {
  margin-left: -15px;
}

.customcheck{
  opacity: 0;
  position: relative;
  z-index: 9;
  width: 34px;
}
.customcheck + span{
  background-color: lightgray;
  width: 34px;
  height: 31px;
  display: inline-block;
  margin-left: -34px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.customtext {
  position: absolute;
  width: 105px;
  background: #f2f2f2;
  height: 32px;
  border: 1px solid #ccc;
  border-radius: 5px;
  z-index: 99;
}
@media screen and (min-width: 1200px) and (max-width: 1400px){
  .customtext{
    width: 130px;
  }
}
@media screen and (min-width: 1001px) and (max-width: 1200px){
  .customtext{
    width: 40px;
  }
}
@media screen and (min-width: 768px) and (max-width: 1000px){
  .customtext{
    opacity: 0;
    pointer-events: none;
  }
}

.div-inside-checkbox {
  position: relative;
}
.div-inside-checkbox input[type="date"]{
  padding-right: 30px;
}
.div-inside-checkbox input[type="checkbox"]{
  position: absolute;
  right: 3px;
  width: 14px;
  height: 14px;
  top: 9px;
  left: auto;
}
.custom-field .custom-border{
  position: relative;
  top: 8px;
}
.custom-field .inside-checkbox{
  left: auto;
  right: 11px;
  top: 20px;
}
#list_product tbody td {
  padding: 1.0rem;
}

.nav.nav-pills {
  border-bottom: 1px;
  border-bottom-style: solid;
  border-color: #666666;
}

.nav.nav-pills .nav-link:hover {
  color: #343a40;
}
.nav.nav-pills .nav-link.active {
  color: #fff;
  background-color: #666666;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.nav.nav-pills .nav-link.active:hover {
  color: #fff;
}

.nav.nav-pills-ex {
  border-bottom: 0;
}

.nav.nav-pills-ex .nav-link.active {
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

a.sidebar-submenu-item:hover, a.sidebar-submenu-item:focus {
  text-decoration: none;
  color: #2E384D !important;
  cursor: pointer;
}

.cost {
  width: 20px;
}

.custom-class{
  height: 38px;
}

.custom-dropdown {
  position: absolute;
  right: 0px;
}
.custom-dropdown button {
  border: 0;
  background-color: transparent;
  box-shadow: none;
  font-size: 20px;
  color: #111;
}

.custom-dropdown button:hover,

.custom-dropdown button:focus,
.show > .btn-secondary.dropdown-toggle {
  background-color: transparent !important;
  color: #111;
  box-shadow: none !important;
}
.custom-dropdown .dropdown-item img{
width: 16px;
height: 16px;
}
.span-image{
  position: absolute;
  left: 10px;
  top: 8px;
  z-index: 1;

}
.span-image-wrapper{
  top: 5px;
}
.custom-input{
  padding-right: 8px;
}
.dropdown-toggle::after {
  /* border-color: #111 !important; */
  padding-left: 0 !important;
}
.addstockmodal {
  width: 1240px;
  margin: 0 -50px;
}
.addstockmodal_show{
  width: 100% !important;
  margin: 0 !important;

}
.addstockmodal
.check_update_wrapper
 .date-fields {
  flex-wrap: nowrap;
}
.addstockmodal .date-fields .custom-input {
  padding-left: 35px;
  padding-right: 30px;
  width: 200px;
}

.customwidth, .dropdown-menu .dropdown-item img{
  width: 18px;
  margin-top: -4px !important;
}
.span-image img{
  width: 15px;
  margin-top: 0px !important;
  pointer-events: none;
}
.span-image-show img{
  width: 18px;
  margin-top: 4px !important;
}
.disabled-dropdown {
  pointer-events: none;
  opacity: 0.65;
}

.custom_append{
  margin-left: 20px;
}
.custom-drop_down{
  position: absolute;
  right: 110px;
  top: -4px
}

.custom_dropdown_show{
  position: absolute;
  right: 0px;
}
.custom_dropdown_show button, .custom-drop_down button{
  background-color: transparent !important;
  color: #111 !important;
  box-shadow: none !important;
  border: 0 !important;
}

.date_field_show{
  padding-left: 30px;
}
.dropdown-menu .dropdown-item-wrapper {
  display: flex;
  padding-left: 1.5rem;
}
.dropdown-menu .dropdown-item-wrapper:hover {
background-color: #e9ecef;
}
.dropdown-menu .dropdown-item {
  padding-left: 5px;
  cursor:pointer;
}
.dropdown-item-wrapper .customwidth {
  width: 18px;
  margin-top: 4px !important;
}
.turbo_dropdown {
  position: absolute;
  right: 136px;
}
.form-control:focus {
  background-color: transparent !important;
}

.drop-down-toggle {
  right: 85px;
}

.drop-down-toggle_position {
  right: 250px;
}
.custom_show {
  margin-top: -4px !important;
}
.expiry_date_wrapper {
  width:200px;
}

.check_update_wrapper
.custom_dropdown {
  width: 250px
}

.expiry-date-outer {
  width: max-content;
  display: flex;
  border-radius: 8px;
}

.expiry-date-outer .expiry-date {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px;
}

.expiry-tooltip-icon {
  position: relative;
  display: flex;
  cursor: pointer;
}

.expiry-tooltip-icon .expiry-tooltiptext {
  display: none;
  background-color: #2e384c;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  opacity: 1;
  transition: opacity 0.3s;
  top: 0;
  left: 50%;
  transform: translate(-50%, -120%);
  min-width: 120px;
  max-width: 300px;
  width: max-content;
}

.expiry-tooltip-icon .expiry-tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #2e384c transparent transparent transparent;
}

.expiry-tooltip-icon:hover .expiry-tooltiptext {
  display: block;
  opacity: 1;
}
.hover-wrapper td {
    display: flex;
    flex-direction: row;
}
.addstockmodal-wrapper{
  width:1500px;
  margin:0 -130px
}
.custom-flex{
  flex:1 !important;
}
.custom-ebay-dropdown{
  position: absolute;
}
.dropdown-content{
  display: none;
}
.custom-ebay-dropdown button {
  width: 110px;
  background: #fff;
  height: 30px;
  border: 0;
  text-align: left;
  position: absolute;
  top: -15px;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.custom-ebay-dropdown button span {
  width: 80px;

    display: block;
}
.custom-ebay-dropdown .dropdown-content {
  background: white;
  z-index: 10;
  position: absolute;
  width: auto;
  border: 1px solid #F7F7F7;
  box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.25);
  height: 200px;
  overflow: auto;
  top: 20px;
  right: -110px;
  border-radius: 10px;
}
.custom-ebay-dropdown .dropdown-content .dropdown-item{
  padding: 10px;
  display: flex;
  align-items: center;
  margin: 0;
}
.custom-ebay-dropdown .dropdown-item input{
  width: 15px;
    height: 15px;
}
.custom-ebay-dropdown button::after 
{
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border: 1px solid #111;
  width: 8px;
  height: 8px;
  position: absolute;
  top: 9px;
  transform: rotate(45deg);
  border-width: 0 2.5px 2.5px 0;
  right: 1px;
}
.ebay_channel_wrapper {
  padding: 8px 16px 10px 16px;
  background: #fff;
  margin: 10px 0;
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
}
.ebay_channel_wrapper img
{
width: 60px;
height: 30px;
}
.ebay_channel_wrapper input[type="checkbox"] {
  width: 15px;
  height: 15px;
}
.Tracking_email_link {
  color: #006bff;
  text-decoration: underline;
}
.custom_msg_check {
  z-index:9999;
  width: 20px;
  height: 20px;
}
.custom_label_check {
  cursor: pointer;
  position: relative;
  z-index: 999;
}

.input-group-nowrap {
  flex-wrap: nowrap;
}
.dropdown-item-img {
  width: 50px;
  height: 50px;
  margin: 0 5px;
}
.dropdown-item-img img {
  width: 100%;
  height: auto;

}