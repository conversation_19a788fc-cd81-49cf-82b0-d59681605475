# frozen_string_literal: true

module Spree
  class SubscribeNewsletterMailer < BaseMailer
    def send_seller_mail(store, subscribeemail)
      ensure_default_action_mailer_url_host(store.url)
      email_smtp = current_store.email_setting.smtp
      body = build_email_body(store, subscribeemail)
      msg = mail(
        to: store.new_order_notifications_email,
        from: store.mail_from_address,
        subject: "New Subscriber on [AXEL.Market]",
        cc: [],
        content_type: "text/html",
        body: body,
      )
      msg.delivery_method.settings.merge!(Hash[email_smtp.map { |k, v| [k.to_sym, v] }]) if msg.perform_deliveries
      msg.deliver
    end

    private

    def build_email_body(store, email)
      body = "{{body_template}}"
      body_details = render_to_string(
        partial: "spree/shared/subscribe_newsletter",
        locals: { storename: store.name, storeurl: store.url, user_email: email, customer_support_email: store.customer_support_email },
      )
      options = {
        "body_template" => body_details,
      }
      template = ::Liquid::Template.parse(body)
      template.render(options)
    end
  end
end
