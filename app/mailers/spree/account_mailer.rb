# frozen_string_literal: true

module Spree
  class AccountMailer < BaseMailer
    def seller_welcome_email(organisation)
      ensure_default_action_mailer_url_host(current_store.url)
      email_smtp = current_store.email_setting.smtp
      @body = build_seller_welcome_email_body(organisation)
      msg = mail(
        to: organisation.admin_email,
        from: current_store.mail_from_address,
        subject: @subject,
        body: @body,
        content_type: "text/html",
      )
      msg.delivery_method.settings.merge!(email_smtp.transform_keys(&:to_sym)) if msg.perform_deliveries
      msg.deliver
    end

    def store_employee_invite(user_email, organisation)
      ensure_default_action_mailer_url_host(current_store.url)
      email_smtp = current_store.email_setting.smtp
      @body = build_employee_invite_email_body(user_email, organisation)
      msg = mail(
        to: user_email,
        from: current_store.mail_from_address,
        subject: @subject,
        body: @body,
        content_type: "text/html",
      )
      msg.delivery_method.settings.merge!(email_smtp.transform_keys(&:to_sym)) if msg.perform_deliveries
      msg.deliver
    end

    private

    def build_seller_welcome_email_body(organisation)
      template = Spree::EmailTemplate.find_or_initialize_by(template_name: "seller_welcome_email", store_id: current_store.id)

      unless template.persisted?
        template.update(template_data(current_store)[:seller_welcome_email])
      end

      setup_guide_url = "#{organisation.url}/admin/login"

      @subject = template.subject
      template.render_body(
        organisation,
        {
          store_name: organisation.subdomain.titleize,
          workplace_name: organisation.name,
          customer_support_email: current_store.customer_support_email,
          setup_guide_url: setup_guide_url,
        },
      )
    end

    def build_employee_invite_email_body(user_email, organisation)
      template = Spree::EmailTemplate.find_or_initialize_by(template_name: "store_employee_invite", store_id: current_store.id)

      unless template.persisted?
        template.update(template_data(current_store)[:store_employee_invite])
      end

      invitation_url = "#{organisation.url}/admin/login?invited=true&email=#{user_email}"
      @subject = template.subject
      template.render_body(
        organisation,
        {
          user_email: user_email,
          store_name: organisation.subdomain.titleize,
          workplace_name: organisation.name,
          customer_support_email: current_store.customer_support_email,
          invitation_url: invitation_url,
        },
      )
    end

    def template_data(store)
      {
        store_employee_invite: {
          template_name: "store_employee_invite",
          active: true,
          subject: "You have been invited to join Axel Market",
          body: Rails.public_path.join("store_employee_invite.txt").read,
          store_id: store.id,
          variables: [
            "user_email",
            "admin_email",
            "store_name",
            "workplace_name",
            "customer_support_email",
            "invitation_url",
          ],
          description: "This email is sent when admin add team member to organisation",
          mailer_class: "Spree::AccountMailer",
        },
        seller_welcome_email: {
          template_name: "seller_welcome_email",
          active: true,
          subject: "Welcome to AXEL Market - Your Seller Journey Begins Here!",
          body: Rails.public_path.join("seller_welcome_email.txt").read,
          store_id: store.id,
          variables: [
            "user_email",
            "store_name",
            "workplace_name",
            "customer_support_email",
            "setup_guide_url",
          ],
          description: "This welcome email is sent to seller after they created business account",
          mailer_class: "Spree::AccountMailer",
        },
      }
    end
  end
end
