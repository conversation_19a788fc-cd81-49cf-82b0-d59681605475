# frozen_string_literal: true

module Spree
  class ReturnAuthorizationSend<PERSON>abelMailer < BaseMailer
    def return_authorization_send_label_email(return_authorization, customer_shipment, test: true)
      @return_authorization = if return_authorization.is_a?(Spree::ReturnAuthorization)
        return_authorization
      else
        Spree::ReturnAuthorization.find(return_authorization)
      end

      @customer_shipment = if customer_shipment.is_a?(Spree::CustomerShipment)
        customer_shipment
      else
        Spree::ReturnAuthorization.find(customer_shipment)
      end

      @order = @return_authorization.order
      current_store = @order&.store || Spree::Store.default
      return_authorization_send_label_details
      email = test ? current_store.email_setting.intercept_email : @order.email
      mail(
        to: email,
        from: current_store.mail_from_address,
        subject: @subject,
        store_url: current_store.url,
        cc: current_store.mail_from_address,
      )
    end

    private

    def return_authorization_send_label_details
      ensure_default_action_mailer_url_host(current_store.url)
      render_to_string(
        partial: "spree/return_authorization_send_label_mailer/shared/return_authorization_send_label_table",
        locals: { return_authorization: @return_authorization, customer_shipment: @customer_shipment },
      )
      @body = build_template(
        @return_authorization,
        {
          customer_tracking: @customer_shipment&.tracking,
          customer_tracking_label: @customer_shipment&.tracking_label,
          customer_tracking_number: @customer_shipment&.number,
        },
      )
    end
  end
end
