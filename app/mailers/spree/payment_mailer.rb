# frozen_string_literal: true

module Spree
  class PaymentMailer < BaseMailer
    include ::Spree::BaseHelper
    def paypal_payment_url(order, paypal_url, amount_payable)
      email = order.email
      body = build_paypal_url_email_details(order, paypal_url, amount_payable)
      subject = "Payment Due"
      mail(to: email, from: from_address, subject: subject, cc: from_address, body: body, content_type: "text/html")
    end

    private

    def build_paypal_url_email_details(order, paypal_url, amount_payable)
      current_store = order.store
      user = order.user
      ensure_default_action_mailer_url_host(current_store.url)

      line_item_details = render_to_string(
        partial: "spree/shared/revised_order_items_table",
        locals: { line_items: order.line_items, order: order },
      )

      template = ::Spree::EmailTemplate.find_or_initialize_by(template_name: 'paypal_payment_url', store_id: current_store.id)
      unless template.persisted?
        template.update(template_data(current_store)[:paypal_payment_url])
      end

      template.subject
      @body = template.render_body(order, { item_details: line_item_details, url: paypal_url, amount: amount_payable })
    end

    def template_data(current_store)
      {
        paypal_payment_url: {
          template_name: "paypal_payment_url",
          active: true,
          subject: "Payment Confirmation",
          body: Rails.public_path.join("paypal_payment_url.txt").read,
          store_id: current_store.id,
          variables: [
            "customer_firstname",
            "order_number",
            "purchase_timestamp",
            "seller_support_email",
            "store_name",
            "user_email",
            "customer_email",
            "customer_shipping_address",
            "order_total",
            "line_item_details",
            "payment_link",
            "payable_amount",
          ],
          description: "This email is sent to customer when new payment is created",
          mailer_class: "Spree::PaymentMailer",
        }
      }
    end
  end
end
