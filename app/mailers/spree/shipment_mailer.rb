# frozen_string_literal: true

module Spree
  class ShipmentMailer < BaseMailer
    include ::Spree::BaseHelper

    def shipped_email(shipment, resend = false, test: false)
      @shipment = shipment.respond_to?(:id) ? shipment : Spree::Shipment.find(shipment)
      @order_package = @shipment.order_package
      @order = @shipment.order
      current_store = @shipment.store
      shipment_details
      email = test ? current_store.email_setting.intercept_email : @order.email
      if test
        mail(
          to: email,
          from: from_address,
          subject: @subject,
          body: @body,
          content_type: "text/html",
          store_url: current_store.url,
          cc: from_address,
        )
      elsif @order.sale_channel_metadata.blank? && !invalid_email?(@order.email)
        # do nothing to prevent sending 2 emails to ebay buyer.
        # cause eBay will send email after order changes get synced from AM
        mail(
          to: email,
          from: from_address,
          subject: @subject,
          body: @body,
          content_type: "text/html",
          store_url: current_store.url,
          cc: from_address,
        )
      end
    end

    private

    def shipment_details
      ensure_default_action_mailer_url_host(current_store.url)
      line_items = @order_package.shipments.flat_map(&:manifest).map(&:line_item)
      line_item_details = render_to_string(
        partial: "spree/shared/shipped_purchased_items_table",
        locals: { line_items: line_items, order_package: @order_package },
      )

      line_items_table = render_to_string(
        collection: line_items,
        partial: "spree/shared/purchased_items_table/line_item",
        as: :line_item,
      )

      shipment_note = render_to_string(
        partial: "spree/shared/purchased_items_table/shipment_note",
        locals: { order_package: @order_package },
      )

      @body = build_template(
        @shipment,
        { line_item_details: line_item_details, line_items_table: line_items_table, shipment_note: shipment_note },
      )
    end
  end
end
