# frozen_string_literal: true

module Spree
  class CustomerMailer < BaseMailer
    def send_test_customer_mail(subject, body)
      order = ::Spree::Order.last
      current_store = order.store
      email = current_store.email_setting.intercept_email
      body = build_email_detail(order, body)
      email_smtp = current_store.email_setting.smtp
      msg = mail(
        to: email,
        from: current_store.mail_from_address,
        subject: subject,
        store_url: current_store.url,
        body: body,
      )
      msg.delivery_method.settings.merge!(Hash[email_smtp.map { |k, v| [k.to_sym, v] }]) if msg.perform_deliveries
      msg.deliver
    end

    def send_customer_mail(store, body, emails, subject = nil, cc = [])
      ensure_default_action_mailer_url_host(store.url)
      email_smtp = current_store.email_setting.smtp
      msg = mail(
        to: emails,
        from: current_store.mail_from_address,
        subject: subject,
        store_url: store.url,
        cc: cc,
        content_type: "text/html",
        body: body,
      )
      msg.delivery_method.settings.merge!(Hash[email_smtp.map { |k, v| [k.to_sym, v] }]) if msg.perform_deliveries
      msg.deliver
    end

    def build_email_detail(order, body)
      ensure_default_action_mailer_url_host(order.store.url)
      line_item_details = render_to_string(
        partial: "spree/shared/purchased_items_table",
        locals: { line_items: order.line_items, order: order },
      )
      line_items_table = render_to_string(
        collection: order.line_items,
        partial: "spree/shared/purchased_items_table/line_item",
        as: :line_item,
      )
      options = {
        "username" => order.name,
        "user_email" => order.email,
        "order_number" => order.number,
        "order_total" => order.total,
        "tax" => order.additional_tax_total,
        "subtotal" => order.item_total,
        "store_name" => order.store.name,
        "line_item_details" => line_item_details,
        "line_items_table" => line_items_table,
        "tracking" => order.shipments&.map(&:tracking)&.join(","),
      }
      template = ::Liquid::Template.parse(body)
      template.render(options)
    end
  end
end
