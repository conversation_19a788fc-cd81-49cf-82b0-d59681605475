# frozen_string_literal: true

module Spree
  class CustomerNotifyMeMailer < BaseMailer
    def send_customer_notify_mail(store, email, product, action)
      ensure_default_action_mailer_url_host(store.url)
      email_smtp = store.email_setting.smtp
      prod_name = find_product_name(product)
      listing_url = find_listing_url(product)
      option_detail = find_option_detail(product)
      other_params = {}
      other_params["listing_url"] = listing_url
      other_params["option_detail"] = option_detail
      body = build_email_body(store, product, action, prod_name, other_params)
      msg = mail(
        to: email,
        from: store.mail_from_address,
        subject: "Great News! #{prod_name}#{option_detail} is Back in Stock – Get Yours Now!",
        cc: [],
        content_type: "text/html",
        body: body,
      )
      msg.delivery_method.settings.merge!(Hash[email_smtp.map { |k, v| [k.to_sym, v] }]) if msg.perform_deliveries
      msg.deliver
    end

    private

    def find_product_name(variant)
      if variant.is_a?(::Spree::Product)
        variant&.listings&.Active&.select { |x| x if x.sale_channel.brand == "storefront" }&.last&.title
      else
        variant&.product&.listings&.Active&.select { |x| x if x.sale_channel.brand == "storefront" }&.last&.title
      end
    end

    def find_listing_url(variant)
      listing = if variant.is_a?(::Spree::Product)
        variant&.listings&.Active&.select { |x| x if x.sale_channel.brand == "storefront" }&.last
      else
        variant&.product&.listings&.Active&.select { |x| x if x.sale_channel.brand == "storefront" }&.last
      end

      if listing&.sale_channel&.brand == "amazon"
        return "" unless listing.status == "Active"

        "https://www.amazon.in/sp?seller=#{listing.sale_channel.oauth_application.seller_name}&asin=#{listing&.sale_channel_hash&.dig("attributes", "merchant_suggested_asin")&.first&.dig("value")}" # rubocop:disable Layout/LineLength
      elsif listing&.sale_channel&.brand == "storefront"
        if variant.is_a?(::Spree::Product)
          listing&.item_url
        elsif variant.is_master?
          listing&.item_url
        else
          gen_variant_url(listing&.item_url, variant)
        end
      else
        listing&.item_url
      end
    end

    def find_option_detail(variant)
      return "" if variant.is_a?(::Spree::Product)
      return "" if variant.is_master?
      return '' if variant.nil? || variant.option_values.nil?

      ovs = ""
      variant.option_values.each do |option_value|
        key = option_value.option_type.name
        value = option_value.presentation
        ovs += "#{key}: #{value}, "
      end
      ovs.chomp!(", ")
      "(" + ovs + ")"
    end

    def build_email_body(store, variant, action, prod_name, other_params)
      listing_url = other_params["listing_url"]
      option_detail = other_params["option_detail"]
      body = "{{body_template}}"
      body_details = if action == "temporaryUnavailable"
        render_to_string(
          partial: "spree/shared/customer_notify_me",
          locals: {
            store: store,
            product: variant,
            product_name: prod_name,
            listing_url: listing_url,
            option_detail: option_detail,
          },
        )
      else
        render_to_string(
          partial: "spree/shared/notify_me_out_of_stock",
          locals: {
            store: store,
            product: variant,
            product_name: prod_name,
            listing_url: listing_url,
            option_detail: option_detail,
          },
        )
      end
      options = {
        "body_template" => body_details,
      }
      template = ::Liquid::Template.parse(body)
      template.render(options)
    end

    def gen_variant_url(baseurl, variant)
      # url = "https://xxxx.xxx.xxx" # "?xx=aa&yy=bb"
      return baseurl if variant.nil? || variant.option_values.nil?

      ovs = ""
      variant.option_values.each do |option_value|
        key = option_value.option_type.name
        value = option_value.presentation
        ovs += "#{key}=#{value}&"
      end
      baseurl + "?" + ovs
    end
  end
end
