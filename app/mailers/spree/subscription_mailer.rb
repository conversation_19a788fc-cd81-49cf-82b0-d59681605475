# frozen_string_literal: true

module Spree
  class SubscriptionMailer < ::Spree::BaseMailer
    include ::Spree::BaseHelper
    def subscription_email(subscription)
      @subscription = subscription
      @order = @subscription.line_item.order
      current_store = @order.store
      user = @order.user
      email = @subscription.line_item.order&.email
      body = build_activation_email_details(subscription, user, current_store, email)
      @subject = "Subscribed Successfully"
      mail(to: email, from: from_address, subject: @subject, cc: from_address, body: body, content_type: "text/html")
    end

    def subscription_ended_email(subscription)
      @subscription = subscription
      @order = @subscription.line_item.order
      current_store = @order.store
      user = @order.user
      email = @subscription.line_item.order&.email
      body = build_inactive_email_details(subscription, user, current_store, email)
      @subject = "Subscription Ended"
      mail(to: email, from: from_address, subject: @subject, cc: from_address, body: body, content_type: "text/html")
    end

    def unsubscribed_email(subscription)
      @subscription = subscription
      @order = @subscription.line_item.order
      current_store = @order.store
      user = @order.user
      email = @subscription.line_item.order&.email
      body = build_deactivation_email_details(subscription, user, current_store, email)
      @subject = "Unsubscribed Successfully"
      mail(to: email, from: from_address, subject: @subject, cc: from_address, body: body, content_type: "text/html")
    end

    private

    def build_activation_email_details(subscription, user, store, email)
      ensure_default_action_mailer_url_host(store.url)

      template = ::Spree::EmailTemplate.find_or_initialize_by(template_name: 'subscription_email', store_id: store.id)

      unless template.persisted?
        template.update(template_data(store)[:welcome_subscription_email])
      end

      template.subject
      @body = template.render_body(user, { store: store, user_email: email, subscription: subscription })
    end

    def build_inactive_email_details(subscription, user, store, email)
      ensure_default_action_mailer_url_host(store.url)

      template = ::Spree::EmailTemplate.find_or_initialize_by(template_name: 'subscription_ended_email', store_id: store.id)

      unless template.persisted?
        template.update(template_data(store)[:ended_subscription_email])
      end

      template.subject
      @body = template.render_body(user, { store: store, user_email: email, subscription: subscription })
    end

    def build_deactivation_email_details(subscription, user, store, email)
      ensure_default_action_mailer_url_host(store.url)

      template = ::Spree::EmailTemplate.find_or_initialize_by(template_name: 'unsubscribed_email', store_id: store.id)

      unless template.persisted?
        template.update(template_data(store)[:unsubscribed_email])
      end

      template.subject
      @body = template.render_body(user, { store: store, user_email: email, subscription: subscription })
    end

    def template_data(current_store)
      {
        welcome_subscription_email: {
          template_name: "subscription_email",
          active: true,
          subject: "Welcome to AXEL Market - Your Shopping Journey Begins Here!",
          body: Rails.public_path.join("welcome_subscription_email.txt").read,
          store_id: current_store.id,
          variables: [
            "username",
            "user_email",
            "product_name",
            "delivery_frequency",
            "price",
            "next_scheduled_delivery",
          ],
          description: "This welcome subscription email is sent to customer after purched product with subscription",
          mailer_class: "Spree::SubscriptionMailer",
        },
        ended_subscription_email: {
          template_name: "subscription_ended_email",
          active: true,
          subject: "Welcome to AXEL Market - Your Shopping Journey Begins Here!",
          body: Rails.public_path.join("ended_subscription_email.txt").read,
          store_id: current_store.id,
          variables: [
            "username",
            "user_email",
            "product_name",
            "delivery_frequency",
            "price",
            "next_scheduled_delivery",
          ],
          description: "This welcome subscription email is sent to customer after purched product with subscription",
          mailer_class: "Spree::SubscriptionMailer",
        },
        unsubscribed_email: {
          template_name: "unsubscribed_email",
          active: true,
          subject: "Welcome to AXEL Market - Your Shopping Journey Begins Here!",
          body: Rails.public_path.join("unsubscribed_email.txt").read,
          store_id: current_store.id,
          variables: [
            "username",
            "user_email",
            "product_name",
            "delivery_frequency",
            "price",
            "next_scheduled_delivery",
          ],
          description: "This welcome subscription email is sent to customer after purched product with subscription",
          mailer_class: "Spree::SubscriptionMailer",
        },
      }
    end
  end
end
