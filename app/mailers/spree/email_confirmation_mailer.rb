# frozen_string_literal: true

module Spree
  class EmailConfirmationMailer < BaseMailer
    def send_confirmation_code(record)
      store = current_store
      ensure_default_action_mailer_url_host(store.url)
      email_smtp = store.email_setting.smtp
      @body = build_send_confirmation_email_body(record, store)

      msg = mail(
        to: record.email,
        from: store.mail_from_address,
        subject: @subject,
        body: @body,
        content_type: "text/html",
      )
      msg.delivery_method.settings.merge!(email_smtp.transform_keys(&:to_sym)) if msg.perform_deliveries
      msg.deliver
    end

    private

    def build_send_confirmation_email_body(seller, store)
      template = Spree::EmailTemplate.find_or_initialize_by(template_name: "send_confirmation", store_id: store.id)

      unless template.persisted?
        template.update(template_data(store)[:send_confirmation_code])
      end

      @subject = template.subject
      template.render_body(seller, { customer_support_email: store.customer_support_email })
    end

    def template_data(store)
      {
        send_confirmation_code: {
          template_name: "send_confirmation",
          active: true,
          subject: "Your AXEL Market Email Verification Code",
          body: Rails.public_path.join("send_confirmation.txt").read,
          store_id: store.id,
          variables: [
            "user_email",
            "confirmation_code",
            "customer_support_email",
          ],
          description: "This email was sent to user when to verify email",
          mailer_class: "Spree::EmailConfirmationMailer",
        },
      }
    end
  end
end
