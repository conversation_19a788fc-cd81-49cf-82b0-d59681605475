# frozen_string_literal: true

module Spree
  class CampaignMailer < BaseMailer
    def send_campaign_email(campaign, subscriber, campaign_subscriber)
      ensure_default_action_mailer_url_host(current_store.url)
      email_smtp = current_store.campaign_email_setting.smtp
      template = campaign.template
      @body = build_email_body(template.body, campaign, subscriber)
      msg = mail(
        to: subscriber.email,
        from: campaign.sender_email,
        subject: template.subject,
        body: @body,
        content_type: "text/html",
        message_id: campaign_subscriber.message_id
      )
      msg.delivery_method.settings.merge!(email_smtp.transform_keys(&:to_sym)) if msg.perform_deliveries
      msg.deliver
    end

    def build_email_body(body, campaign, subscriber)
      template = ::Liquid::Template.parse(body)
      template_variables = {
        "username" => subscriber.name || 'Customer',
        "user_email" => subscriber.email,
        "campaign_title" => campaign.title,
        "store_name" => current_store.name
      }
      template.render(template_variables)
    end
  end
end
