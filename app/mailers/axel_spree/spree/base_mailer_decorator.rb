# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module BaseMailerDecorator
      def self.prepended(base)
        # base.before_action :switch_tenant
        base.before_action(:ensure_default_action_mailer_url_host)
        base.after_action(:set_delivery_options)
        base.after_action(:attach_metadata)
      end

      private

      def switch_tenant
        tenant = Apartment::Tenant.current
        Apartment::Tenant.switch!(tenant)
      end

      def set_delivery_options
        store =  action_name == "reset_password_instructions" ? ::Spree::Store.default : current_store
        settings = store.email_setting
        mail.from = Rails.application.credentials.dig(:system_email_from)
        if settings
          mail.perform_deliveries = (settings.mail_delivery)
          mail.from = store.mail_from_address
          mail.delivery_method.settings.merge!(Hash[settings.smtp.map do |k, v|
                                                      [k.to_sym, v]
                                                    end]) if mail.perform_deliveries
        end
      end

      def ensure_default_action_mailer_url_host(store_url = nil)
        store = action_name == "reset_password_instructions" ? ::Spree::Store.default : current_store
        store_url ||= store.url
        ActionMailer::Base.default_url_options = {}
        ActionMailer::Base.default_url_options[:protocol] = "https"
        ActionMailer::Base.default_url_options[:host] = store_url
        Rails.application.routes.default_url_options[:protocol] = "https"
        Rails.application.routes.default_url_options[:host] = store_url
      end

      def build_template(resource, options = {})
        @template = if resource.respond_to?(:email_template)
          Rails.logger.info("Resource state before reload and ship: #{resource.default_email}")
          resource.reload.default_email
        else
          ::Spree::EmailTemplate.find_by(template_name: action_name, store_id: current_store.id)
        end
        return if @template.blank?

        @subject = @template.subject
        @body = @template.render_body(resource, options)
      end

      def attach_metadata
        mailer_klass = self.class.to_s
        mailer_action = action_name

        message.instance_variable_set(:@store_id, current_store.id)
        message.instance_variable_set(:@mailer_klass, mailer_klass)
        message.instance_variable_set(:@mailer_action, mailer_action)

        message.class.send(:attr_reader, :store_id)
        message.class.send(:attr_reader, :mailer_klass)
        message.class.send(:attr_reader, :mailer_action)
      end
    end
  end
end

Spree::BaseMailer.prepend(AxelSpree::Spree::BaseMailerDecorator)
