# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module UserMailerDecorator
      def reset_password_instructions(user, token, *args, test: false)
        current_store_id = args.inject(:merge)[:current_store_id]
        @current_store = ::Spree::Store.find(current_store_id) || ::Spree::Store.current
        @user = user
        @edit_password_reset_url = if user.admin?
          spree.admin_edit_password_url(
            reset_password_token: token,
            host: @current_store.url,
          )
        else
          "#{@current_store.formatted_url}/resetpassword?token=#{token}"
        end
        build_reset_password_template
        @locale = @current_store.has_attribute?(:default_locale) ? @current_store.default_locale : I18n.default_locale
        I18n.locale = @locale if @locale.present?
        mail(
          to: user.email,
          from: @current_store.mail_from_address,
          subject: @current_store.name + " " + I18n.t("devise.mailer.reset_password_instructions.subject"),
          store_url: @current_store.url,
        )
      end

      def confirmation_instructions(user, token, opts = {}, test = false)
        current_store_id = opts[:current_store_id]
        @current_store = ::Spree::Store.find(current_store_id) || ::Spree::Store.current
        @confirmation_url = "https://#{::Spree::Store.current.url}/email-validate?token=#{token}"
        @email = user.email
        @user = user
        confirmation_instructions_details
        set_system_email_configuration
        mail(
          to: user.email,
          from: @current_store.mail_from_address,
          subject: "User Email Confirmation",
          store_url: @current_store.url,
        )
      ensure
        reset_mail_setting(@current_store)
      end

      def welcome_email(user_id, store_id)
        user = ::Spree::User.find(user_id)
        store = ::Spree::Store.find(store_id)
        ensure_default_action_mailer_url_host(store.url)
        template = ::Spree::EmailTemplate.find_by(template_name: "welcome_email")
        return if template.blank?

        subject = template.subject
        @body = template.render_body(user, { store: store })
        set_system_email_configuration
        mail(
          to: user.email,
          from: store.mail_from_address,
          subject: subject,
          store_url: store.url,
        )
      ensure
        reset_mail_setting(store)
      end

      private

      def set_system_email_configuration
        return if Rails.env.test?

        if ::Spree::EmailSetting.last.blank?
          smtp_settings = Rails.application.credentials.dig(:smtp) || {}
          ActionMailer::Base.delivery_method = :smtp
          ActionMailer::Base.perform_deliveries = true
          ActionMailer::Base.smtp_settings.merge!(smtp_settings.transform_keys(&:to_sym))
        end
      end

      def reset_mail_setting(store)
        return if Rails.env.test?

        settings = store.email_setting

        # Set the default delivery method and enable email deliveries
        ActionMailer::Base.delivery_method = :smtp

        # If no email settings are present, clear specific SMTP settings
        if settings.blank? || settings.smtp.blank?
          ActionMailer::Base.perform_deliveries = false
          ActionMailer::Base.smtp_settings = {
            address: nil,
            port: nil,
            domain: nil,
            user_name: nil,
            password: nil,
            authentication: nil,
            enable_starttls_auto: nil,
          }
          return # Exit the method if no settings exist
        end

        # Convert string keys in settings.smtp to symbol keys and merge into smtp_settings
        ActionMailer::Base.perform_deliveries = settings.mail_delivery
        smtp_settings = settings.smtp.transform_keys(&:to_sym)
        ActionMailer::Base.smtp_settings.merge!(smtp_settings)
      end

      def confirmation_instructions_details
        ensure_default_action_mailer_url_host(current_store.url)
        @body = build_template(@user, { confirmation_url: @confirmation_url })
      end

      def build_reset_password_template
        ensure_default_action_mailer_url_host(current_store.url)
        @body = build_template(@user, { password_reset_url: @edit_password_reset_url, store: @current_store })
      end
    end
  end
end

Spree::UserMailer.prepend(AxelSpree::Spree::UserMailerDecorator)
