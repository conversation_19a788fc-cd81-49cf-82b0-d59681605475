module Spree
  module Listings
    class Find
      def initialize(scope:, params:, current_currency: nil, store_id: nil)
        @scope = scope
        @params = params

        if current_currency.present?
          ActiveSupport::Deprecation.warn(<<-DEPRECATION, caller)
            `current_currency` param is deprecated and will be removed in Spree 5.
            Please pass `:currency` in `params` hash instead.
          DEPRECATION
        end

        @ids              = String(params.dig(:filter, :ids)).split(',')
        @skus             = String(params.dig(:filter, :skus)).split(',')
        @store            = params[:store] || Spree::Store.default
        @price            = map_prices(String(params.dig(:filter, :price)).split(','))
        @currency         = current_currency || params.dig(:filter, :currency) || params[:currency]
        @store_id         = store_id
        @taxons           = taxon_ids(params.dig(:filter, :taxons))
        @concat_taxons    = taxon_ids(params.dig(:filter, :concat_taxons))
        @name             = params.dig(:filter, :name)
        @options          = params.dig(:filter, :options).try(:to_unsafe_hash)
        @option_value_ids = String(params.dig(:filter, :option_value_ids)).split(',')
        @sort_by          = params.dig(:sort_by)
        @deleted          = params.dig(:filter, :show_deleted)
        @discontinued     = params.dig(:filter, :show_discontinued)
        @properties       = params.dig(:filter, :properties)
        @in_stock         = params.dig(:filter, :in_stock)
        @backorderable    = params.dig(:filter, :backorderable)
        @purchasable      = params.dig(:filter, :purchasable)
      end

      def execute
        listings = by_ids(scope)
        listings = by_skus(listings)
        listings = by_price(listings)
        # listings = by_currency(listings)
        listings = by_taxons(listings)
        listings = by_concat_taxons(listings)
        listings = by_name(listings)
        listings = by_options(listings)
        listings = by_option_value_ids(listings)
        listings = by_properties(listings)
        # listings = include_deleted(listings)
        # listings = include_discontinued(listings)
        listings = show_only_backorderable(listings)
        listings = show_only_purchasable(listings)
        listings = show_only_stock(listings)

        listings
      end

      private

      attr_reader :ids, :skus, :price, :currency, :taxons, :concat_taxons, :name, :options, :option_value_ids, :scope,
                  :sort_by, :deleted, :discontinued, :properties, :store, :in_stock, :backorderable, :purchasable, :store_id,
                  :params

      def ids?
        ids.present?
      end

      def skus?
        skus.present?
      end

      def price?
        price.present?
      end

      def currency?
        currency.present?
      end

      def taxons?
        taxons.present?
      end

      def concat_taxons?
        concat_taxons.present?
      end

      def name?
        name.present?
      end

      def options?
        options.present?
      end

      def option_value_ids?
        option_value_ids.present?
      end

      def sort_by?
        sort_by.present?
      end

      def properties?
        properties.present? && properties.values.reject(&:empty?).present?
      end

      def by_ids(listings)
        return listings unless ids?

        listings.where(id: ids)
      end

      def by_skus(listings)
        return listings unless skus?

        # listings.joins(:variants_including_master).where(spree_variants: { sku: skus })
        listings.joins(:listing_selected_variants).where(spree_variants: {sku: skus})
      end

      def by_price(listings)
        return listings unless price?

        listings.price_between(price.min, price.max)
      end

      def by_currency(listings)
        return listings unless currency?

        listings.with_currency(currency)
      end

      def by_taxons(listings)
        return listings unless taxons?

        listings.joins(product: :classifications).where(Classification.table_name => { taxon_id: taxons })
      end

      def by_concat_taxons(listings)
        return listings unless concat_taxons?

        listing_ids = Spree::Listing.
                      joins(product: :classifications).
                      where(Classification.table_name => { taxon_id: concat_taxons }).
                      group("#{Spree::Listing.table_name}.id").
                      having("COUNT(#{Spree::Listing.table_name}.id) = ?", concat_taxons.length).
                      ids

        listings.where(id: listing_ids)
      end

      def by_name(listings)
        return listings unless name?

        es_listing = AxelSpree::Elasticsearch::Listing.new
        ids = es_listing.frontend.name(name).store_id(store_id).currency(currency).from(0).size(25).ids

        listings.where(id: ids)
      end

      def by_options(listings)
        return listings unless options?

        listing_ids = options.map { |key, value| listings.with_option_value(key, value)&.ids }.compact.uniq
        listings.where(id: listing_ids.reduce(&:intersection))
      end

      def by_option_value_ids(listings)
        return listings unless option_value_ids?

        listing_ids = Spree::Listing.
                      joins(listing_selected_variants: :option_values).
                      where(spree_option_values: { id: option_value_ids }).
                      group("#{Spree::Listing.table_name}.id, #{Spree::Variant.table_name}.id").
                      having('COUNT(spree_option_values.option_type_id) = ?', option_types_count(option_value_ids)).
                      distinct.
                      ids

        listings.where(id: listing_ids)
      end

      def by_properties(listings)
        return listings unless properties?

        listing_ids = []
        index = 0

        properties.to_unsafe_hash.each do |property_filter_param, product_properties_values|
          next if property_filter_param.blank? || product_properties_values.empty?

          values = product_properties_values.split(',').reject(&:empty?).uniq.map(&:parameterize)

          next if values.empty?

          ids = scope.unscope(:order, :includes).with_property_values(property_filter_param, values).ids
          listing_ids = index == 0 ? ids : listing_ids & ids
          index += 1
        end

        listings.where(id: listing_ids)
      end

      def option_types_count(option_value_ids)
        Spree::OptionValue.
          where(id: option_value_ids).
          distinct.
          count(:option_type_id)
      end

      def include_deleted(listings)
        deleted ? listings.with_deleted : listings.not_deleted
      end

      def include_discontinued(listings)
        discontinued ? listings : listings.active(currency)
      end

      def show_only_stock(listings)
        return listings unless in_stock.to_s == 'true'

        sorter = Spree::Listings::Sort.new(nil, nil, params)
        if sorter.sort_by?("price")
          listings.in_stock
        else
          listings.in_stock.group("#{Spree::Listing.table_name}.id")
        end
      end

      def show_only_backorderable(listings)
        return listings unless backorderable.to_s == 'true'

        listings.backorderable
      end

      def show_only_purchasable(listings)
        return listings unless purchasable.to_s == 'true'

        listings.in_stock_or_backorderable
      end

      def map_prices(prices)
        prices.map do |price|
          price == 'Infinity' ? BigDecimal::INFINITY : price.to_f
        end
      end

      def taxon_ids(taxons_ids)
        return if taxons_ids.nil? || taxons_ids.to_s.blank?

        taxons = store.taxons.where(id: taxons_ids.to_s.split(','))
        taxons.map(&:cached_self_and_descendants_ids).flatten.compact.uniq.map(&:to_s)
      end

      def order_by_price(scope, order_type)
        scope.
          select("#{Product.table_name}.*, #{Spree::Price.table_name}.amount").
          reorder('').
          send(order_type)
      end
    end
  end
end
