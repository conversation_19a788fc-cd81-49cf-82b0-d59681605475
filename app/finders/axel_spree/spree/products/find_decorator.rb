# frozen_string_literal: true

module Axel<PERSON>pree
  module Spree
    module Products
      module FindDecorator
        include ::Spree::Admin::ElasticsearchHelper

        def initialize(scope:, params:, current_currency: nil, store_id: nil)
          @scope = scope

          ActiveSupport::Deprecation.warn("`current_currency` param is deprecated and will be removed in Spree 5") if current_currency

          if current_currency.present?
            ActiveSupport::Deprecation.warn(<<-DEPRECATION, caller)
              `current_currency` param is deprecated and will be removed in Spree 5.
              Please pass `:currency` in `params` hash instead.
            DEPRECATION
          end

          @ids = String(params.dig(:filter, :ids)).split(",")
          @skus = String(params.dig(:filter, :skus)).split(",")
          @store = params[:store] || Spree::Store.default
          @price = map_prices(String(params.dig(:filter, :price)).split(","))
          @currency = current_currency || params.dig(:filter, :currency) || params[:currency]
          @store_id = store_id
          @taxons = taxon_ids(params.dig(:filter, :taxons))
          @concat_taxons = taxon_ids(params.dig(:filter, :concat_taxons))
          @name = params.dig(:filter, :name)
          @options = params.dig(:filter, :options).try(:to_unsafe_hash)
          @option_value_ids = params.dig(:filter, :option_value_ids)
          @sort_by = params.dig(:sort_by)
          @mode = params.dig(:mode)
          @deleted = params.dig(:filter, :show_deleted)
          @discontinued = params.dig(:filter, :show_discontinued)
          @properties = params.dig(:filter, :properties)
          @in_stock = params.dig(:filter, :in_stock)
          @backorderable = params.dig(:filter, :backorderable)
          @purchasable = params.dig(:filter, :purchasable)
        end

        attr_reader :mode, :store_id

        def mode?
          mode.present?
        end

        def by_name(products)
          return products unless name?

          es_listing = AxelSpree::Elasticsearch::Listing.new
          ids = es_listing.frontend.name(name).store_id(store_id).currency(currency).from(0).size(25).storefront_product_ids

          products.where(id: ids)
        end
      end
    end
  end
end

Spree::Products::Find.prepend(AxelSpree::Spree::Products::FindDecorator)
