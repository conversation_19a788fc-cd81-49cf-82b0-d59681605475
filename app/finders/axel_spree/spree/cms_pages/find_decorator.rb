# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module CmsPages
      module FindDecorator
        def initialize(scope:, params:)
          @scope = scope
          @title = params.dig(:filter, :title)
          @kind = params.dig(:filter, :type)
          @slug = params.dig(:filter, :slug)
        end

        def execute
          pages = by_title(scope)
          pages = by_kind(pages)
          pages = by_slug(pages)

          pages
        end

        private

        attr_reader :slug

        def slug_matcher
          ::Spree::CmsPage.arel_table[:slug].matches("%#{slug}%")
        end

        def by_slug(pages)
          return pages if slug.blank?

          pages.where(slug_matcher)
        end
      end
    end
  end
end

Spree::CmsPages::Find.prepend(AxelSpree::Spree::CmsPages::FindDecorator)
