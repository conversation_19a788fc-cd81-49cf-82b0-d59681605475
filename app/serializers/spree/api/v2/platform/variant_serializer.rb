# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Platform
        class VariantSerializer < BaseSerializer
          include ResourceSerializerConcern
          include DisplayMoneyHelper

          attributes :options_text, :total_on_hand

          attribute :purchasable, &:purchasable?

          attribute :in_stock, &:in_stock?

          attribute :backorderable, &:backorderable?

          attribute :available, &:available?

          attribute :currency do |_product, params|
            params[:currency]
          end

          attribute(:name) do |object|
            object.product&.name
          end

          attribute :price do |object, params|
            price(object, params[:currency])
          end

          attribute :display_price do |object, params|
            display_price(object, params[:currency])
          end

          attribute :compare_at_price do |object, params|
            compare_at_price(object, params[:currency])
          end

          attribute :display_compare_at_price do |object, params|
            display_compare_at_price(object, params[:currency])
          end

          attribute :full_options_text
          attribute :width

          belongs_to :product
          belongs_to :tax_category
          has_many :digitals
          has_many :images
          has_many :option_values
          has_many :stock_items
          has_many :stock_locations
        end
      end
    end
  end
end
