# frozen_string_literal: true

module Api
  module V3
    class InvoiceSettingSerializer < BaseSerializer
      fields :name, :address, :city, :zipcode, :state_name, :country_id, :message

      # Strange state_id column type in table invoice_settings
      field(:state_id) { |object| object.state_id&.to_i }

      field :logo do |object|
        if object.logo.present?
          Rails.application.routes.url_helpers.rails_blob_path(object.logo, only_path: true)
        end
      end
    end
  end
end
