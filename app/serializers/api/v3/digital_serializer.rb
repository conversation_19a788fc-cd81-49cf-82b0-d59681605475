# frozen_string_literal: true

module Api
  module V3
    class DigitalSerializer < BaseSerializer
      identifier :id

      fields :variant_id

      field :url do |digital|
        url_helpers = Rails.application.routes.url_helpers
        url_helpers.polymorphic_url(digital.attachment, only_path: true)
      end

      field :content_type do |digital|
        digital.attachment.content_type.to_s
      end

      field :filename do |digital|
        digital.attachment.filename.to_s
      end

      field :byte_size do |digital|
        digital.attachment.byte_size.to_i
      end
    end
  end
end
