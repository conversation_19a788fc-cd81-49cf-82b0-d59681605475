# frozen_string_literal: true

module Api
  module V3
    class FrequentlyBoughtSerializer < BaseSerializer
      include ::Spree::Api::V3::FrequentlyBoughtHelper
      identifier :id

      fields :matched_listing_id, :position

      field :matched_listing_title do |object, params|
        matched_listing = object.matched_listing
        matched_listing&.title.presence || matched_listing&.product&.name&.titleize
      rescue
        ""
      end

      field :image_path do |object, params|
        product_images(object.matched_listing, params)
      rescue
        nil
      end
    end
  end
end
