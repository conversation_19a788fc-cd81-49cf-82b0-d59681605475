# frozen_string_literal: true

module Api
  module V3
    class LineItemSerializer < BaseSerializer
      include ::Spree::Api::V2::LineItemHelper
      identifier :id

      fields :order_id, :variant_id, :quantity, :pack_size, :listing_id
      fields :tax_category_id
      fields :price, :currency,
        :adjustment_total,
        :promo_total,
        :pre_tax_amount,
        :included_tax_total,
        :additional_tax_total,
        :taxable_adjustment_total,
        :non_taxable_adjustment_total

      field :pack_quantity do |item|
        if item.storefront_sale_channel? && item.pack_size.to_i > 0
          item.quantity / item.pack_size
        else
          item.quantity
        end
      end

      field :pack_total do |item|
        if item.storefront_sale_channel? && item.pack_size.to_i > 0
          (item.quantity / item.pack_size) * item.price
        else
          item.price * item.quantity
        end
      end

      field(:variant_image_path) do |line_item|
        fetch_variant_image(line_item)
      end

      field(:variant_name) { |li| li.variant.name }
      field(:variant_options_text) { |li| li.variant.options_text }
      field(:variant_sku) { |li| li.variant.sku }
      field(:shipment_state) { |li| li.inventory_units.first&.shipment&.state }
      field(:product_id) { |li| li&.product&.id }

      view :order do
        excludes :order_id, :tax_category_id
        excludes :adjustment_total, :promo_total, :pre_tax_amount, :included_tax_total, :additional_tax_total,
          :taxable_adjustment_total, :non_taxable_adjustment_total
      end

      view :user do
        include_view :order
        association :order, blueprint: OrderSerializer
      end

      view :full do
        # field(:product_id) { |li| li.product.id }
        # field(:product_image_path) do |li|
        #   if li.product.variant_images.first&.attachment&.attached?
        #     url_helpers = Rails.application.routes.url_helpers
        #     url_helpers.rails_blob_url(li.product.variant_images.first.attachment, only_path: true)
        #   end
        # end
        # field(:product_name) { |li| li.product.name }
        # field(:product_sku) { |li| li.product.sku }
      end

      view :shipment do
        field(:shipment_id) { |li| li.inventory_units.first&.shipment_id }

        field(:locked_in_stock) do |li|
          li.product.stock_item_units.select { |siu|
            siu.line_item_id == li.id && siu.state == 'locked'
          }.sum(&:pack_size).positive?
        end
      end
    end
  end
end
