# frozen_string_literal: true

module Api
  module V3
    class ListingCollectionSerializer < BaseSerializer
      identifier :id

      fields :slug, :name, :description

      field(:listing_ids) do |li|
        li.listing_ids || []
      end

      view :full do
        field(:listings) do |li|
          (li.listing_ids || [])
            .then { |ids| ::Spree::Listing.where(id: ids) }
            .map { |l| ListingSerializer.render_as_json(l, view: :full) }
        end
      end
    end
  end
end
