# frozen_string_literal: true

module Api
  module V3
    class MenuItemSerializer < BaseSerializer
      identifier :id

      fields :parent_id, :name, :code, :subtitle, :destination, :new_window
      field(:item_type) { |m| m.item_type&.downcase }
      fields :linked_resource_type, :linked_resource_id
      field :lft, name: :position

      view :children do
        association :children, blueprint: MenuItemSerializer, view: :children
      end
    end
  end
end
