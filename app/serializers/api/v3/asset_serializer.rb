# frozen_string_literal: true

module Api
  module V3
    class AssetSerializer < BaseSerializer
      identifier :id

      fields :viewable_id

      field(:type) { |asset| asset.type&.sub(/^Spree::/, '')&.underscore }
      field(:viewable_type) { |asset| asset.viewable_type&.sub(/^Spree::/, '')&.underscore }
      field(:url) do |asset|
        begin
          if asset.respond_to?(:attachment)
            Rails.application.routes.url_helpers.rails_blob_path(asset.attachment, only_path: true)
          end
        rescue StandardError => e
          Rails.logger.error("Error generating URL for asset: #{e.message}")
          nil
        end
      rescue
        nil
      end
    end
  end
end
