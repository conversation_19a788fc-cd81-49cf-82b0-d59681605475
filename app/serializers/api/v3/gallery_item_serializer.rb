# frozen_string_literal: true

module Api
  module V3
    class GalleryItemSerializer < BaseSerializer
      identifier :id

      fields :title, :uploaded_by

      field(:path) do |asset|
        if asset.file.attached?
          Rails.application.routes.url_helpers.rails_blob_path(asset.file, only_path: true)
        end
      end

      field(:full_path) do |asset, params|
        if asset.file.attached?
          base_url = params[:base_url].presence || "https://#{params[:store].url}"
          path = Rails.application.routes.url_helpers.rails_blob_path(asset.file, only_path: true)
          base_url + path
        end
      end
    end
  end
end
