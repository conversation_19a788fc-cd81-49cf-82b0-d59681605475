# frozen_string_literal: true

module Api
  module V3
    class BaseSerializer < Blueprinter::Base
      def self.store_image_url_for(store, attribute_name)
        attachment = store.send(attribute_name)&.attachment
        return unless attachment&.attached?

        Rails.application.routes.url_helpers.rails_blob_path(attachment, only_path: true)
      end

      def self.mini_image_url_for(img)
        mini_variant = img.attachment.variant(resize_to_limit: [48, 48]).processed
        url_helpers = Rails.application.routes.url_helpers
        url_helpers.rails_representation_url(mini_variant, only_path: true)
      end

      def self.image_url_for(img)
        url_helpers.rails_representation_url(img, only_path: true)
      end

      def self.original_image_url_for(img)
        url_helpers = Rails.application.routes.url_helpers
        url_helpers.rails_blob_url(img.attachment, only_path: true)
      end

      def self.product_img_url_for(product)
        if product.present? && product.images.first&.attachment&.attached?
          original_image_url_for(product.images.first)
        end
      end

      def self.product_variant_img_url_for(product)
        if product.present? && product.variant_images.first&.attachment&.attached?
          original_image_url_for(product.variant_images.first)
        end
      end
    end
  end
end
