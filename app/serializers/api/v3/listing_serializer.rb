# frozen_string_literal: true

module Api
  module V3
    class ListingSerializer < BaseSerializer
      include ::Spree::Api::V3::ListingHelper
      identifier :id

      fields :store_id, :item_id, :sale_channel_id, :product_id, :quantity, :currency, :sku, :image_classifier, :item_url, :shipping_category_id

      field(:title) { |listing| listing.title.presence || listing.product&.name&.titleize }

      field(:status) { |listing| listing.status&.downcase }

      field :listing_allow_offer do |listing, params|
        listing.storefront_sale_channel? ? nil : allow_offer(listing, params)
      end

      field(:total_listed_quantity) do |listing|
        listing.storefront_sale_channel? ? nil : listing.total_listed_qty
      end

      field(:total_sold_quantity) do |listing|
        listing.storefront_sale_channel? ? nil : listing.total_sold_qty
      end

      field(:total_available_quantity) do |listing|
        listing.storefront_sale_channel? ? nil : listing.total_available_qty
      end

      field(:low_availability_alert) do |listing|
        result = false
        if listing.listing_inventories.present?
          listing.listing_inventories.each do |inventory|
            result = true if inventory.low_availability_toggle.present? && inventory.low_availability_value >= inventory.total_available_qty && !listing.storefront_sale_channel?
          end
        end
        result
      end

      view :full do
        fields :description, :uploaded_by
        fields :category_id, :category_name, :shipping_id, :payment_id, :return_id, :condition_id
        fields :offer_made_count

        field :taxon_name do |listing|
          product = listing.product

          product&.taxons&.pluck(:name)&.join(', ')
        end

        field :ebay_item_specifics do |listing|
          listing.storefront_sale_channel? ? nil : listing.ebay_item_specifics
        end

        field :variant_stock_items_data do |listing|
          listing.storefront_sale_channel? ? nil : listing.variant_stock_items_data
        end

        field(:sale_channel_hash) do |listing|
          if listing.is_walmart_channel?
            listing.sale_channel_hash.present? ? listing.sale_channel_hash : listing.default_walmart_sale_channel_hash(0)
          else
            listing.sale_channel_hash
          end
        end

        field :sell_as_lot do |listing, params|
          sell_as_lot(listing, params)
        end

        field :listing_auction_pricing do |listing, params|
          listing.storefront_sale_channel? ? nil : auction_pricing(listing, params)
        end

        field :variants do |listing, params|
          listing_variants(listing, params)
        rescue
          nil
        end
      end

      fields :start_time, :end_time, :created_at, :updated_at

      field :image_path do |listing, params|
        listing_images(listing, params)
      rescue
        nil
      end

      association :sale_channel, blueprint: SaleChannelSerializer
      association :product, blueprint: ProductSerializer, view: :minimal

      field :variants, if: ->(_field_name, _listing, params) { params[:include]&.include?("variants") && params[:view] != :full } do |listing, params|
        listing_variants(listing, params)
      rescue
        nil
      end

      field :price do |listing, params|
        listing.product&.variants.present? ?  nil : (listing.listing_inventories.take&.price || listing.product&.master&.price)&.to_f
      rescue
        nil
      end

      field :match_frequently_bought do |listing, params|
        params[:frequently_bought_listing].present? ? params[:frequently_bought_listing].frequently_boughts_related?(listing.id) : nil
      rescue
        nil
      end
    end
  end
end
