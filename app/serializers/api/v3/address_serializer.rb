# frozen_string_literal: true

module Api
  module V3
    class AddressSerializer < BaseSerializer
      identifier :id

      fields :user_id, :label, :firstname, :lastname
      fields :address1, :address2, :city, :state_id, :country_id, :zipcode
      fields :company, :phone, :alternative_phone

      field(:country_name) { |a| a.country&.name }
      field(:state_name) { |a| a.state&.name || a.state_name }
    end
  end
end
