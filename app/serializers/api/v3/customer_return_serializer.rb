# frozen_string_literal: true

module Api
  module V3
    class CustomerReturnSerializer < BaseSerializer
      identifier :id

      fields :number, :stock_location_id, :store_id, :public_metadata, :private_metadata, :created_at, :updated_at

      field :order_id do |object|
        object&.order&.id
      end

      field :order_number do |object|
        object&.order&.number
      end

      field :pre_tax_total do |object|
        object&.pre_tax_total
      end

      field :fully_reimbursed do |object|
        object&.fully_reimbursed?
      end

      field :return_authorization_ids do |object|
        id_arr = []
        object&.return_authorizations&.uniq&.each do |r|
          id_arr.push(r&.id)
        end
        id_arr
      end

      field :reimbursements do |object|
        object&.reimbursements&.map do |r|
          {
            # Spree::Reimbursement column data
            id: r.id,
            number: r.number,
            reimbursement_status: r.reimbursement_status,
            customer_return_id: r.customer_return_id,
            order_id: r.order_id,
            total: r.total,
            created_at: r.created_at,
            updated_at: r.updated_at
          }
        end
      end

      field :return_items do |object|
        object&.return_items&.map do |r|
          {
            # Spree::ReturnItem column data
            id: r.id,
            return_authorization_id: r.return_authorization_id,
            inventory_unit_id: r.inventory_unit_id,
            exchange_variant_id: r.exchange_variant_id,
            created_at: r.created_at,
            updated_at: r.updated_at,
            pre_tax_amount: r.pre_tax_amount,
            included_tax_total: r.included_tax_total,
            additional_tax_total: r.additional_tax_total,
            reception_status: r.reception_status,
            acceptance_status: r.acceptance_status,
            customer_return_id: r.customer_return_id,
            reimbursement_id: r.reimbursement_id,
            acceptance_status_errors: r.acceptance_status_errors,
            preferred_reimbursement_type_id: r.preferred_reimbursement_type_id,
            override_reimbursement_type_id: r.override_reimbursement_type_id,
            resellable: r.resellable,
            reason: r.reason,
            return_authorization_reason_id: r.return_authorization_reason_id,
            # extral data
            editable: r&.inventory_unit&.shipped? && !r&.return_authorization&.customer_returned_items? && r&.reimbursement.nil?,
            return_quantity: r.return_quantity,
            quantity: r&.inventory_unit&.quantity,
            variant_name: r&.inventory_unit&.variant&.name,
            sku: r&.inventory_unit&.variant&.sku,
            inventory_unit_state: r&.inventory_unit&.state,
            eligible_exchange_variants: r&.eligible_exchange_variants&.map do |v|
              {
                id: v&.id,
                sku: v&.sku,
                variant_name: v&.name
              }
            end
          }
        end
      end
    end
  end
end
