# frozen_string_literal: true

module Api
  module V3
    class ImageSerializer < BaseSerializer
      identifier :id

      fields :alt, :position
      field :viewable_id, name: :variant_id
      field(:variant_is_master) do |image|
        image.viewable.is_a?(Spree::Variant) && image.viewable.is_master?
      end
      field :variant_name do |image|
        if image.viewable.is_a?(Spree::Variant)
          unless image.viewable.is_master?
            image.viewable.sku_and_options_text
          end
        end
      end
      field :styles do |image|
        image.class.styles.map do |name, size|
          begin
            width, height = size.chop.split('x').map(&:to_i)
            {
              style: name,
              url: image.generate_url(size: size),
              size: size,
              width: width,
              height: height
            }
          rescue => e
            Rails.logger.error("Failed to process style #{name} with size #{size}: #{e.message}")
            nil
          end&.compact
        end
      end

      view :index do
        excludes :variant_name
      end
    end
  end
end
