# frozen_string_literal: true

module Api
  module V3
    class CmsSectionSerializer < BaseSerializer
      identifier :id

      field(:type) { |p| p.type.demodulize.underscore }
      fields :position, :name, :visible,
        :settings, :content, :fit, :destination,
        :linked_resource_type, :linked_resource_id

      field(:properties) do |section|
        section.properties.as_json
          .tap { |r|
            if r.key?('collection_id')
              r['collection'] = ::Spree::ListingCollection.find_by(id: r['collection_id'])
                                  .then { |collection| ::Spree::Listing.where(id: collection&.listing_ids || []) }
                                  .map { |l| ListingSerializer.render_as_json(l, view: :full) }
            end
          }
      end

      view :homepage do
        excludes :settings, :content, :fit, :destination, :linked_resource_type, :linked_resource_id
      end
    end
  end
end
