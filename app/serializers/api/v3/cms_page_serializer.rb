# frozen_string_literal: true

module Api
  module V3
    class CmsPageSerializer < BaseSerializer
      identifier :id

      field(:type) { |p| p.type.demodulize.underscore.sub(/_?page/, '') }
      fields :locale, :title, :slug, :visible

      view :full do
        fields :content, :meta_title, :meta_description
        association :cms_sections, blueprint: CmsSectionSerializer, name: :sections
      end

      view :homepage do
        excludes :type, :title, :slug, :visible
        association :cms_sections, blueprint: CmsSectionSerializer, name: :sections, view: :homepage
      end
    end
  end
end
