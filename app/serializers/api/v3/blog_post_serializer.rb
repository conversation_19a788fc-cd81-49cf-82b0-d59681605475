# frozen_string_literal: true

module Api
  module V3
    class BlogPostSerializer < BaseSerializer
      identifier :id

      fields :author_name, :title, :slug, :visible, :description, :created_at, :updated_at
      fields :published_at, :summary, :position, :views_count, :likes_count, :tag_list
      fields :meta_title, :meta_description, :meta_keywords

      field :category do |blog|
        blog.blog_category&.slice(:id, :name) || {}
      end

      field :feature_image do |blog|
        if blog.feature_image.attached?
          Rails.application.routes.url_helpers.rails_blob_path(blog.feature_image, only_path: true)
        end
      end
    end
  end
end
