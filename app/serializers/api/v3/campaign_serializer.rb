# frozen_string_literal: true

module Api
  module V3
    class CampaignSerializer < BaseSerializer
      identifier :id

      fields :title, :scheduled_date, :status, :template_id, :sender_email, :sent_count, :sent_count, :store_id
      fields :delivered_count, :opened_count, :clicked_count, :unsubscribed_count, :created_at, :updated_at

      field :template_title do |campaign|
        campaign.template&.template_name
      end
    end
  end
end
