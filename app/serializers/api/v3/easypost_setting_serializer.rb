# frozen_string_literal: true

module Api
  module V3
    class EasypostSettingSerializer < BaseSerializer
      identifier :id

      fields :customs_signer, :customs_contents_type, :customs_eel_pfc, :carrier_accounts_shipping, :carrier_accounts_returns
      fields :endorsement_type, :returns_stock_location_id, :buy_postage_when_shipped, :use_easypost_on_frontend
      fields :validate_address, :store_id, :created_at, :updated_at, :key, :name
    end
  end
end
