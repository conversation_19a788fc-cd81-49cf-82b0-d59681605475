# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module V2
      module Storefront
        module User<PERSON>erializerDecorator
          def self.prepended(base)
            base.deprecated_cache_options(
              store: Rails.cache,
              namespace: "jsonapi-serializer",
              expires_in: ::Spree::Api::Config[:api_v2_serializers_cache_ttl],
            )

            base.attribute(:roles) do |user|
              if user.spree_admin?
                [{ role: "admin" }]
              else
                user.role_users.map { |ru| { role: ru.role&.name, store: ru.store.url } }
              end
            end

            base.attribute(:store_credits) do |user|
              user.store_credits.map do |item|
                item.currency + ": " + item.amount.to_f.to_s
              end
            end
          end
        end
      end
    end
  end
end

Spree::V2::Storefront::UserSerializer.prepend(AxelSpree::Spree::V2::Storefront::UserSerializerDecorator)
