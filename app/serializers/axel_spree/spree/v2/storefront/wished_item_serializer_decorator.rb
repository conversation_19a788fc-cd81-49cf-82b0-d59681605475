# frozen_string_literal: true

module Axel<PERSON>pree
  module Spree
    module V2
      module Storefront
        module WishedItemSerializerDecorator
          include ::Spree::Api::V2::Wished<PERSON><PERSON><PERSON><PERSON><PERSON>
          def self.prepended(base)
            base.attribute(:price) do |item, params|
              item.listing.present? ? find_price(item) : item.price(currency: params[:currency])
            end

            base.attribute(:total) do |item, params|
              item.listing.present? ? find_total(item) : item.total(currency: params[:currency])
            end

            base.attribute(:display_price) do |item, params|
              item.listing.present? ? dispay_amount(find_price(item), params) : item.display_price(currency: params[:currency]).to_s
            end

            base.attribute(:display_total) do |item, params|
              item.listing.present? ? dispay_amount(find_total(item), params) : item.display_total(currency: params[:currency]).to_s
            end

            base.attribute(:product_name) do |item|
              item.listing.present? ? item.listing.title : item.variant&.product&.name
            end

            base.attribute(:product_slug) do |item|
              item.variant&.product&.slug
            end

            base.attribute(:variant_sku) do |item|
              item.variant&.sku
            end

            base.attribute(:temporary_unavailable) do |item|
              item.product.temporary_unavailable
            end

            base.attribute(:variant_image_path) do |item|
              fetch_variant_image(item)
            end

            base.belongs_to(:listing) do |item|
              item.listing.presence || item.product&.last_active_listing
            end


            base.belongs_to(:product)
          end

          private

            def self.find_price(item)
              item.listing.listing_inventories&.find_by(variant_id: item.variant.id)&.price || 0
            end

            def self.find_total(item)
              find_price(item) * item.quantity
            end

            def self.dispay_amount(amount, params)
              ::Spree::Money.new(amount, currency: params[:currency]).to_s
            end
        end
      end
    end
  end
end


Spree::V2::Storefront::WishedItemSerializer.prepend(AxelSpree::Spree::V2::Storefront::WishedItemSerializerDecorator)
