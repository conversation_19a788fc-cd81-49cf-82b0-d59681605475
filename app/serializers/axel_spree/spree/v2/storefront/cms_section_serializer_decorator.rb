# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module V2
      module Storefront
        module CmsSectionSerializerDecorator
          def self.prepended(base)
            base.attribute(:content, &:gen_content)
          end
        end
      end
    end
  end
end

Spree::V2::Storefront::CmsSectionSerializer.prepend(AxelSpree::Spree::V2::Storefront::CmsSectionSerializerDecorator)
