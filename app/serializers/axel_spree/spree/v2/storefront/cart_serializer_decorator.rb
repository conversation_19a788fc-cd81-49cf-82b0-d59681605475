# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module V2
      module Storefront
        module CartSerializerDecorator
          def self.prepended(base)
            base.cache_options(enabled: false)

            base.attribute(:invoice_no) do |order|
              order.order_invoice&.number
            end

            base.attribute(:item_count) do |order|
              sum = 0
              order.line_items.each do |line_item|
                # pack_size = line_item.listing&.pack_size_value || 1
                pack_size = line_item.pack_size
                sum += line_item.quantity/pack_size
              end
              sum
            end

            base.attribute(:contains_subscription) do |order|
              order.line_items.reload.map(&:subscription_enabled).include?(true)
            end

            base.attribute(:next_delivery_date) do |order|
              order.line_items.reload.select(&:subscription_enabled).map(&:subscription).compact
                   .select { |sub| sub.status == "subscribe" }
                   .min_by(&:next_delivery_date)&.next_delivery_date&.strftime("%m/%d/%Y")
            end

            base.attribute(:local_pickup)

            base.attribute(:cart_in_stock) do |order|
              order.line_items.all? do |li|
                li.variant.variant_total_available >= li.quantity && li.product.available? && !li.product.temporary_unavailable
              end if order.line_items.present?
            end

            base.attribute(:cart_in_stock_reason) do |order|
              reasons = []
              order.line_items.map do |line_item|
                reasons << I18n.t(
                  "serializers.cart.quantity_error_message",
                  product: line_item.product.name,
                ) if line_item.variant.variant_total_available < line_item.quantity
                if !line_item.product.available? || line_item.product.temporary_unavailable
                  reasons << I18n.t("serializers.cart.availability_error_message", product: line_item.product.name)
                end
              end
              reasons.empty? ? nil : reasons.join(", ")
            end
          end

          ::Spree::V2::Storefront::CartSerializer.prepend(self)
        end
      end
    end
  end
end
