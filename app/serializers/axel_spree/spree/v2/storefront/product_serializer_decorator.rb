# frozen_string_literal: true

module Axel<PERSON>pree
  module Spree
    module V2
      module Storefront
        module ProductSerializerDecorator
          include ::Spree::Api::V2::ProductHelper
          def self.prepended(base)
            base.cache_options(enabled: false)
            base.attributes(:meta_title)
            base.attributes(:temporary_unavailable)

            base.attribute(:stores) do |product, params|
              if !params[:store].nil? && !params[:store].default
                []
              else
                product.stores.pluck(:url)
              end
            end

            base.attribute(:name) do |product, params|
              listing = get_product_listing(product, params)
              listing.present? ? listing.title : published_name(product)
            end

            base.attribute(:description) do |product, params|
              listing = get_product_listing(product, params)
              listing.present? ? listing.description : description(product)
            end

            base.attribute(:variant_id) do |product|
              product.respond_to?(:variant_id) ? product.variant_id : nil
            end

            base.attribute(:listing_id) do |product, params|
              get_listing_id(product, params)
            end

            base.has_one(:default_variant) do |product, params|
              listing = get_product_listing(product, params)
              listing_variants_ids = listing&.listing_inventories&.select{|inv| inv.is_selected == true }&.map(&:variant_id)
              variants = find_variants(product, listing_variants_ids)
              variants.ids&.include?(product.default_variant.id) ? product.default_variant : variants.take
            end

            base.has_many(:variants) do |product, params|
              listing = get_product_listing(product, params)
              listing_variants_ids = listing&.listing_inventories&.select{|inv| inv.is_selected == true }&.map(&:variant_id)
              product.variants.where(id: listing_variants_ids).presence || product.variants
            end

            base.attribute(:subscription_details) do |product, params|
              listing = get_product_listing(product, params)
              subscription_details(listing)
            end

            base.attribute(:delivery_every) do |_product, _params|
              ::Spree::Subscription::DELIVERY_INTERVALS
            end

            base.attribute(:frequently_boughts) do |product, params|
              product_listing_id = get_listing_id(product, params)
              next [] if product_listing_id.blank?

              product_listing = ::Spree::Listing.find_by(id: product_listing_id)
              next [] if product_listing.nil?

              frequently_boughts = product_listing.actived_frequently_boughts
              serialized = ::Spree::V2::Storefront::FrequentlyBoughtSerializer.new(frequently_boughts).serializable_hash

              serialized[:data].map { |item| item[:attributes] }
            end

            base.attribute(:listing_images) do |product, params|
              listing_images(product, params)
            end

            base.has_many(:match_its)
            base.has_many(:digitals)
            base.has_many(:listings) do |product, params|
              product.all_active_product_listings.find_by(id: get_listing_id(product, params))
            end
          end
        end
      end
    end
  end
end

Spree::V2::Storefront::ProductSerializer.prepend(AxelSpree::Spree::V2::Storefront::ProductSerializerDecorator)
