# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module V2
      module Storefront
        module ProductPropertySerializerDecorator
          def self.prepended(base)
            base.attributes(:show_property)
          end
        end
      end
    end
  end
end

Spree::V2::Storefront::ProductPropertySerializer.prepend(AxelSpree::Spree::V2::Storefront::ProductPropertySerializerDecorator)
