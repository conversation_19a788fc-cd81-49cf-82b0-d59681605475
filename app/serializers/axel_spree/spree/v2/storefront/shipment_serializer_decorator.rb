# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module V2
      module Storefront
        module ShipmentSerializerDecorator
          def self.prepended(base)
            base.has_many(:inventory_units, serializer: :shipping_inventory_unit)
          end
        end
      end
    end
  end
end

Spree::V2::Storefront::ShipmentSerializer.prepend(AxelSpree::Spree::V2::Storefront::ShipmentSerializerDecorator)
