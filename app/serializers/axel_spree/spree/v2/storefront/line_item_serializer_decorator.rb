# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module V2
      module Storefront
        module LineItemSerializerDecorator
          include ::Spree::Api::V2::Line<PERSON><PERSON><PERSON><PERSON><PERSON>
          def self.prepended(base)
            base.cache_options(enabled: false)
            base.attributes(:sku)
            base.attribute(:shipment_ids) do |line_item|
              line_item.inventory_units.map(&:shipment_id)
            end

            base.attribute(:current_price) do |line_item|
              line_item.reload
              if line_item.storefront_sale_channel?
                line_item.price
              elsif line_item.variant.reload.volume_prices.present? || line_item.variant.volume_price_models.any?
                vprice = line_item.variant.volume_price(line_item.quantity, nil)
                [vprice, line_item.variant.price].min
              else
                line_item.variant.price
              end
            end

            base.attribute(:price) do |line_item|
              line_item.reload.price
            end

            base.attribute(:is_subscribed) do |line_item|
              line_item.subscription_enabled
            end

            base.attribute(:next_delivery_date) do |line_item|
              line_item.subscription_enabled ? line_item.subscription&.next_delivery_date&.to_date : nil
            end

            base.attribute(:name) do |line_item|
              line_item.listing&.title || line_item.product&.name
            end

            base.attribute(:available_quantity) do |line_item|
              # pack_size = line_item.listing&.pack_size_value || 1
              pack_size = line_item.pack_size
              total_available = line_item.variant.variant_total_available <= 0 ? 0 : line_item.variant.variant_total_available
              total_available / pack_size
            end
            
            base.attribute(:quantity) do |line_item|
              # pack_size = line_item.listing&.pack_size_value || 1
              pack_size = line_item.pack_size
              line_item.quantity / pack_size
            end

            base.attribute(:display_total) do |line_item, params|
              line_item.reload
              if line_item.storefront_sale_channel?
                # pack_size = line_item.listing&.pack_size_value || 1
                pack_size = line_item.pack_size
                total = (line_item.quantity/pack_size) * line_item.price
                ::Spree::Money.new(total, currency: params[:currency]).to_s
              else
                line_item.display_total
              end
            end

            base.attribute(:variant_image_path) do |line_item|
              fetch_variant_image(line_item)
            end

            base.has_one(:product)
            base.belongs_to(:listing)
          end
        end
      end
    end
  end
end

Spree::V2::Storefront::LineItemSerializer.prepend(AxelSpree::Spree::V2::Storefront::LineItemSerializerDecorator)
