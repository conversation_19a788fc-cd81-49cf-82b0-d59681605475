# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module V2
      module Storefront
        module CmsPageSerializerDecorator
          def self.prepended(base)
            base.attribute(:visible, &:visible?)
          end
        end
      end
    end
  end
end

Spree::V2::Storefront::CmsPageSerializer.prepend(AxelSpree::Spree::V2::Storefront::CmsPageSerializerDecorator)
