# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module V2
      module Storefront
        module VariantSerializerDecorator
          def self.prepended(base)
            base.cache_options(enabled: false)
            base.has_many(:digitals)

            base.attribute(:listing_inventories) do |variant, params|
              variant.listing_inventories.each_with_object({}) do |inventory, result|
                variant = inventory.variant
                if inventory.listing.present?
                  pack_size = inventory.listing&.pack_size_value || 1
                  total_stock_available = variant.variant_total_available <= 0 ? 0 : variant.variant_total_available
                  result[inventory[:listing_id]] = {
                    id: inventory.id,
                    price: inventory.price,
                    display_price: ::Spree::Money.new(inventory.price, currency: params[:currency]).to_s,
                    compare_at_price: inventory.compare_at_price,
                    compare_to_price: inventory.compare_to_price,
                    in_stock: pack_size <= total_stock_available,
                    available_quantity: total_stock_available / pack_size
                  }
                end
              end
            end

            base.has_many(:images) do |variant|
              if variant&.images&.size >= 1
                variant&.images
              else
                variant&.product&.master&.images
              end
            end
          end
        end
      end
    end
  end
end

Spree::V2::Storefront::VariantSerializer.prepend(AxelSpree::Spree::V2::Storefront::VariantSerializerDecorator)
