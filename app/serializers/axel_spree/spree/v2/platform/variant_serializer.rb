# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module V2
      module Platform
        class VariantSerializer < ::Spree::Api::V2::Platform::VariantSerializer
          attribute :full_options_text

          attribute(:compare_to_price) do |product, params|
            compare_to_price(product, params[:currency])
          end

          attribute(:display_compare_to_price) do |product, params|
            display_compare_to_price(product, params[:currency])
          end
        end
      end
    end
  end
end
