# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module PriceSerializerDecorator
            def self.prepended(base)
              base.attribute(:display_compare_to_price_including_vat_for) do |price, params|
                price.display_compare_to_price_including_vat_for(params[:price_options].presence || {}).to_s
              end
            end
          end
        end
      end
    end
  end
end

if Spree::Price.table_exists?
  Spree::Api::V2::Platform::PriceSerializer.prepend(AxelSpree::Spree::Api::V2::Platform::PriceSerializerDecorator)
end
