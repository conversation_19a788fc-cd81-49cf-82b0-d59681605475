# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module ProductSerializerDecorator
            def self.prepended(base)
              base.cache_options(enabled: false)
              base.attributes(:meta_title)

              base.attribute(:compare_to_price) do |product, params|
                base.compare_to_price(product, params[:currency])
              end

              base.attribute(:display_compare_to_price) do |product, params|
                base.display_compare_to_price(product, params[:currency])
              end

              base.attribute(:name) do |product, params|
                listing = get_product_listing(product, params)
                listing.present? ? listing.title : product.name
              end

              private

                def self.get_listing_id(product, params)
                  params[:listing_id]&.to_i || (params[:controller] == "products" ? product.listing_id : product.last_active_listing&.id)
                end

                def self.get_product_listing(product, params)
                  product.all_active_product_listings.find_by(id: get_listing_id(product, params))
                end
            end
          end
        end
      end
    end
  end
end

if Spree::Product.table_exists?
  Spree::Api::V2::Platform::ProductSerializer.prepend(AxelSpree::Spree::Api::V2::Platform::ProductSerializerDecorator)
end
