# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module DisplayMoneyHelperDecorator
          # rubocop:disable Lint/NestedMethodDefinition
          def self.prepended(base)
            base.class_methods do
              def find_price(product_or_variant, currency)
                product_or_variant.price_in(currency)
              end

              def compare_to_price(product_or_variant, currency)
                price = find_price(product_or_variant, currency)
                return if price.new_record? || price.compare_to_amount.blank?

                price.compare_to_amount
              end

              def display_compare_to_price(product_or_variant, currency)
                price = find_price(product_or_variant, currency)
                return if price.new_record? || price.compare_to_amount.blank?

                ::Spree::Money.new(price.compare_to_amount, currency: currency).to_s
              end
            end
          end
          # rubocop:enable Lint/NestedMethodDefinition
        end
      end
    end
  end
end

Spree::Api::V2::DisplayMoneyHelper.prepend(AxelSpree::Spree::Api::V2::DisplayMoneyHelperDecorator)
