# frozen_string_literal: true

module Axel<PERSON>pree
  module Spree
    module BaseHelperDecorator
      # This helper, similar to spree core routes helper, that allow generate only path
      def cdn_image_path(model, options = {})
        begin
          if model.respond_to?(:signed_id)
            name = :rails_service_blob_proxy
            args = [
              model.signed_id,
              model.filename,
              options,
            ]
          else
            name = :rails_blob_representation_proxy
            args = [
              model.blob.signed_id,
              model.variation.key,
              model.blob.filename,
              options,
            ]
          end
          main_app.public_send(:"#{name}_path", *args)
        rescue StandardError => e
          Rails.logger.error("[cdn_image_path] Error while generating path for model: #{e.message}")
          nil
        end
      end

      def create_product_image_tag(image, product, options, style)
        options[:alt] = (image.alt.presence || product.name)
        image_url = image.attachment.variable? ? image.url(style) : image.placeholder(style)
        image_tag(main_app.url_for(image_url), options)
      end

      def product_small_image(product)
        img = product.images.first
        return "" if img.nil?

        main_app.url_for(img.url(:small))
      end

      def spree_storefront_resource_url(resource, options = {})
        if defined?(locale_param) && locale_param.present?
          options[:locale] = locale_param
        end

        localize = ""

        if resource.instance_of?(::Spree::Product)
          listing = resource.last_active_listing
          listing&.generate_url(current_store)
        elsif resource.instance_of?(::Spree::Taxon)
          "#{current_store.formatted_url + localize}/#{::Spree::Config[:storefront_taxons_path]}/#{resource.permalink}"
        elsif resource.instance_of?(::Spree::Cms::Pages::FeaturePage) || resource.instance_of?(::Spree::Cms::Pages::StandardPage)
          "#{current_store.formatted_url + localize}/#{::Spree::Config[:storefront_pages_path]}/#{resource.slug}"
        elsif localize.blank?
          current_store.formatted_url
        else
          current_store.formatted_url + localize
        end
      end

      def link_to_tracking(shipment, options = {})
        return unless shipment.tracking

        options[:target] ||= :blank
        if shipment.tracking_url
          link_to(shipment.tracking, shipment.tracking_url, options)
        else
          content_tag(:span, shipment.tracking)
        end
      end

      def pretty_time(time)
        return "" if time.blank?

        time.strftime("%Y/%m/%d %l:%M %p %Z")
      end

      def folder_size(path)
        files = Dir.glob(File.join(path, "**/*"), File::FNM_DOTMATCH)
        files.filter_map { |f| File.size?(f) }.compact.sum
      end

      def invalid_email?(email)
        Mail::Address.new(email).domain
        if email.blank? || invalid_ebay_email?(email) || invalid_am_email?(email)
          return true
        end

        false
      rescue Mail::Field::IncompleteAddressError
        true
      end

      private

      # ebay's guest user will use <NAME_EMAIL> to create order
      def invalid_ebay_email?(email)
        pattern = /\A[a-zA-Z0-9_]{20}@members\.ebay\.com\z/
        email.match?(pattern)
      end

      # am's guest user will use <NAME_EMAIL> to create order
      def invalid_am_email?(email)
        pattern = /\A[a-zA-Z0-9_]{35}@mail\.co\z/
        email.match?(pattern)
      end
    end
  end
end

Spree::BaseHelper.prepend(AxelSpree::Spree::BaseHelperDecorator)
