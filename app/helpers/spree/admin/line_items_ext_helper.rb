# frozen_string_literal: true

module Spree
  module Admin
    module LineItemsExtHelper
      def process_line_items_ext(line_items_ext, line_item = nil, order = nil)
        order&.reload
        
        shipment_fee_total = get_shipment_total(order)
        paypal_fee_total = calculate_fee(order, "paypal")
        stripe_fee_total = calculate_fee(order, "stripe")
        shipment_fee_tool = SalesReport::Order::FeeTool.new(shipment_fee_total, sub_total(order), 4)
        paypal_fee_tool = SalesReport::Order::FeeTool.new(paypal_fee_total, sub_total(order), 4)
        stripe_fee_tool = SalesReport::Order::FeeTool.new(stripe_fee_total, sub_total(order), 4)
        is_final = order.line_items.last.id == line_item.id
        
        marketplace_fee = if order.is_walmart? && is_final
          if walmart_commission(order).blank?
            0.0
          else
            walmart_commission(order)
          end
        elsif order.is_ebay?
          order_ebay_fees(order, line_item)
        else
          0.0
        end

        ad_fee = if order.is_ebay?
          get_ad_fee(order, line_item)
        else
          0.0
        end

        actual_shipping = if order.is_walmart? && is_final
          if walmart_shipping_cost(order).blank?
            convert_fee(order, shipment_fee_tool, line_item, is_final)
          else
            walmart_shipping_cost(order)
          end
        elsif order.is_ebay?
          convert_fee(order, shipment_fee_tool, line_item, is_final)
        else
          convert_fee(order, shipment_fee_tool, line_item, is_final)
        end
        
        line_items_ext.assign_attributes(
          additional_data: {
            "qty": line_item&.quantity / line_item&.pack_size,
            "unit_price": unit_price(order, line_item),
            "total": (order&.total&.to_f - sale_tax(line_item)&.to_f).round(2),
            #"total": (line_item&.total&.to_f - sale_tax(line_item)&.to_f).round(2),
            "sale_tax": sale_tax(line_item),
            "unit_cost": unit_cost(line_item),
            "cogs": cogs(line_item),
            "actual_shipping": actual_shipping,
            "envelope": nil,
            "marketplace_fee": marketplace_fee, 
            "ad_fee": ad_fee,
            "tracking": line_item&.inventory_units&.first&.shipment&.tracking,
            "profit": profit(order, line_item),
            "complimentary": order[:complimentary_items], 
            "lot": vin(line_item),
            "vendor_receipt_date": vrd(line_item),
            "vendor_receipt_number": vrn(line_item),
            "expiry_date": item_expiry_date(line_item),
            "vendor_base_cost": (unit_cost(line_item) / line_item&.pack_size).round(2),
            "vendor_total_cost": (unit_cost(line_item) * line_item&.quantity / line_item&.pack_size).round(2)
          }
        )

        line_items_ext.save
      end

      private

      def unit_price(order, line_item)
        if order.sale_channel_metadata.present? && order.channel == "eBay"
          return 0.0 if line_item.ebay_line_item_id.blank?
  
          item_mapper = ebay_line_item_cost_mapper(order)
          ret = item_mapper.dig(line_item.ebay_line_item_id, line_item.currency)
          return 0.0 if ret.blank?
  
          ret
        else
          line_item&.price&.to_f
        end
      end

      def sale_tax(line_item)
        ebay_tax_adjustments = line_item.adjustments&.where(label: ["eBay_tax", "walmart_tax", "Amazon_tax"])
        if ebay_tax_adjustments.any?
          ebay_tax_adjustments.take.amount.to_f
        else
          0.00
        end
      end

      def ebay_line_item_cost_mapper(order)
        hash_mapper = {}
        order.sale_channel_metadata.dig("lineItems").each do |line_item|
          costs = {}
          currency = line_item.dig("lineItemCost", "currency")
          value = line_item.dig("lineItemCost", "value")
          costs[currency] = (value.to_f / line_item.dig("quantity"))
          hash_mapper = hash_mapper.merge({ line_item.dig("lineItemId") => costs })
        end
        hash_mapper
      end

      def cogs(line_item)
        # line_item.stock_item_units.pluck(:vendor_inventory_cost)&.sum(&:to_f) if line_item.stock_item_units.any?
        line_item.stock_item_units.pluck(:vendor_inventory_cost)&.sum(&:to_f)&.round(2) if line_item.stock_item_units.any?
      end
  
      def unit_cost(line_item)
        cogs = cogs(line_item) || 0.0
        # (cogs / line_item.quantity)&.round(2)
        (cogs / line_item.quantity * line_item.pack_size)&.round(2)
      end

      def get_shipment_total(order)
        order.order_packages.sum do |op|
          if op.selected_shipping_rate&.cost.to_f > 0
            op.selected_shipping_rate&.cost.to_f
          elsif op.shipments.first&.cost.to_f > 0
            op.shipments.first&.cost.to_f
          else
            0
          end
        end
      end
  
      def convert_fee(order, fee_tool, line_item, is_final = false)
        is_final ? fee_tool.residue : fee_tool.convert(unit_price(order, line_item) * line_item.quantity)
      end
      
      def calculate_fee(order, type)
        return if order.payments.none?
  
        fee = 0
        payments = order.payments.where.not(state: "pending")
  
        relevant_payment_method_types = {
          "paypal" => "Spree::Gateway::PayPalExpress",
          "stripe" => "Spree::Gateway::StripeElementsGateway",
        }
  
        payment_method_type = relevant_payment_method_types[type]
  
        payments.each do |payment|
          next unless payment.payment_method.type == payment_method_type
  
          time_difference_in_minutes = ((Time.zone.now.to_time - payment.updated_at.to_time) / 60).to_i
  
          if payment_method_type == "Spree::Gateway::PayPalExpress" && time_difference_in_minutes < 20
            return "PayPal fee will be updated after 20 minutes"
          elsif payment_method_type == "Spree::Gateway::StripeElementsGateway" && time_difference_in_minutes < 20
            return "Stripe fee will be updated after 20 minutes"
          end
  
          fee += type == "paypal" ? payment.paypal_fees.to_f : payment.stripe_fees.to_f
        end
        line_item_count = [order.line_items.count, 1].max
        fee_per_line_item = fee / line_item_count
        fee_per_line_item
      end

      def sub_total(order)
        order&.item_total.to_f
      end

      def order_ebay_fees(order, line_item = nil)
        return 0 if order.ebay_fees.blank?
  
        fees = Hash.new(0)
        listing_count = Hash.new(0)
        item_mapper = line_item_mapper(order)
        item_mapper.each_value { |listing_id| listing_count[listing_id] += 1 }
        item_mapper.each do |line_item_id, listing_id|
          line_item_fees = order.ebay_fees.dig(line_item_id)
          total_fee = line_item_fees&.values&.sum
          total_fee /= listing_count.dig(listing_id)
          # fees[line_item_id] = total_fee&.truncate(2)&.to_f
          fees[line_item_id] = total_fee&.round(2)&.to_f
        end
        if line_item.present?
          fees[line_item&.ebay_line_item_id]
        else
          fees&.values&.sum
        end
      end

      def line_item_mapper(order)
        hash_mapper = {}
        order.sale_channel_metadata.dig("lineItems").each do |line_item|
          hash_mapper = hash_mapper.merge({ line_item.dig("lineItemId") => line_item.dig("legacyItemId") })
        end
        hash_mapper
      end

      def get_ad_fee(order, line_item = nil)
        return 0 if order.ebay_fees.blank? || order.ebay_fees.dig("ad_fee").blank?
  
        fees = Hash.new(0)
        listing_count = Hash.new(0)
        item_mapper = line_item_mapper(order)
        item_mapper.each_value { |listing_id| listing_count[listing_id] += 1 }
        item_mapper.each do |line_item_id, listing_id|
          total_fee = 0
          ad_fee = order.ebay_fees.dig("ad_fee", listing_id).to_f
          total_fee += ad_fee / listing_count.dig(listing_id)
          # fees[line_item_id] = total_fee&.truncate(2)&.to_f
          fees[line_item_id] = total_fee&.round(2)&.to_f
        end
  
        if line_item.present?
          fees[line_item&.ebay_line_item_id]
        else
          fees&.values&.sum
        end
      end

      def profit(order, line_item)
        shipping_charge_amount = if walmart_shipping_cost(order).blank?
            get_shipment_total(order)
          else
            walmart_shipping_cost(order).to_f
          end
  
        profit_total = order[:total].to_f - order_sale_tax(order).to_f - (cogs(line_item).to_f +
                                             shipping_charge_amount +
                                             order[:envelope_fee].to_f +
                                             order_ebay_fees(order, line_item) +
                                             get_ad_fee(order, line_item) +
                                             calculate_fee(order, "paypal").to_f +
                                             calculate_fee(order, "stripe").to_f +
                                             walmart_commission(order).to_f)
        # if order.is_walmart?
        #  profit_total -= order_sale_tax(order)
        # end
        # profit_total&.truncate(2)&.to_f
        profit_total&.round(2)&.to_f
      end

      def walmart_commission(order, line_item = nil)
        order.walmart_fees&.dig("commission") || ""
      end

      def walmart_shipping_cost(order, line_item = nil)
        order.walmart_fees&.dig("shipping_cost") || ""
      end

      def order_sale_tax(order)
        order.line_items.sum { |line_item| sale_tax(line_item).to_f }
      end

      def vin(line_item)
        line_item.stock_item_units&.pluck(:vendor_inventory_number)&.join(";")
      end

      def vrn(line_item)
        line_item.stock_item_units&.pluck(:vendor_receipt_number)&.join(";")
      end

      def vrd(line_item)
        vr_date = []
        line_item.stock_item_units.each do |stock_item_unit|
          vendor_reciept_date = stock_item_unit.vendor_receipt_date.to_s
          vr_date << format_date(vendor_reciept_date) if vendor_reciept_date.present?
        end
        vr_date.join(";")
      end

      def item_expiry_date(line_item)
        inventory_expiry_date = []
        line_item.stock_item_units.each do |stock_item_unit|
          expiry_date = stock_item_unit.show_expiry_date.to_s
          inventory_expiry_date << format_date(expiry_date) if expiry_date.present?
        end
        inventory_expiry_date.join(";")
      end

      def format_date(input_date)
        return "" if input_date.blank?
  
        Time.zone.parse(input_date)&.strftime("%d/%m/%Y")
      end

    end
  end
end
