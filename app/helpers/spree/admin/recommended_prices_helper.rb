# frozen_string_literal: true

module Spree
  module Admin
    module Recommended<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      def recommended_price_dump(object)
        Marshal.dump(object)
        object = ERB::Util.html_escape(object.to_yaml)
        content_tag(:pre, object, class: "debug_dump", style: "max-width: 1000px;")
      rescue # errors from <PERSON> or YAML
        content_tag(:code, object.inspect, class: "debug_dump", style: "max-width: 1000px;")
      end

      def recommended_price_log_dump(string)
        content_tag(:pre, string, class: "debug_dump", style: "max-width: 1000px;")
      end

      def datetime_ago(date)
        return "".html_safe unless date

        content_tag(
          :span,
          time_ago_in_words(date) + " ago",
          class: "datetime",
          data: { bs_toggle: "tooltip", bs_placement: "top", bs_title: date.to_s },
        )
      end
    end
  end
end
