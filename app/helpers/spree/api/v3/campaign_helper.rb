# frozen_string_literal: true

module Spree
  module Api
    module V3
      module CampaignHelper
        extend ActiveSupport::Concern

        def reschedule_campaign_emails(campaign)
          cancel_scheduled_emails(campaign)
          schedule_campaign_emails(campaign)
        end

        def schedule_campaign_emails(campaign)
          SendCampaignEmailsJob.set(wait_until: campaign.scheduled_date).perform_later(campaign.id)
        end

        def send_campaign_emails_now(campaign)
          SendCampaignEmailsJob.perform_later(campaign.id)
        end

        def cancel_scheduled_emails(campaign)
          Sidekiq::ScheduledSet.new.each do |job|
            next unless job.item["args"].is_a?(Array) && job.item["args"].first.is_a?(Hash)
            
            job_args = job.item["args"].first
            job_class = job_args["job_class"]
            arguments = job_args["arguments"]
            if job_class == "SendCampaignEmailsJob" && arguments.include?(campaign.id)
              puts "<<<<<<<<<<<<<<<<< Removing Job from queue for campaign #{campaign.id} >>>>>>>>>>>>>>>>>>>>>>"
              job.delete
            end
          end
        end
      end
    end
  end
end
