# frozen_string_literal: true

module Spree
  module Api
    module V3
      module ListingHelper
        extend ActiveSupport::Concern

        class_methods do
          def listing_images(listing, params)
            product = listing.product
            return if product.nil?

            # Group images by variant ID for faster lookup
            images_by_variant = listing.image_files.group_by { |img| img.filename.to_s.split("_")[1] }

            product.variants_including_master.map do |variant|
              listing_images = (images_by_variant[variant.id.to_s] || []).map do |img|
                next unless img.blob.present?
                {
                  position: listing.image_uploads.find_by(image_id: img.id)&.position.to_i,
                  blob_id: img.id,
                  image_url: Rails.application.routes.url_helpers.rails_blob_url(img.blob, only_path: true)
                }
              end&.compact&.sort_by { |img| img[:position] }

              {
                is_master: variant.is_master,
                variant_id: variant.id,
                option_text: variant.is_master? ? 'All' : "#{variant.sku} #{variant.options_text}"&.strip,
                descriptive_name: variant.descriptive_name,
                image_url_list: listing_images
              }
            end
          end

          def listing_variants(listing, params)
            product = fetch_product(listing, params)
            return if product.nil?
            product.variants.any? && product.option_types.any? ? variants_details(listing, product, params) : master_variant_details(listing, product, params)
          end

          def fetch_product(listing, params)
            params['updated_product_id'].present? ? Spree::Product.find_by(id: params['updated_product_id']) : listing.product
          end

          def variants_details(listing, product, params)
            product.variants.map { |variant| variant_data(listing, variant, params) }
          end

          def master_variant_details(listing, product, params)
            [variant_data(listing, product.master, params)]
          end

          def variant_data(listing, variant, params)
            published_variant = listing.variant_stock_items_data&.dig(variant.id.to_s)
            inventory = listing.listing_inventories.find_by(variant_id: variant.id)

            {
              id: variant.id,
              listing_inventory_id: inventory&.id,
              is_master: variant.is_master,
              option_text: "#{variant.sku} #{variant.options_text}"&.strip,
              descriptive_name: variant.descriptive_name,
              sku: published_variant&.dig('variant_sku') || variant.sku.to_s,
              quantity: determine_variant_quantity(inventory, variant, params),
              publish: published_variant_valid(listing, published_variant),
              min_vic: variant.min_vic,
              avg_vic: variant.avg_vic,
              max_vic: variant.max_vic,
              vic_vin: build_vic_vin(variant),
              upc: variant.upc,
              lbs: variant.lbs,
              oz: variant.oz,
              weight: variant.weight,
              height: variant.height,
              width: variant.width,
              depth: variant.depth,
              price: fetch_price(inventory, variant, params),
              compare_to_price: fetch_compare_to_price(inventory, variant, params),
              compare_at_price: fetch_compare_at_price(inventory, variant, params),
              total_listed_qty: inventory&.total_listed_qty.to_i,
              total_sold_qty: inventory&.total_sold_qty.to_i,
              total_available_qty: inventory&.total_available_qty.to_i,
              low_availability_toggle: inventory&.low_availability_toggle || false,
              low_availability_value: inventory&.low_availability_value.to_i,
              is_selected: inventory&.is_selected || false,
              volume_prices: listing.volume_prices.where(variant_id: variant.id).map { |vp| volume_price_data(vp) },
              subscription_details: listing.subscription_details["#{variant.id}"]&.symbolize_keys || {}
            }
          end

          def sample_subscription_data
            {
              discount_type: nil,
              recurring_discount: nil,
              first_time_discount: nil,
              subscription_toggle: nil,
              first_price_per_item: nil,
              recurring_price_per_item: nil,
              recurring_discount_checkbox: nil
            }
          end

          def published_variant_valid(listing, published_variant)
            return false unless published_variant

            (published_variant["publish"] == "true" || published_variant["publish"].nil?) && published_variant["variant_delete"].nil?
          end

          def determine_variant_quantity(inventory, variant, params)
            return variant.total_available if params['updated_product_id'].present?

            inventory.present? ? inventory.total_available_qty.to_i : variant.total_available
          end

          def fetch_price(inventory, variant, params)
            return variant.price if params['updated_product_id'].present?
            inventory.present? ? inventory.decimal_price : variant.price
          end

          def fetch_compare_to_price(inventory, variant, params)
            return variant.compare_to_price if params['updated_product_id'].present?

            inventory.present? ? inventory.compare_to_price : variant.compare_to_price
          end

          def fetch_compare_at_price(inventory, variant, params)
            return variant.compare_at_price if params['updated_product_id'].present?

            inventory.present? ? inventory.compare_at_price : variant.compare_at_price
          end

          def build_vic_vin(variant)
            variant.stock_items.flat_map(&:stock_item_units)
              .select { |siu| siu.vendor_inventory_cost.present? && siu.state == "stock" }
              .map { |siu| { vin: siu.vendor_inventory_number, vic: siu.vendor_inventory_cost } }
          end

          def volume_price_data(volume_price)
            {
              id: volume_price.id,
              name: volume_price.name,
              range: volume_price.range,
              amount: volume_price.amount,
              position: volume_price.position,
              role_id: volume_price.role_id,
              discount_type: volume_price.discount_type,
              _destroy: false
            }
          end

          def auction_pricing(listing, params)
            product = fetch_product(listing, params)
            return if product.nil?
            master_variant = product.master
            inventory = listing.listing_inventories.find_by(variant_id: master_variant.id)

            suggested_price = listing.listing_inventories.present? ? true : false
            auction_duration_value = listing.auction_pricing.present? ? listing.auction_pricing.first[1].dig("auction_duration") : nil

            {
              pricing_format: ["Buy It Now", "Auction"],
              auction_pricing: listing&.auction_pricing.present? ? true : false,
              auction_duration: fetch_auction_duration(listing),
              auction_duration_value: auction_duration_value,
              suggested_price: suggested_price,
              starting_bid: auction_pricing_data(listing, "starting_bid") || 0,
              buy_it_now: auction_pricing_data(listing, "buy_it_now").to_i || 0,
              reserve_price: auction_pricing_data(listing, "reserve_price").to_i || 0,
              price: fetch_price(inventory, master_variant, params),
              quantity: determine_variant_quantity(inventory, master_variant, params)
            }
          end

          def auction_pricing_data(listing, field)
            listing.auction_pricing.present? ? listing.auction_pricing.values[0].dig(field) : nil
          end

          def fetch_auction_duration(listing)
            return [] if listing.sale_channel&.brand != "eBay" || listing.category_id.blank?

            response = get_category_features(listing)

            extract_listing_durations(response) if response.present? && response.dig('GetCategoryFeaturesResponse','Ack') == "Success"
          end

          def extract_listing_durations(response)
            result = []

            # Extract the listing durations array from the response
            listing_durations = response.dig('GetCategoryFeaturesResponse', 'FeatureDefinitions', 'ListingDurations', 'ListingDuration')[0]&.dig('Duration') || []

            # Ensure listing_durations is an array and process it
            listing_durations = [listing_durations] unless listing_durations.is_a?(Array)

            # Format each duration
            formatted_durations = listing_durations.map do |duration|
              {
                value: duration,
                text: duration.match(/\d+/).to_s + ' Days'
              }
            end

            # Add the default "1 Day" option
            result.push({ value: 'Days_1', text: '1 Day' })
            result.concat(formatted_durations)

            result
          end

          def get_category_features(listing)
            sale_channel = listing.sale_channel
            return if sale_channel.oauth_application.blank?
            category_id = listing.category_id
            condition_id = listing&.condition_id

            get_category_features = Ebay::TradingApis::GetCategoryFeatures.new(
              listing.store.id,
              sale_channel.oauth_application.id
            ).get_category_features(category_id)

            get_category_features
          end

          def sell_as_lot(listing, params)
            sell_as_lot = listing.pack_size_toggle.present? ? true : false
            {
              sell_as_lot: sell_as_lot,
              pack_size: listing.pack_size_value
            }
          end

          def allow_offer(listing, params)
            allow_offer = listing.allow_offer.present? ? true : false
            {
              allow_offer: allow_offer,
              minimum_offer_price: listing&.minimum_offer_price || 0,
              autoaccept_offer_price: listing&.autoaccept_offer_price || 0
            }
          end
        end
      end
    end
  end
end
