# frozen_string_literal: true

module Spree
  module Api
    module V3
      module FrequentlyBoughtHelper
        extend ActiveSupport::Concern

        class_methods do
          def product_images(listing, params)
            product = listing.product
            return if product.nil?

            # Group images by variant ID for faster lookup
            images_by_variant = listing.image_files.group_by { |img| img.filename.to_s.split("_")[1] }

            product.variants_including_master.map do |variant|
              listing_images = (images_by_variant[variant.id.to_s] || []).map do |img|
                next unless img.blob.present?
                {
                  position: listing.image_uploads.find_by(image_id: img.id)&.position.to_i,
                  blob_id: img.id,
                  image_url: Rails.application.routes.url_helpers.rails_blob_url(img.blob, only_path: true)
                }
              end&.compact&.sort_by { |img| img[:position] }

              image_url_list = if listing_images.blank?
                product.variant_images.map do |image|
                  attachment = image.attachment
                  next unless attachment&.blob.present?
                  {
                    position: image.position,
                    blob_id: attachment.id,
                    image_url: Rails.application.routes.url_helpers.rails_blob_url(attachment, only_path: true)
                  }
                end.compact.sort_by { |img| img[:position] }
              else
                listing_images
              end

              {
                is_master: variant.is_master,
                variant_id: variant.id,
                option_text: variant.is_master? ? 'All' : "#{variant.sku} #{variant.options_text}"&.strip,
                descriptive_name: variant.descriptive_name,
                image_url_list: image_url_list
              }
            end
          end
        end
      end
    end
  end
end
