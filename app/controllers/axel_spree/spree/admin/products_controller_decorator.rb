# frozen_string_literal: true

require "fileutils"

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module ProductsControllerDecorator
        include ::Spree::BaseHelper
        include ::Spree::Admin::ProductsHelper
        include ::Spree::Admin::ElasticsearchHelper
        include UpcDataProcessing

        def self.prepended(base)
          base.skip_before_action(:load_resource, only: :compare_price)
          base.skip_before_action(:set_product_defaults, only: :new)
          base.after_action(:helper_sync_product_to_elasticsearch, only: [:create, :update, :set_active, :destroy, :clone])
          base.before_action(:check_permission, only: [:populate_vin_field, :edit, :batch_create_stock_item, :batch_create_stock_item_units]) # rubocop:disable Layout/LineLength
          base.before_action(:set_permissions, only: [:index, :destroy, :new, :edit, :stock, :import])
          base.helper("spree/products")
        end

        def collection
          base_collection
          if params.dig(:q, :search_by_name).present? && params[:action] == "search"
            es_listing = AxelSpree::Elasticsearch::Listing.new
            query_params = {
              title: params.dig(:q, :search_by_name),
            }
            ids = es_listing.backend(query_params).product_ids
            @collection = product_scope.where(id: ids)
          end
          super
        end

        def base_collection
          return @base_collection if @base_collection.present?

          new_params = {
            q: {
              deleted_at_null: "1",
              s: params.dig(:q, :s) || "name asc",
            },
          }

          @base_collection = product_scope.with_deleted

          # @search needs to be defined as this is passed to search_form_for
          # Temporarily remove params[:q][:deleted_at_null] from params[:q] to ransack products.
          # This is to include all products and not just deleted products.
          @base_search = @base_collection.ransack(new_params[:q].reject { |k, _v| k.to_s == "deleted_at_null" })
          @base_collection = @base_search.result
            .includes(product_includes)
            .page(params[:page])
            .per(params[:per_page] || ::Spree::Backend::Config[:admin_products_per_page])
          @base_collection
        end

        def handle_elasticsearch_param
          with_deleted = false
          with_discontinued = false
          query_by_elasticsearch = [:search_by_name, :variants_including_master_upc_cont, :variants_including_master_sku_cont].any? do |key|
            params.dig(:q, key).present?
          end

          sort = params.dig(:q, :s) == "name desc" ? "desc" : "asc"
          order = AxelSpree::Elasticsearch::Search::Order.new("keyword_name", sort).to_hash
          with_deleted = params.dig(:q, :deleted_at_null) == "0" if params.key?(:q) && params[:q].key?(:deleted_at_null)
          with_discontinued = params.dig(:q, :not_discontinued) != "1" if params.key?(:q) && params[:q].key?(:not_discontinued)
          taxons = []

          taxons_id_in = params.dig(:q, :taxons_id_in)
          if taxons_id_in.present?
            taxons_id_in.each do |taxon_id|
              if taxon_id.present?
                taxons << taxon_id
              end
            end
          end

          mode = params[:mode] || "standard"
          query_params = {
            name: params.dig(:q, :search_by_name),
            upc: params.dig(:q, :variants_including_master_upc_cont),
            sku: params.dig(:q, :variants_including_master_sku_cont),
            taxons: taxons,
            with_deleted: with_deleted,
            with_discontinued: with_discontinued,
          }

          es_product = AxelSpree::Elasticsearch::Product.new
          es_product.backend(query_params).order(order).mode(mode)
          if query_by_elasticsearch
            @collection.use_elasticsearch(es_product, @base_collection)
          end
        end

        def index
          session[:return_to] = request.url
          handle_elasticsearch_param
          respond_with(@collection)
        end

        def create
          invoke_callbacks(:create, :before)
          @object.attributes = permitted_resource_params
          if @object.save
            record_activity_log
            invoke_callbacks(:create, :after)
            flash[:success] = flash_message_for(@object, :successfully_created)
            respond_with(@object) do |format|
              format.html { redirect_to(location_after_save) }
              format.js   { render(layout: false) }
            end
          else
            invoke_callbacks(:create, :fails)
            respond_with(@object) do |format|
              format.html { render(action: :new, status: :unprocessable_entity) }
              format.js { render(layout: false, status: :unprocessable_entity) }
            end
          end
        end

        def destroy
          @product = product_scope.friendly.find(params[:id])
          if @permissions&.allow_product_delete
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            respond_with(@product) do |format|
              format.html { redirect_to(collection_url) }
              format.js { render_js_for_destroy }
            end
            return
          end

          begin
            # TODO: why is @product.destroy raising ActiveRecord::RecordNotDestroyed instead of failing with false result
            if @product.destroy
              record_activity_log
              flash[:success] = "Product deleted successfully"
            else
              flash[:error] = "There was an error: #{@product.errors.full_messages}"
            end
          rescue ::ActiveRecord::RecordNotDestroyed => e
            flash[:error] = "Something went wrong: #{e.message}"
          end

          respond_with(@product) do |format|
            format.html { redirect_to(collection_url) }
            format.js { render_js_for_destroy }
          end
        end

        def new
          if @permissions&.create_and_edit_product
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_products_path)
            return
          end
          @product&.abbreviation = generate_unique_abbreviation if params.dig(
            :product,
            :abbreviation,
          ).blank? && @product&.abbreviation.blank?
          super
        end

        def edit
          if @permissions&.create_and_edit_product
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_products_path)
            return
          end
          respond_with(@object) do |format|
            format.html { render(layout: !request.xhr?) }
            format.js   { render(layout: false) } if request.xhr?
          end
        end

        def update
          if params[:product][:taxon_ids].present?
            params[:product][:taxon_ids] = params[:product][:taxon_ids].reject(&:empty?)
          end
          if params[:product][:option_type_ids].present?
            params[:product][:option_type_ids] = params[:product][:option_type_ids].reject(&:empty?)
          end

          # Check for duplicate property names and issue a warning instead of an error

          if params.dig(:product, :product_properties_attributes).present?
            result = validate_unique_property_names

            if result[:warning].present?
              flash.now[:error] = result[:warning]
              invoke_callbacks(:update, :fails)
              respond_with(@object) do |format|
                format.html { render(:edit, status: :unprocessable_entity) }
              end
              return
            end

            params[:product][:product_properties_attributes].each do |_, attrs|
              next if attrs["property_name"].blank?

              property = ::Spree::Property.find_or_initialize_by(name: attrs["property_name"])

              if property.store_id.nil?
                property.store_id = current_store.id
              end

              if property.presentation.blank?
                property.presentation = attrs["property_name"].titleize
              end

              property.save! if property.changed?

              attrs["property_id"] = property.id
            end
          end
          invoke_callbacks(:update, :before)
          if @object.update(permitted_resource_params)
            record_activity_log
            set_current_store
            # convert weight into two fields as well as update the data accordingly
            convert_oz_to_lbs(@object)
            invoke_callbacks(:update, :after)
            flash[:success] = flash_message_for(@object, :successfully_updated)
            respond_with(@object) do |format|
              format.html { redirect_to(location_after_save) }
              format.js { render(layout: false) }
            end
          else
            # Stops people submitting blank slugs, causing errors when they try to
            # update the product again
            @product.slug = @product.slug_was if @product.slug.blank?
            invoke_callbacks(:update, :fails)
            respond_with(@object) do |format|
              format.html { render(:edit, status: :unprocessable_entity) }
            end
          end
        end

        def clone
          @product&.abbreviation = generate_unique_abbreviation
          super
        end

        def load_data
          @taxons = ::Spree::Taxon.for_store(current_store).i18n.includes(:translations).order(:name)
          @option_types = ::Spree::OptionType.order(:name)
          @tax_categories = ::Spree::TaxCategory.for_store(current_store).order(:name)
          @shipping_categories = ::Spree::ShippingCategory.for_store(current_store).order(:name)
        end

        def set_active
          product = product_scope.friendly.find(params[:id])
          current_active = product.active?
          if product.active?
            product.draft
          else
            product.activate
          end
          if product.errors.empty?
            flash[:success] = "Product #{product.name} is set as #{current_active ? "inactive" : "active"} now"
          else
            flash[:error] = "Product #{product.name} cannot be set as #{current_active ? "inactive" : "active"}. \
              #{product.errors.full_messages.join(", ")}"
          end

          redirect_to(spree.admin_products_path)
        end

        def import
          unless @permissions&.create_and_edit_product
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_products_path)
            return
          end
          file_path = params[:product][:file].path
          import_log = ::Spree::ImportLog.create!(
            started_by_email: spree_current_user.email,
            state: "created",
            store_id: current_store.id,
          )
          ProductsImport::Import.new(file_path, spree_current_user, import_log, current_store).perform_import
          message = "Product import successful."
          flash[:success] = message
          redirect_to(admin_products_path)
        end

        def stock
          if @permissions&.manage_stock
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(edit_admin_product_path(@product))
            return
          end
          @variants = @product.variants_including_master.includes(*variant_stock_includes)
          @stock_locations = ::Spree::StockLocation.accessible_by(current_ability).for_store(current_store)
          if @stock_locations.empty?
            flash[:error] = ::Spree.t(:stock_management_requires_a_stock_location)
            redirect_to(spree.admin_stock_locations_path)
          end
        end

        def search
          listing_collection = collection.select("spree_products.*, listings.id as listing_id, listings.title as listing_title")
            .joins(listings: :sale_channel)
            .where(listings: { status: "Active" })
            .where(sale_channel: { brand: "storefront" })

          render(json: listing_collection.with_status(:active).filter_map do |product|
                         next unless product.active?

                         {
                           listing_id: product.listing_id,
                           id: product.id,
                           name: product.listing_title,
                           sku: product.try(:sku),
                           total_on_hand: product.total_on_hand,
                           digitals: product.digitals.any?,
                           active: product.active?,
                           available: product.available?,
                           available_status: available_status_ex(product),
                           image_path: if default_image_for_product_or_variant(product).present?
                                         main_app.url_for(default_image_for_product_or_variant(product).url("small"))
                                       else
                                         ActionController::Base.helpers.asset_url("noimage/backend-missing-image.svg")
                                       end,
                         }
                       end)
        end

        def track_number_setting
        end

        def generate_unique_abbreviation
          loop do
            abbreviation = SecureRandom.hex(3).upcase
            break abbreviation unless ::Spree::Product.exists?(abbreviation: abbreviation)
          end
        end

        def search_product_to_add_stock
        end

        def scan_vin_to_add_stock
          @variant = ::Spree::Variant.find_by(id: params["variant"])
        end

        def product_title_or_upc_search
          @variants = if params.dig(:title_or_upc_search).present?
            ::Spree::Variant.filter_by_name_or_upc(params[:title_or_upc_search]).order(created_at: :desc)
          else
            []
          end
          respond_to do |format|
            format.turbo_stream do
              render(turbo_stream: [
                turbo_stream.update(
                  "search_results",
                  partial: "spree/admin/products/product_search_results",
                  locals: { variants: @variants },
                ),
              ])
            end
          end
        end

        def scan_and_fetch_upc
          if params["variant_upc"].present?
            variant_id = ::Spree::Variant.find_by(upc: params["variant_upc"])&.id
            render(json: { variant_id: variant_id })
          end
        end

        def fetch_upc_item_lookup
          resp = UpcItem::Lookup.new.get_product_details(params["submit-id"], "upcitemdb", current_store)
          item = ::Spree::ScannedItem.find_or_create_by(upc: params["submit-id"], user_id: spree_current_user.id)
          unless resp
            item.update!(status: :failed)
            raise StandardError, ::Spree.t("admin.products.invalid_upc")
          end

          SpreeInsertion::UpcItem::ScannedItem.new(resp[:items].first, item).execute
          item.update!(status: :completed)

          @scanned_item = ::Spree::ScannedItem.find_by(upc: params["submit-id"])
          @product = ::Spree::Product.new
          @product&.abbreviation = generate_unique_abbreviation
          @shipping_categories = current_store.shipping_categories.order(:name)
        rescue StandardError => e
          redirect_to(search_product_to_add_stock_admin_products_path(error_message: e.message))
        end

        def populate_vin_field
          if params["submit-id"].present?
            command = ::Admin::GetPricecompareInfoCommand.call(vin: params["submit-id"])
            stock_item_unit_response = command.result
          end
          stock_item_unit_response = stock_item_unit_response && stock_item_unit_response[:success] ? stock_item_unit_response : nil

          respond_to do |format|
            format.turbo_stream do
              render(
                turbo_stream: [
                  turbo_stream.prepend(
                    "search_vin_results",
                    partial: "spree/admin/products/search_vin_results",
                    locals: { stock_item_unit: stock_item_unit_response },
                  ),
                ],
              )
            end
          end
        end

        def create_product
          # Used a concern to create product
          process_upc_data(product_params, common_add_stock_flow: true)
        rescue StandardError
          flash[:error] = ::Spree.t("admin.products.product_not_created")
          redirect_to(admin_products_path)
        end

        def batch_create_stock_item
          @command = ::Admin::BatchCreateStockItemCommand.call(
            product: ::Spree::Product.find(params[:product_id]),
            stock_item_params: params[:stock_items],
            current_user: spree_current_user,
            action_place: admin_products_path,
          )
          @stock_items = @command.result

          unless @command.success?
            flash[:error] = @command.errors.full_messages
          end
        end

        def batch_create_stock_item_units
          @product = current_store.products.friendly.find(params[:product_id])
          @command = ::Admin::BatchCreateStockItemUnitCommand.call(
            stock_item_unit_params: params[:stock_item_units],
            current_user: spree_current_user,
            action_place: admin_products_path,
          )
          @stock_item_units = @command.result

          if @command.success?
            flash[:success] = ::Spree.t("admin.products.stock_success_message")
          else
            flash[:error] = ::Spree.t("admin.products.stock_fail_message", full_message: @command.errors.full_messages)
          end
        end

        def compare_price
          @scanned_item = ::Spree::ScannedItem.find_by(upc: params["id"])
          @upc_items = @scanned_item.upc_data
          @upc_items = @upc_items.map(&:with_indifferent_access)
        end

        def product_action_log
          product = product_scope.friendly.find(params[:id])
          actions = product.action_logs
          @search = actions&.ransack(params[:q])
          @actions = @search&.result&.order(created_at: :desc)
          @actions = @actions&.order(params[:order]) if params[:order].present?
          @actions = @actions&.page(params[:page])&.per(15)
        end

        def product_activity_log
          product = product_scope.friendly.find(params[:id])
          actions = product.associated_activity_logs
          @search = actions&.ransack(params[:q])
          @actions = @search&.result&.order(created_at: :desc)
          @actions = @actions&.order(params[:order]) if params[:order].present?
          @actions = @actions&.page(params[:page])&.per(15)
        end

        private

        def record_activity_log
          return unless @object
          ::Spree::ActivityLog.create!(
            loggable: @object,
            user_id: spree_current_user.id,
            action: ::Spree::ActivityLogActions.get(:product, resolve_product_action),
            role: spree_current_user.spree_roles.first&.name,
            date: Time.current,
            email: spree_current_user.email,
            action_place: resolve_product_action_place,
            action_name: @object.name,
            product_id: @object.id
          )
        end

        def resolve_product_action
          case action_name
          when "update" then :edit
          when "destroy" then :remove
          when "create" then :add
          else :default
          end
        end

        def resolve_product_action_place
          case action_name
          when "create", "update"
            edit_admin_product_path(@object)
          when "destroy"
            admin_products_path
          else
            "N/A"
          end
        end

        def set_permissions
          @permissions = current_user_permissions.find_permission
        end

        def current_user_permissions
          @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
        end

        def check_permission
          @permission = spree_current_user&.spree_roles&.first&.permission&.view_product_cost_price
        end

        def product_params
          params.require(:product).permit(
            :price,
            :sku,
            :available_on,
            :shipping_category_id,
            :upc_item_db_data,
            :scanned_item_id,
            :abbreviation,
          )
        end

        def validate_unique_property_names
          property_names = params[:product][:product_properties_attributes].values&.pluck(:property_name)&.map{|name| name.to_s.parameterize(separator: '_') }
          duplicate_names = property_names&.select { |name| property_names.count(name) > 1 }&.uniq

          if duplicate_names.present?
            return {
              warning: I18n.t("spree.admin.update.duplicate_property_names", duplicate_names: duplicate_names.join(", ")),
            }
          end
          {}
        end
      end
    end
  end
end

Spree::Admin::ProductsController.prepend(AxelSpree::Spree::Admin::ProductsControllerDecorator)
