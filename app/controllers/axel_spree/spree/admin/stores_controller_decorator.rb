# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module StoresControllerDecorator
        def self.prepended(base)
          base.before_action(:push_subdomain_to_redis, only: :create)
          base.before_action(:check_permission_for_store, only: [:new, :edit])
          base.before_action(:check_delete_permission, only: :destroy)
        end

        def switch_publish
          if spree_current_user&.spree_roles&.first&.permission&.publish_unpublish_store
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_stores_path)
            return
          end
          store = stores_scope.find(params[:id])
          is_prev_published = store.published
          store.update(published: !store.published)
          if store.errors.empty?
            flash[:success] = "Store #{store.name} is switched to #{is_prev_published ? "unpublished" : "published"} now"
          else
            flash[:error] = "Store #{store.name} cannot be switched to #{is_prev_published ? "unpublished" : "published"}. \
              #{store.errors.full_messages.join(", ")}"
          end

          redirect_to(spree.admin_stores_path)
        end

        def set_default
          store = stores_scope.find(params[:id])
          stores = stores_scope.where.not(id: params[:id])

          ApplicationRecord.transaction do
            store.update(default: true)
            stores.update_all(default: false) # rubocop:disable Rails/SkipsModelValidations
          end

          if store.errors.empty?
            flash[:success] = ::Spree.t(:store_set_as_default, store: store.name)
          else
            flash[:error] = "#{::Spree.t(:store_not_set_as_default, store: store.name)} #{store.errors.full_messages.join(", ")}"
          end

          redirect_to(spree.admin_stores_path)
        end

        protected

        def permitted_store_params
          permitted = params.require(:store).permit([
            :name,
            :url,
            :seo_title,
            :code,
            :meta_keywords,
            :meta_description,
            :default_currency,
            :mail_from_address,
            :customer_support_email,
            :facebook,
            :twitter,
            :instagram,
            :description,
            :address,
            :contact_phone,
            :supported_locales,
            :default_locale,
            :default_country_id,
            :supported_currencies,
            :new_order_notifications_email,
            :checkout_zone_id,
            :seo_robots,
            :digital_asset_authorized_clicks,
            :digital_asset_authorized_days,
            :limit_digital_download_count,
            :limit_digital_download_days,
            :digital_asset_link_expire_time,
            :about_us,
            :term_policies,
            :contact_us,
            :guarantee,
            :return_refund_policy,
            :shipping_delivery,
            :price_analytics_url,
            :price_analytics_client_id,
            :price_analytics_client_secret,
            meta_tags: [:name, :content],
            mailer_logo_attributes: {}, favicon_image_attributes: {}, logo_attributes: {},
          ])
          permitted[:meta_tags]&.reject! { |it| it[:name].blank? && it[:content].blank? }
          permitted
        end

        private

        def push_subdomain_to_redis
          if ::Spree::Store.any? # must be from current tenant
            subdomain = params[:store][:url].sub(%r{^https?\://(www.)?}, "").split(".").first
            RedisStore.set(subdomain, Apartment::Tenant.current)
          end
        end

        def check_permission_for_store
          if spree_current_user&.spree_roles&.first&.permission&.create_and_edit_store
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_stores_path)
            return # rubocop:disable Style/RedundantReturn
          end
        end

        def check_delete_permission
          if spree_current_user&.spree_roles&.first&.permission&.delete_store
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            respond_with(@object) do |format|
              format.html { redirect_to(collection_url) }
              format.js { render_js_for_destroy }
            end
            return # rubocop:disable Style/RedundantReturn
          end
        end
      end
    end
  end
end

Spree::Admin::StoresController.prepend(AxelSpree::Spree::Admin::StoresControllerDecorator)
