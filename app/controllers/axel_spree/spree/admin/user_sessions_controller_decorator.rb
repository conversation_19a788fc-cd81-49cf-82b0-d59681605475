# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module UserSessionsControllerDecorator
        # def authorization_failure
        #  store_location
        #  return redirect_to spree.admin_login_path
        # end
        def after_sign_in_path_for(resource_or_scope)
          resource_or_scope.update(user_token: SecureRandom.hex(12)) if resource_or_scope.user_token.blank?
          stored_location_for(resource_or_scope) || spree.admin_path
        end

        def redirect_back_or_default(default)
          redirect_to(session["spree_user_return_to"] || spree.admin_path)
          session["spree_user_return_to"] = nil
        end

        def after_sign_out_redirect(resource_or_scope)
          spree.admin_login_path("_" => Time.now.to_i)
        end
      end
    end
  end
end

Spree::Admin::UserSessionsController.prepend(AxelSpree::Spree::Admin::UserSessionsControllerDecorator)
