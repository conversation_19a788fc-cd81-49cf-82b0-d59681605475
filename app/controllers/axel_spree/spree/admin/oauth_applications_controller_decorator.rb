# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module OauthApplicationsControllerDecorator
        def self.prepended(base)
          base.before_action(:display_oauth_response_message, only: :index)
        end

        def index
          @oauth_applications = ::Spree::OauthApplication.where(store_id: current_store)
        end

        private

        def display_oauth_response_message
          if params[:response_message].present?
            flash.now[:notice] = params[:response_message]
          end
        end
      end
    end
  end
end
Spree::Admin::OauthApplicationsController.prepend(AxelSpree::Spree::Admin::OauthApplicationsControllerDecorator)
