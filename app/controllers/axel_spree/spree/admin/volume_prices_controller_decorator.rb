# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Admin
      module VolumePricesControllerDecorator
        def destroy
          @volume_price = ::Spree::VolumePrice.find(params[:id])
          @volume_price.destroy
          # render nothing: true
        end
      end
    end
  end
end

Spree::Admin::VolumePricesController.prepend(AxelSpree::Spree::Admin::VolumePricesControllerDecorator)
