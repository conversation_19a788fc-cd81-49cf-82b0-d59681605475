# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module VariantsControllerDecorator
        include ::Spree::BaseHelper
        include ::Spree::Admin::ProductsHelper
        include ::Spree::Admin::ElasticsearchHelper

        def self.prepended(base)
          base.after_action(:helper_sync_product_to_elasticsearch, only: [:create, :update, :destroy])
          base.before_action(:check_permission, only: [:index, :new, :edit])
          base.before_action(:find_variant, only: :edit)
        end

        def create
          if @product.option_types.empty?
            option_type = ::Spree::OptionType.unscoped.find_by(name: "~")
            if option_type
              @product.option_types << option_type unless @product.variant_ids.any? && @product.option_types.empty?
              option_value = option_type.option_values.find_or_create_by(
                name: params[:variant][:expiry_date],
                presentation: params[:variant][:expiry_date],
              )
              params[:variant][:option_value_ids] = [option_value&.id]
            end
          end
          super
          if @object.persisted?
            record_activity_log(@object)
          end
        end

        def update
          invoke_callbacks(:update, :before)
          if @object.update(permitted_resource_params)
            set_current_store
            # convert weight into two fields as well as update the data accordingly
            @object = ::Spree::Variant.find(@object.id)
            convert_oz_to_lbs(@object)
            invoke_callbacks(:update, :after)
            record_activity_log(@object)
            respond_with(@object) do |format|
              format.html do
                flash[:success] = success_message_for_update
                redirect_to(location_after_save)
              end
              format.js { render(layout: false) }
            end
          else
            invoke_callbacks(:update, :fails)
            action = request.referer.split("/").include?("volume_prices") ? :volume_prices : :edit
            respond_with(@object) do |format|
              format.html { render(action: action, status: :unprocessable_entity) }
              format.js { render(layout: false, status: :unprocessable_entity) }
            end
          end
        end

        def destroy
          super
          if @product.variants.exclude?(@variant)
            record_activity_log(@variant)
          end
        end

        def location_after_save
          if @product.master.id == @variant.id &&
              (params[:variant].key?(:volume_prices_attributes) ||
               request.referer.split("/").include?("volume_prices"))
            return volume_prices_admin_product_variant_url(@product, @variant)
          end

          super
        end

        def collection_url
          return request.url if request.url.include?("volume_prices")

          super
        end

        def load_data
          @tax_categories = ::Spree::TaxCategory.for_store(current_store).order(:name)
        end

        def collection
          return @collection if @collection.present?

          params[:q] ||= {}
          @deleted = params[:q].delete(:deleted_at_null) || "0"
          @collection = parent.variants
          @collection = if @deleted == "1"
            @collection.deleted.or(parent.variants.where(
              ::Spree::Variant.arel_table[:discontinue_on].not_eq(nil).and(
                ::Spree::Variant.arel_table[:discontinue_on].lt(Time.current),
              ),
            ))
          else
            @collection.not_deleted.not_discontinued
          end
          # @search needs to be defined as this is passed to search_link
          @search = @collection.ransack(params[:q])
          @collection = @search.result
            .page(params[:page])
            .per(params[:per_page] || ::Spree::Backend::Config[:variants_per_page])
        end

        private

        def find_variant
          variant = parent.variants.find(params[:id])
          unless variant
            redirect_to(spree.admin_product_variants_url(params[:product_id]))
          end
        end

        def record_activity_log(variant)
          ::Spree::ActivityLog.create!(
            loggable: variant,
            user_id: spree_current_user.id,
            action: ::Spree::ActivityLogActions.get(:variant, resolve_variant_action),
            role: spree_current_user.spree_roles.first&.name,
            date: Time.current,
            email: spree_current_user.email,
            action_place: resolve_variant_action_place(variant),
            action_name: variant.descriptive_name,
            product_id: @product.id
          )
        end

        def resolve_variant_action
          case action_name
          when "create"  then :add
          when "update"  then :edit
          when "destroy" then :remove
          else :default
          end
        end

        def resolve_variant_action_place(variant)
          case action_name
          when "create", "update"
            edit_admin_product_variant_path(@product.slug, variant.id)
          when "destroy"
            admin_product_variants_path(@product.slug)
          else
            "N/A"
          end
        end

        def check_permission
          @permission = spree_current_user&.spree_roles&.first&.permission&.view_product_cost_price
        end

        def success_message_for_update
          if request.referer.split("/").include?("volume_prices")
            "Volume Price for #{flash_message_for(@object, :successfully_updated)}"
          else
            flash_message_for(@object, :successfully_updated)
          end
        end

        def redirect_on_empty_option_values
          if @product.option_types.present?
            redirect_to(spree.admin_product_variants_url(params[:product_id])) if @product.empty_option_values?
          end
        end
      end
    end
  end
end

Spree::Admin::VariantsController.prepend(AxelSpree::Spree::Admin::VariantsControllerDecorator)
