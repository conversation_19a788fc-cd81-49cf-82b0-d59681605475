# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Admin
      module TaxRatesControllerDecorator
        def load_data
          @available_zones = ::Spree::Zone.order(:name)
          @available_categories = ::Spree::TaxCategory.for_store(current_store).order(:name)
          @calculators = ::Spree::TaxRate.calculators.sort_by(&:name)
        end
      end
    end
  end
end

Spree::Admin::TaxRatesController.prepend(AxelSpree::Spree::Admin::TaxRatesControllerDecorator)
