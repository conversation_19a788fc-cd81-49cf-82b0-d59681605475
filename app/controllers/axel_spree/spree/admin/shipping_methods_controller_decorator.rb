# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Admin
      module ShippingMethodsControllerDecorator
        def load_data
          @available_zones = ::Spree::Zone.order(:name)
          @tax_categories = ::Spree::TaxCategory.for_store(current_store).order(:name)
          @calculators = ::Spree::ShippingMethod.calculators.sort_by(&:name)
          @countries = ::Spree::Country.order(:name)
        end

        def load_states
          country_id = params[:country_id]
          @states = ::Spree::Country.find(country_id).states.order(:name)

          render(partial: "states_options", locals: { states: @states })
        end
      end
    end
  end
end

Spree::Admin::ShippingMethodsController.prepend(AxelSpree::Spree::Admin::ShippingMethodsControllerDecorator)
