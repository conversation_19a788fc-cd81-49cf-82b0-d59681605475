# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module OrdersControllerDecorator
        def self.prepended(base)
          base.before_action(:set_permissions, only: [:index, :edit, :new])
        end

        def index
          super

          @orders = @orders.joins(:ship_address).select("spree_orders.*")
          if params[:q]
            @orders = @orders.where(shipment_state: "ready") if params[:q][:status] == "fulfilling"
            @orders = @orders.with_subscriptions if params[:q][:subscription_order_eq] == '1'
          end
        end

        def show
          @order = scope.includes(:adjustments).find_by!(number: params[:id])
          authorize!(action, @order)
        end

        def cancel
          super
        rescue StandardError => e
          redirect_back(fallback_location: spree.edit_admin_order_url(@order), flash: { error: e&.message })
        end

        def edit
          if @permissions&.create_and_edit_order
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_orders_path)
            return
          end
          super
          @sale_channel = ::Spree::SaleChannel.find_by(id: @order.sale_channel_id) if @order.sale_channel_id
          @shipping_tag_emails = shipping_emails_list.uniq
          if @shipping_tag_emails.present?
            @templates_custom_message = @shipping_tag_emails.pluck(:id, :subject, :body).index_by(&:shift).transform_values do |v|
              { subject: v[0], custom_message: v[1].include?(::Spree.t(:custom_message_variable)) }
            end
          end
        end

        def new
          if @permissions&.create_and_edit_order
          else
            message = "You are not authorized to do this action"
            flash[:error] = message
            redirect_to(admin_orders_path)
            return
          end
          @sale_channel = ::Spree::SaleChannel.joins(:oauth_application).where('LOWER(brand) = ?', 'storefront').last
          if @sale_channel.nil?
            flash[:error] = "Storefront sale channel not exist."
            redirect_to(admin_orders_path)
            return
          end
          @order = scope.create(order_params.merge(sale_channel_id: @sale_channel.id))
          redirect_to(cart_admin_order_url(@order))
        end

        def calculate_weight
          line_items = ::Spree::LineItem.where(id: params[:lineItemIdArray])

          # Calculate the total weight of line items
          sum_weight = line_items.to_a.sum(0) do |item|
            # Determine weight in pounds and ounces based on whether the variant is a master variant or not
            lbs = item.variant.is_master ? item.product.lbs : item.variant.lbs
            oz = item.variant.is_master ? item.product.oz : item.variant.oz

            # Ensure weight values are valid and compute the total weight for the line item
            lbs && oz && (lbs > 0 || oz > 0) ? (lbs * 16 + oz) * item.quantity : 0.1
          end

          ordered_listings_data = ordered_listings_data(line_items)

          if ordered_listings_data
            sum_weight = ordered_listings_data.weight || 0.1
            filtered_ordered_listings_data = [{
              weight: ordered_listings_data.weight,
              length: ordered_listings_data.length,
              width: ordered_listings_data.width,
              height: ordered_listings_data.height
            }]
          end
          # Render a JSON response with the calculated weight
          render(json: {
            message: "success",
            data: {
              sum_weight_lbs: (sum_weight / 16).floor,
              sum_weight_oz: (sum_weight % 16).floor(1),
              ordered_listings_data: filtered_ordered_listings_data,
            },
          })
        end

        def ordered_listings_data(line_items)
          ordered_listings_data = line_items.each_with_object({}) do |line_item, result|
            result[line_item.listing_id] ||= 0
            result[line_item.listing_id] += line_item.quantity
          end

          return nil if ordered_listings_data.empty? || ordered_listings_data[nil].present?

          ordered_listings_data = ordered_listings_data.map do |listing_id, total_quantity|
            { listing_id: listing_id, quantity: total_quantity }
          end

          ordered_listings_data.sort_by! { |item| item[:listing_id] }
          ordered_listings_json = ordered_listings_data.to_json
          ordered_listing_info = ::Spree::OrderedListingsInfo.find_by(listing_quantities: ordered_listings_json)
        end

        def validate_stock_location
          shipments = ::Spree::Shipment.where(id: params[:shipment_ids])
          distinct_addresses = shipments.map(&:stock_location_id).uniq
          if distinct_addresses.length == 1
            render(json: { message: "success", data: { error: false } })
          else
            render(json: {
              message: "success",
              data: { error: true },
            })
          end
        end

        def destroy_order_package
          order_package = ::Spree::OrderPackage.find_by(id: params[:id])
          if order_package.present?
            order_package.shipments.update_all(order_package_id: nil)
            order_package.destroy
          end
        end

        def shipping_emails_list
          Array(sale_channel_default_email) + Array(fetch_shipping_tag_emails)
        end

        def sale_channel_default_email
          (Array(@sale_channel&.email_template) + app_default_shipping_email) || app_default_shipping_email
        end

        def fetch_shipping_tag_emails
          ::Spree::Tag.shipping_emails_without_default || ::Spree::Tag.none
        end

        def app_default_shipping_email
          ::Spree::EmailTemplate.current_store(current_store.id).default_shipping_email
        end

        def set_permissions
          @permissions = current_user_permissions.find_permission
        end

        def current_user_permissions
          @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
        end
      end
    end
  end
end

Spree::Admin::OrdersController.prepend(AxelSpree::Spree::Admin::OrdersControllerDecorator)
