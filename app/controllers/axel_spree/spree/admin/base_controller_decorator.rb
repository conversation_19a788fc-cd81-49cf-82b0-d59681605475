# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Admin
      module BaseControllerDecorator
        # def stores_scope
        #   user = try_spree_current_user
        #   return unless user

        #   raise ::CanCan::AccessDenied.new(
        #     "Not authorized!",
        #     :read,
        #     current_store,
        #   ) unless user.spree_admin? || ::Spree::RoleUser.has_store_role(
        #     user, current_store, :store_owner
        #   ) || ::Spree::RoleUser.has_store_role(user, current_store, :store_employee)

        #   ::Spree::Store.accessible_by(current_ability, :show)
        # end

        def stores_scope
          user = try_spree_current_user
          return unless user

          raise ::CanCan::AccessDenied.new(
            "Not authorized!",
            :read,
            current_store,
          ) unless user.spree_admin? || ::Spree::RoleUser.has_store_role(
            user, current_store, :store_owner
          ) || ::Spree::RoleUser.has_store_role(user, current_store, :store_employee) || (::Spree::RoleUser.has_store_role(user, current_store, user&.spree_roles&.first&.name) && user&.spree_roles&.any? { |role| role.name != "store_customer" }) # rubocop:disable Layout/LineLength

          ::Spree::Store.accessible_by(current_ability, :show)
        end

        def load_stores
          scope = stores_scope
          if scope.nil?
            @stores = []
            return
          end

          if !try_spree_current_user.spree_admin? && try_spree_current_user.spree_store_ids.count > 0
            scope = scope.where(id: try_spree_current_user.spree_store_ids)
          end

          unless try_spree_current_user.spree_admin?
            scope = scope.where(default: false)
          end

          @stores = scope.order(default: :desc).i18n.includes(:translations)
        end

        # def unauthorized
        #  #sign_out try_spree_current_user if spree_user_signed_in?
        #  store_location
        #  redirect_to spree.admin_login_path
        # end

        def redirect_unauthorized_access
          if try_spree_current_user
            flash[:error] = ::Spree.t(:authorization_failure)
            if request.fullpath != spree.admin_forbidden_path
              redirect_to(spree.admin_forbidden_path)
            elsif @store.nil? || @store.count == 0
              sign_out(try_spree_current_user)
              respond_to do |format|
                format.html do
                  redirect_to(spree.admin_login_path)
                end
              end
            end
          else
            store_location
            if defined?(spree.admin_login_path)
              redirect_to(spree.admin_login_path)
            elsif respond_to?(:spree_login_path)
              redirect_to(spree_login_path)
            elsif spree.respond_to?(:root_path)
              redirect_to(spree.root_path)
            else
              redirect_to(main_app.respond_to?(:root_path) ? main_app.root_path : "/")
            end
          end
        end
      end
    end
  end
end

Spree::Admin::BaseController.prepend(AxelSpree::Spree::Admin::BaseControllerDecorator)
