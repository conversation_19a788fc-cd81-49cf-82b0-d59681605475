# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Admin
      module ActiveShippingSettingsControllerDecorator
        def self.prepended(base)
          base.before_action(:set_json_values, only: [:edit, :update])
        end

        def edit
          @config = ::Spree::ActiveShippingSetting.where(store: current_store).first_or_initialize
        end

        def update
          active_shipping_setting = ::Spree::ActiveShippingSetting.where(store: current_store).first_or_initialize

          @preferences_ups.each do |key|
            active_shipping_setting.ups[key] = params[key]
          end

          @preferences_fedex.each do |key|
            active_shipping_setting.fedex[key] = params[key]
          end

          @preferences_usps.each do |key|
            active_shipping_setting.usps[key] = if key.to_s == "usps_commercial_base" || key.to_s == "usps_commercial_plus"
              (params[key] == "1")
            else
              params[key]
            end
          end

          @preferences_canadapost.each do |key|
            active_shipping_setting.canada_post[key] = params[key]
          end

          @preferences_australiapost.each do |key|
            active_shipping_setting.australia_post[key] = params[key]
          end

          @preferences_general_settings.each do |key|
            active_shipping_setting.general_settings[key] = if key.to_s == "test_mode"
              (params[key] == "1")
            else
              params[key]
            end
          end
          active_shipping_setting.save!
          redirect_to(edit_admin_active_shipping_settings_path)
        end

        private

        def set_json_values
          @preferences_ups = [:ups_login, :ups_password, :ups_key, :shipper_number]
          @preferences_fedex = [:fedex_login, :fedex_password, :fedex_account, :fedex_key]
          @preferences_usps = [:usps_login, :usps_commercial_base, :usps_commercial_plus]
          @preferences_canadapost = [:canada_post_login]
          @preferences_australiapost = [:australia_post_login]
          @preferences_general_settings = [
            :units,
            :unit_multiplier,
            :default_weight,
            :handling_fee,
            :max_weight_per_package,
            :test_mode,
          ]
        end
      end
    end
  end
end

Spree::Admin::ActiveShippingSettingsController.prepend(AxelSpree::Spree::Admin::ActiveShippingSettingsControllerDecorator)
