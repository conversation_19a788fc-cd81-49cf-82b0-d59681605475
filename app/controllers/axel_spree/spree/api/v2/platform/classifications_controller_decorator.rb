# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module ClassificationsControllerDecorator
            def index
              render_serialized_payload do
                Rails.cache.fetch(collection_cache_key(paginated_collection), collection_cache_opts) do
                  serialize_collection(paginated_collection)
                end
              end
            end

            def fetch_listings_records
              sorted_collection.select("spree_products_taxons.*, spree_listings.id AS listing_id, spree_listings.title AS listing_title")
                              .joins(product: { listings: :sale_channel })
                              .where(spree_listings: { status: "Active" })
                              .where(spree_sale_channels: { brand: "storefront" })
            end

            def paginated_collection
              @paginated_collection ||= collection_paginator.new(fetch_listings_records, params).call
            end

            ::Spree::Api::V2::Platform::ClassificationsController.prepend(self)
          end
        end
      end
    end
  end
end
