# frozen_string_literal: true

module Axel<PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Platform
          module LineItemsControllerDecorator
            def update
              quantity = permitted_resource_params[:quantity].to_i * resource.pack_size
              permitted_params = permitted_resource_params.merge!(quantity: quantity)

              result = update_service.call(line_item: resource, line_item_attributes: permitted_params)

              if result.success?
                render_serialized_payload { serialize_resource(result.value) }
              else
                render_error_payload(resource.errors)
              end
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Platform::LineItemsController.prepend(AxelSpree::Spree::Api::V2::Platform::LineItemsControllerDecorator)
