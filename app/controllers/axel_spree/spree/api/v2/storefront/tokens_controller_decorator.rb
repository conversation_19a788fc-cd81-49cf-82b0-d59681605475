# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Storefront
          module TokensControllerDecorator
            include ::Spree::Admin::Subscribers<PERSON><PERSON><PERSON>
            def self.prepended(base)
              base.before_action(:check_if_account_is_pending, only: :create)
            end

            def after_successful_authorization(context)
              check_subscriber
              super
            end

            private

            def check_if_account_is_pending
              user = ::Spree::User.find_by(email: params["username"]&.downcase)
              render(json: unconfirmed_account_error, status: :unprocessable_entity) if user&.confirmation_sent_at && !user&.confirmed?
            end

            def unconfirmed_account_error
              { error: "unverified_user", error_description: "You must validate your email address before login" }
            end

            def check_subscriber
              if params[:username]
                subscriber = ::Spree::Subscriber.find_or_create_by(email: params[:username])
                process_subscriber_action(subscriber, 'logged in') if subscriber
              end
            end
          end
        end
      end
    end
  end
end

Doorkeeper::TokensController.prepend(AxelSpree::Spree::Api::V2::Storefront::TokensControllerDecorator)
