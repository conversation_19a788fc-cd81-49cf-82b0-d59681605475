# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Api
      module V2
        module Storefront
          module CartControllerDecorator
            include ::Spree::Admin::Subscribers<PERSON>elper
            def self.prepended(base)
              base.before_action(:ensure_valid_product, only: [:create, :add_item, :show])
              base.before_action(:load_listing, only: :add_item)
              base.before_action(:update_quantity_as_per_pack_size, only: [:add_item, :set_quantity])
            end

            def add_item
              spree_authorize!(:update, spree_current_order, order_token)
              spree_authorize!(:show, @variant)

              params_with_options = add_item_params.to_h
              params_with_options[:options] ||= {}
              params_with_options[:options][:cart] = params[:cart]
              params_with_options[:options][:listing_id] = @listing.id
              result = add_item_service.call(
                order: spree_current_order,
                variant: @variant,
                quantity: add_item_params[:quantity],
                public_metadata: add_item_params[:public_metadata],
                private_metadata: add_item_params[:private_metadata],
                options: params_with_options[:options],
              )
              check_local_pickup_eligibility
              if result.success? && spree_current_order&.email.present?
                subscriber = ::Spree::Subscriber.find_or_create_by(email: spree_current_order&.email)
                process_subscriber_action(subscriber, 'added item to the cart', spree_current_order) if subscriber
              end
              render_order(result)
            end

            def remove_line_item
              spree_authorize!(:update, spree_current_order, order_token)

              remove_line_item_service.call(
                order: spree_current_order,
                line_item: line_item,
              )
              check_local_pickup_eligibility
              render_serialized_payload { serialized_current_order }
            end

            private

            def load_listing
              @listing = @variant.product.listings.find_by(id: params[:listing_id])
              return render json: {error: "listing id must exists"}, status: :unprocessable_entity if @listing.nil?

              return render json: {error: "Unpublished Listing"}, status: :unprocessable_entity unless @listing.Active?
            end

            def update_quantity_as_per_pack_size
              # listing = params[:action] == "add_item" ? @listing : line_item.listing
              # params[:quantity] *= listing.pack_size_value

              pack_size = params[:action] == "add_item" ? @listing.pack_size_value : line_item.pack_size
              params[:quantity] *= pack_size
            end

            def check_local_pickup_eligibility
              local_pickup = spree_current_order.line_items.reload.map do |line_item|
                line_item.listing&.shipping_category&.shipping_methods&.any?(&:local_pickup)
              end
              # rubocop:disable Rails/SkipsModelValidations
              spree_current_order.update_columns(local_pickup: local_pickup.exclude?(false))
              # rubocop:enable Rails/SkipsModelValidations
            end

            def ensure_valid_product
              if spree_current_user.present? || @variant || spree_current_order.present?
                if @variant
                  active_listings = @variant.product.listings.Active
                  has_active_storefront_listing = active_listings.joins(:sale_channel).exists?(sale_channel: { brand: "storefront" })

                  unless has_active_storefront_listing && @variant.product.active?
                    render(json: { error: "Unpublished product" }, status: :unprocessable_entity)
                  end
                elsif spree_current_order.present?
                  spree_current_order.line_items.each do |li|
                    added_product = li.product
                    active_listings = added_product.listings.Active
                    has_active_storefront_listing = active_listings.joins(:sale_channel).exists?(sale_channel: { brand: "storefront" })

                    next if has_active_storefront_listing && added_product.active?

                    params[:line_item_id] = li.id

                    spree_authorize!(:update, spree_current_order, order_token)
                    remove_line_item_service.call(
                      order: spree_current_order,
                      line_item: line_item,
                    )
                    render(json: { error: "Unpublished product." }, status: :unprocessable_entity)
                  end
                end
              end
            end

            def add_item_params
              params.permit(:quantity, :variant_id, public_metadata: {}, private_metadata: {}, options: {})
            end

            def serializer_params
              {
                currency: current_currency,
                locale: current_locale,
                price_options: current_price_options,
                store: current_store,
                user: spree_current_user,
                image_transformation: params[:image_transformation],
                taxon_image_transformation: params[:taxon_image_transformation],
                listing_id: params[:listing_id]
              }
            end
          end
        end
      end
    end
  end
end

Spree::Api::V2::Storefront::CartController.prepend(AxelSpree::Spree::Api::V2::Storefront::CartControllerDecorator)
