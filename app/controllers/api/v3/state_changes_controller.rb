# frozen_string_literal: true

module Api
  module V3
    class StateChangesController < ResourceController
      def index
        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        collection = Spree::StateChange.where(stateful_id: params[:order_id]).order(created_at: :desc)
        render_serialized_payload { serialize_collection(collection) }
      end

      protected

      def model_class
        Spree::StateChange
      end
    end
  end
end
