# frozen_string_literal: true

module Api
  module V3
    class UsersController < ResourceController
      rescue_from Spree::Core::DestroyWithOrdersError, with: :render_withorder_error

      before_action -> { params[:page] ||= 1 }, only: [:index, :items]

      def current
        render_serialized_payload { serialize_resource(spree_current_user) }
      end

      def addresses
        user = Spree::User.find(params[:id])
        addresses = Spree::Address.where(user_id: params[:id])
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?
        addresses = addresses.page(params[:page]).per(per_page)

        render(json: {
          use_billing: user.ship_address_id.present? && user.ship_address_id == user.bill_address_id,
          bill_address_id: user.bill_address_id,
          ship_address_id: user.ship_address_id,
          addresses: AddressSerializer.render(addresses)
        })
      end

      def orders
        params[:q] ||= {}
        @search = current_store.orders.reverse_chronological.ransack(params[:q].merge(user_id_eq: params[:id]))
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?
        orders = @search.result.page(params[:page]).per(per_page)
        render(json: OrderSerializer.render(orders))
      end

      def items
        current_store.line_items
          .joins(:order)
          .includes(variant: [:product, {option_values: :option_type}])
          .where(order: {user_id: params[:id]})
          .ransack(params[:q] || {})
          .tap { |s| s.sorts = 'created_at desc' if s.sorts.empty? }
          .result
          .page(params[:page])
          .per(params[:page])
          .then { |items|
            render_serialized_payload { serialize_collection(items, LineItemSerializer, view: :user) }
          }
      end

      protected

      def authorize_spree_user
        case action_name
        when 'current' then true
        else
          super
        end
      end

      def model_class
        Spree::User
      end

      def serializer_params
        super
          .tap { |params| params[:view] = :full unless action_name == 'index' }
          .tap { |params| params[:view] = :current if action_name == 'current' }
      end

      def permitted_resource_params
        params.require(:user).permit(
          :email, :password, :password_confirmation,
          :first_name, :last_name, :ship_address_id, :bill_address_id,
          role_ids: [], store_ids: []
        )
          .tap { |params|
            params[:spree_role_ids] = params.delete(:role_ids) if params.include?(:role_ids)
            params[:spree_store_ids] = params.delete(:store_ids) if params.include?(:store_ids)
          }
          .tap { |params|
            params.delete(:password) if action_name == 'update' && params[:password_confirmation].blank?
          }
      end
    end
  end
end
