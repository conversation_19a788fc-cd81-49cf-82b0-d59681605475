# frozen_string_literal: true

module Api
  module V3
    class CustomerShipmentsTrackingController < ResourceController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        collection = if params[:tracking]
          Spree::CustomerShipment.where(tracking: params[:tracking]).page(params[:page]).per(per_page)
        else
          Spree::CustomerShipment.all.page(params[:page]).per(per_page)
        end

        scope = collection
        return render_error_payload('no pagenation info') unless scope.respond_to?(:total_pages)

        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count

        render_serialized_payload { serialize_collection(collection) }
      end

      protected

      def model_class
        Spree::CustomerShipment
      end
    end
  end
end
