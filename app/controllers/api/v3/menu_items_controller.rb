# frozen_string_literal: true

module Api
  module V3
    class MenuItemsController < ResourceController
      include ::Spree::Api::V2::Platform::NestedSetRepositionConcern

      def reposition
        # This method source from spree_api-4.6.6 [Date: 2025-06-10]
        spree_authorize! :update, resource if spree_current_user.present?

        @new_parent = scope.find(permitted_resource_params[:new_parent_id])
        new_index = permitted_resource_params[:new_position_idx].to_i

        if resource.move_to_child_with_index(@new_parent, new_index)
          resource.touch
          # If successful reposition call the custom method for handling success.
          successful_reposition_actions
        elsif resource.errors.any?
          # If there are errors output them to the response
          render_error_payload(resource.errors.full_messages.to_sentence)
        else
          # If the user drops the re-positioned item in the same location it came from
          # we just render the serialized payload, nothing has changed, we don't need to
          # render any errors, or fire any custom success methods.
          render_serialized_payload { serialize_resource(resource) }
        end
      end

      protected

      def model_class
        Spree::MenuItem
      end

      def parent_scope
        Spree::Menu.for_store(current_store).find(params[:menu_id]).menu_items
      end

      def scope
        parent_scope
      end

      def permitted_resource_params
        params.require(:menu_item).permit(:parent_id, :name, :code,
          :subtitle, :destination, :new_window, :item_type,
          :linked_resource_type, :linked_resource_id,
          :icon, :alt, :new_parent_id, :new_position_idx)
          .tap { |p| p[:item_type] = p[:item_type]&.titleize if p[:item_type] }
          .tap { |p| p[:menu_id] = params[:menu_id] }
      end
    end
  end
end
