# frozen_string_literal: true

module Api
  module V3
    class MenusController < ResourceController
      protected

      def model_class
        Spree::Menu
      end

      def serializer_params
        super.tap { |params| params[:view] = :full unless action_name == 'index' }
      end

      def permitted_resource_params
        params.require(:menu).permit(:name, :location, :locale)
      end
    end
  end
end
