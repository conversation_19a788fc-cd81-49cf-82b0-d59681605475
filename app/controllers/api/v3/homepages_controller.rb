# frozen_string_literal: true

module Api
  module V3
    class HomepagesController < ResourceController
      private

      def model_class
        Spree::CmsPage
      end

      def resource
        @resource ||= (
          current_store.cms_pages.home.by_locale('en').first ||
            current_store.cms_pages.home.by_locale('en').create!(title: 'Homepage'))
                        .tap { |page|
          if action_name.to_s == 'show'
            unless page.cms_sections.find_by(type: 'Spree::Cms::Sections::Header')
              page.cms_sections.create(type: 'Spree::Cms::Sections::Header', name: 'Header')
              page.cms_sections.create(type: 'Spree::Cms::Sections::AnnouncementBar', name: 'AnnouncementBar')
              page.cms_sections.create(type: 'Spree::Cms::Sections::ImageBanner', name: 'ImageBanner')
              page.cms_sections.create(type: 'Spree::Cms::Sections::FeaturedProduct', name: 'FeaturedProducts')
              page.cms_sections.create(type: 'Spree::Cms::Sections::FeaturedArticle', name: 'FeaturedArticle')
              page.cms_sections.create(type: 'Spree::Cms::Sections::Collection', name: 'Collections')
              page.cms_sections.create(type: 'Spree::Cms::Sections::Column', name: 'Columns')
              page.cms_sections.create(type: 'Spree::Cms::Sections::SignupEmail', name: 'SignupEmail')
              page.cms_sections.create(type: 'Spree::Cms::Sections::Footer', name: 'Footer')
            end
          end
        }
      end

      def serializer_params
        super.tap { |params| params[:view] = :homepage }
      end

      def permitted_resource_params
        params.require(:page).permit(
          :type, :locale, :title, :slug, :visible,
          :content, :meta_title, :meta_description,
          sections: [:id, :_destroy, :position, :visible, :type, :name, properties: {}]
        )
          .tap { |p| p[:type] = convert_page_type(p[:type]) if p[:type] }
          .tap { |p|
            p[:sections_attributes] = (p.delete(:sections) || []).each do |item|
              item[:type] = convert_section_type(item[:type]) if item[:type]
            end
          }
      end

      def convert_page_type(type)
        (type == 'home' ? 'Homepage' : "#{type.classify}Page")
          .then { |t| "Spree::Cms::Pages::#{t}" }
      end

      def convert_section_type(type)
        "Spree::Cms::Sections::#{type.classify}"
      end
    end
  end
end
