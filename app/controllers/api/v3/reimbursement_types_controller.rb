# frozen_string_literal: true

module Api
  module V3
    class ReimbursementTypesController < ResourceController
      def create
        if params[:reimbursement_type]
          if Spree::ReimbursementType::KINDS.exclude?(params[:reimbursement_type][:type])
            return render_error_payload('need provide a vaild parameter type')
          end
        end
        super
      end

      protected

      def model_class
        Spree::ReimbursementType
      end

      def permitted_resource_params
        params.require(:reimbursement_type).permit(
          :name,
          :active,
          :mutable,
          :type
        )
      end
    end
  end
end
