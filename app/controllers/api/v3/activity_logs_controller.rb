# frozen_string_literal: true

module Api
  module V3
    class ActivityLogsController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index
      before_action -> { params[:sort] ||= '-created_at' }, only: :index

      protected

      def scope
        model_class
      end

      def allowed_sort_attributes
        [:created_at]
      end

      def model_class
        Spree::ActivityLog
      end
    end
  end
end
