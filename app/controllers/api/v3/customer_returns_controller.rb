# frozen_string_literal: true

module Api
  module V3
    class CustomerReturnsController < ResourceController
      def index
        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?

        collection = []
        rma_return_items = []
        if params[:order_id]
          order = ::Spree::Order.find(params[:order_id])
          unless order
            return render_error_payload('please provide a valid order id')
          end

          order_customer_return_ids = order.return_items.accessible_by(current_ability).pluck(:customer_return_id).uniq.compact
          collection = Spree::CustomerReturn.where(id: order_customer_return_ids).ransack(params[:filter]).result.page(params[:page]).per(per_page)
          # current_store.customer_returns.where(id: order_customer_return_ids)

          return_items = order&.inventory_units&.map(&:current_or_new_return_item)&.reject(&:customer_return_id)
          rma_return_items = return_items&.select(&:return_authorization_id)
        else
          collection = Spree::CustomerReturn.all.ransack(params[:filter]).result.page(params[:page]).per(per_page)
        end

        scope = collection
        return render_error_payload('no pagenation info') unless scope.respond_to?(:total_pages)

        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count

        render(json: {customer_returns: serialize_collection(collection), remain_return_items: format_result(rma_return_items)})
        # render_serialized_payload { serialize_collection(collection) }
      end

      def show
        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        collection = Spree::CustomerReturn.find(params[:id])
        return_items = order&.inventory_units&.map(&:current_or_new_return_item)&.reject(&:customer_return_id)
        rma_return_items = return_items&.select(&:return_authorization_id)

        render(json: {customer_returns: serialize_collection(collection), remain_return_items: format_result(rma_return_items)})
      end

      def create
        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        unless params[:customer_return]
          return render_error_payload('need provide parameter customer_return')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        if params[:customer_return]
          unless params[:customer_return][:stock_location_id]
            return render_error_payload('need provide parameter customer_return - stock_location_id')
          end

          unless params[:customer_return][:return_items_attributes]
            return render_error_payload('need provide parameter customer_return - return_items_attributes')
          end

          stock_location = ::Spree::StockLocation.find(params[:customer_return][:stock_location_id])
          unless stock_location
            return render_error_payload('please provide a valid stock location id')
          end
        end

        ActiveRecord::Base.transaction do
          @customer_return = Spree::CustomerReturn.new
          @customer_return.store = current_store
          @customer_return.stock_location = stock_location

          return_items_params = params[:customer_return][:return_items_attributes]
          @customer_return.return_items = return_items_params.filter_map do |item_params|
            return_item = Spree::ReturnItem.find(item_params[:id])
            return_item
          end

          if @customer_return.save
            render_serialized_payload { serialize_collection(@customer_return) }
          else
            render_error_payload(@customer_return.errors)
          end
        end
      rescue ActiveRecord::RecordInvalid => e
        render_error_payload(e.message)
      rescue ActiveModel::UnknownAttributeError => e
        render_error_payload(e.message)
      rescue ActiveModel::ForbiddenAttributesError => e
        render_error_payload(e.message)
      rescue StateMachines::InvalidTransition => e
        render_error_payload(e.message)
      end

      def update
        unless params[:order_id]
          return render_error_payload('need provide parameter order id')
        end

        unless params[:id]
          return render_error_payload('need provide parameter id')
        end

        unless params[:customer_return]
          return render_error_payload('need provide parameter customer_return')
        end

        order = ::Spree::Order.find(params[:order_id])
        unless order
          return render_error_payload('please provide a valid order id')
        end

        unless params[:customer_return][:stock_location_id]
          return render_error_payload('need provide parameter customer_return - stock_location_id')
        end

        stock_location = ::Spree::StockLocation.find(params[:customer_return][:stock_location_id])
        unless stock_location
          return render_error_payload('please provide a valid stock location id')
        end

        customer_return = ::Spree::CustomerReturn.find(params[:id])
        unless customer_return&.order&.id == order.id
          return render_error_payload('please provide a valid order id and customer return id')
        end

        customer_return.stock_location = stock_location

        if customer_return.save
          render_serialized_payload { serialize_collection(customer_return) }
        else
          render_error_payload(customer_return.errors)
        end
      end

      def refund
        render_serialized_payload { {status: :ok} }
      end

      protected

      def model_class
        Spree::CustomerReturn
      end

      def permitted_resource_params
        params.require(:customer_return).permit(
          :stock_location_id,
          return_items_attributes: [
            :id
          ]
        )
      end

      def format_result(ris)
        ris&.map do |r|
          {
            # Spree::ReturnItem column data
            id: r.id,
            return_authorization_id: r.return_authorization_id,
            inventory_unit_id: r.inventory_unit_id,
            exchange_variant_id: r.exchange_variant_id,
            created_at: r.created_at,
            updated_at: r.updated_at,
            pre_tax_amount: r.pre_tax_amount,
            included_tax_total: r.included_tax_total,
            additional_tax_total: r.additional_tax_total,
            reception_status: r.reception_status,
            acceptance_status: r.acceptance_status,
            customer_return_id: r.customer_return_id,
            reimbursement_id: r.reimbursement_id,
            acceptance_status_errors: r.acceptance_status_errors,
            preferred_reimbursement_type_id: r.preferred_reimbursement_type_id,
            override_reimbursement_type_id: r.override_reimbursement_type_id,
            resellable: r.resellable,
            reason: r.reason,
            return_authorization_reason_id: r.return_authorization_reason_id,
            # extral data
            editable: r&.inventory_unit&.shipped? && !r&.return_authorization&.customer_returned_items? && r&.reimbursement.nil?,
            return_quantity: r.return_quantity,
            quantity: r&.inventory_unit&.quantity,
            variant_name: r&.inventory_unit&.variant&.name,
            sku: r&.inventory_unit&.variant&.sku,
            inventory_unit_state: r&.inventory_unit&.state
          }
        end
      end
    end
  end
end
