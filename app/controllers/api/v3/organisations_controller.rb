# frozen_string_literal: true

module Api
  module V3
    class OrganisationsController < ResourceController

      def update_two_factor
        tenant_name = Apartment::Tenant.current
        organisation = Spree::Organisation.find_by(subdomain: tenant_name)

        return render_error_payload('Organisation not found', :not_found) if organisation.nil?

        if organisation.update(two_factor_enabled: params[:two_factor_enabled])
          render json: {
            message: "Two-factor authentication #{params[:two_factor_enabled] ? 'enabled' : 'disabled'} successfully",
            organisation: organisation
          }, status: :ok
        else
          render_error_payload('Failed to update two-factor authentication', :unprocessable_entity)
        end
      end

      protected

      def model_class
        Spree::Organisation
      end

      def scope
        Spree::Organisation
      end

      def allowed_sort_attributes
        []
      end

      def permitted_resource_params
        params.permit(
          :name,
          :subdomain,
          :admin_email,
          :custom_domain
        )
      end
    end
  end
end
