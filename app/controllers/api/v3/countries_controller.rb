# frozen_string_literal: true

module Api
  module V3
    class CountriesController < ResourceController
      protected

      def model_class
        Spree::Country
      end

      def permitted_resource_params
        params.require(:country).permit(
          :name,
          :iso_name,
          :iso,
          :iso3,
          :states_required,
          :zipcode_required
        )
      end
    end
  end
end
