# frozen_string_literal: true

module Api
  module V3
    class VariantsController < ResourceController
      before_action -> {
                      params[:filter] ||= {}
                      params[:filter][:product_id_eq] = params[:product_id]
                    }, except: [:find, :show]
      before_action -> { params[:page] ||= 1 }, only: :index

      def find
        render_serialized_payload { serialize_collection(paginated_collection) }
      end

      private

      def model_class
        Spree::Variant
      end

      def variant_includes
        [{option_values: :option_type}, :product, :default_price, :images, {stock_items: :stock_location}]
      end

      def serializer_params
        super.tap { |params| params[:view] = :full if action_name == 'show' }
      end

      def authorize_spree_user
        case action_name
        when 'find'
          true
        else
          super
        end
      end

      def permitted_resource_params
        params.require(:variant).permit(:sku, :upc, :barcode,
          :is_master, :position, :track_inventory,
          :lbs, :oz, :weight, :height, :width, :depth,
          :cost_currency, :cost_price, :currency, :price, :compare_at_price, :compare_to_price,
          :discontinue_on, :tax_category_id,
          :expiry_date, :receipt_number, :inventory_number, :vendor_receipt_date,
          option_value_ids: [])
          .tap { |p| p[:product_id] = params[:product_id] }
      end
    end
  end
end
