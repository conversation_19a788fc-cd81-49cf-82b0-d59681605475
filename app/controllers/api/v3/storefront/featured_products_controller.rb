# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class FeaturedProductsController < ::Api::V3::Storefront::ResourceController
        def model_class
          Spree::Listing
        end

        protected

        def serializer_params
          include_fields = params[:include]&.split(',') || []
          super.tap { |params|
            params[:include] = include_fields
            params[:view] = :listing_product if ['index'].include?(action_name)
          }
        end

        def parent_scope
          model_class.for_store(current_store)
        end

        def scope(skip_cancancan: false)
          base_scope = parent_scope
          base_scope.active.joins(:featured_products)
        end

        def resource_serializer
          ::Api::V3::Storefront::ListingSerializer
        end

        private

        def spree_permitted_attributes
          []
        end
      end
    end
  end
end
