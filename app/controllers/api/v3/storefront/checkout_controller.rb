# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class CheckoutController < ::Api::V3::Storefront::ResourceController
        include Spree::BaseHelper
        include Api::V3::Storefront::OrderConcern
        include ::Spree::Admin::SubscribersHelper

        before_action :require_spree_current_user
        before_action :ensure_order

        before_action(:destroy_previous_payments, only: :update)
        before_action(:check_subscriber, only: :update)

        def next
          spree_authorize! :update, spree_current_order, order_token

          result = next_service.call(order: spree_current_order)

          render_order(result)
        end

        def advance
          spree_authorize! :update, spree_current_order, order_token

          result = advance_service.call(order: spree_current_order)

          render_order(result)
        end

        def complete
          spree_authorize! :update, spree_current_order, order_token

          result = complete_service.call(order: spree_current_order)

          render_order(result)
        end

        def update
          spree_authorize! :update, spree_current_order, order_token

          result = update_service.call(
            order: spree_current_order,
            params: params,
            # defined in https://github.com/spree/spree/blob/master/core/lib/spree/core/controller_helpers/strong_parameters.rb#L19
            permitted_attributes: permitted_checkout_attributes,
            request_env: request.headers.env,
          )

          render_order(result)
        end

        def create_payment
          result = create_payment_service.call(order: spree_current_order, params: params)

          if result.success?
            render_serialized_payload(201) { serialize_resource(spree_current_order.reload) }
          else
            render_error_payload(result.error)
          end
        end

        def select_shipping_method
          result = select_shipping_method_service.call(order: spree_current_order, params: params)

          render_order(result)
        end

        def add_store_credit
          spree_authorize! :update, spree_current_order, order_token

          result = add_store_credit_service.call(
            order: spree_current_order,
            amount: params[:amount].try(:to_f),
          )

          render_order(result)
        end

        def remove_store_credit
          spree_authorize! :update, spree_current_order, order_token

          result = remove_store_credit_service.call(order: spree_current_order)
          render_order(result)
        end

        def shipping_rates
          result = shipping_rates_service.call(order: spree_current_order)

          if result.success?
            render_serialized_payload { serialize_shipping_rates(result.value) }
          else
            render_error_payload(result.error)
          end
        end

        def payment_methods
          render_serialized_payload { serialize_payment_methods(spree_current_order.available_payment_methods) }
        end

        private

        def spree_permitted_attributes
          []
        end

        def scope
          super.where(user: spree_current_user, country: available_countries).not_deleted
        end

        def model_class
          nil
        end

        def serializer_params
          super.tap do |params|
            params[:view] = ["payment_methods"].include?(action_name) ? nil : :all
          end
        end

        # def collection_finder
        #   Spree::Api::Dependencies.storefront_address_finder.constantize
        # end

        # def create_service
        #   Spree::Api::Dependencies.storefront_address_create_service.constantize
        # end

        # def update_service
        #   Spree::Api::Dependencies.storefront_address_update_service.constantize
        # end

        # def address_params
        #   params.require(:address).permit(permitted_address_attributes)
        # end

        def resource_serializer
          Api::V3::Storefront::OrderSerializer
        end

        def next_service
          Spree::Api::Dependencies.storefront_checkout_next_service.constantize
        end

        def advance_service
          Spree::Api::Dependencies.storefront_checkout_advance_service.constantize
        end

        def add_store_credit_service
          Spree::Api::Dependencies.storefront_checkout_add_store_credit_service.constantize
        end

        def remove_store_credit_service
          Spree::Api::Dependencies.storefront_checkout_remove_store_credit_service.constantize
        end

        def complete_service
          Spree::Api::Dependencies.storefront_checkout_complete_service.constantize
        end

        def update_service
          Spree::Api::Dependencies.storefront_checkout_update_service.constantize
        end

        def payment_methods_serializer
          # Spree::Api::Dependencies.storefront_payment_method_serializer.constantize
          Api::V3::Storefront::PaymentMethodSerializer
        end

        def shipping_rates_service
          Spree::Api::Dependencies.storefront_checkout_get_shipping_rates_service.constantize
        end

        def shipping_rates_serializer
          # Spree::Api::Dependencies.storefront_shipment_serializer.constantize
          Api::V3::Storefront::ShipmentSerializer
        end

        def create_payment_service
          Spree::Api::Dependencies.storefront_payment_create_service.constantize
        end

        def select_shipping_method_service
          Spree::Api::Dependencies.storefront_checkout_select_shipping_method_service.constantize
        end

        def serialize_payment_methods(payment_methods)
          # payment_methods_serializer.new(payment_methods, params: serializer_params).serializable_hash
          payment_methods_serializer.render(payment_methods, serializer_params)
        end

        # def serialize_shipping_rates(shipments)
        #   shipping_rates_serializer.render(shipments, serializer_params.merge({include: [:shipping_rates, :stock_location, :line_items], view: :checkout}))
        #   # shipping_rates_serializer.render(
        #   #   shipments,
        #   #   params: serializer_params,
        #   #   include: [:shipping_rates, :stock_location, :line_items],
        #   # ).serializable_hash
        # end

        def serialize_shipping_rates(shipments)
          shipping_rates_serializer.render(shipments, serializer_params.merge({ view: :all }))
          # shipping_rates_serializer.new(
          #   shipments,
          #   params: serializer_params,
          #   include: [:shipping_rates, :stock_location, :inventory_units],
          # ).serializable_hash
        end

        private

        def destroy_previous_payments
          order_params = params.dig(:order)

          if order_params.present?
            shipments_attributes = order_params.dig(:shipments_attributes)
            if shipments_attributes.present?
              sh = shipments_attributes[0]
              sl_shipping_rate_id = sh["selected_shipping_rate_id"]
              if sl_shipping_rate_id.present? # make sure enter storefront saveShippingMethod step
                spree_current_order&.payments&.destroy_all # destroy all previous payments of the order
              end
            end
          end
        end

        def check_subscriber
          order_params = params.dig(:order)

          if order_params.present?
            email = order_params.dig(:email)
            if email.present? # make sure enter storefront fill in shipping address step
              subscriber = ::Spree::Subscriber.find_or_create_by(email: email)
              process_subscriber_action(subscriber, "filling in shipping address", spree_current_order) if subscriber
            end
          end
        end
      end
    end
  end
end
