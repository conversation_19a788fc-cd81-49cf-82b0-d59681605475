# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class IntentsController < Api::V3::Storefront::BaseController
        include Api::V3::Storefront::OrderConcern

        def create
          spree_authorize!(:update, spree_current_order, order_token)

          # enter storefront createPaymentIntent step
          spree_current_order&.payments&.destroy_all # destroy all previous payments of the order

          last_valid_payment = spree_current_order.payments.valid.where.not(intent_client_key: nil).last

          if last_valid_payment.blank?
            if spree_current_order.payments.valid.count == 0
              ret = create_payment_service.call(
                order: spree_current_order,
                params: { payment_method_id: params["payment_method_id"] },
              )
            end

            unless Rails.env.test?
              # spree_current_order.reload
              spree_current_order.create_payment_intent!
              spree_current_order.reload
            end

            last_valid_payment = spree_current_order.payments.valid.where.not(intent_client_key: nil).last
          end

          return mock_response if Rails.env.test?

          if last_valid_payment.present?
            client_secret = last_valid_payment.intent_client_key
            return render(json: { client_secret: client_secret }, status: :ok)
          end

          render_error_payload(I18n.t("spree.no_payment_intent_created"))
        end

        def create_payment_service
          ::Spree::Api::Dependencies.storefront_payment_create_service.constantize
        end

        def payment_confirmation_data
          spree_authorize! :update, spree_current_order, order_token

          if spree_current_order.intents?
            spree_current_order.process_payments!
            spree_current_order.reload
            last_valid_payment = spree_current_order.payments.valid.where.not(intent_client_key: nil).last
            if last_valid_payment.present?
              client_secret = last_valid_payment.intent_client_key
              publishable_key = last_valid_payment.payment_method&.preferred_publishable_key
              return render json: { client_secret: client_secret, pk_key: publishable_key }, status: :ok
            end
          end
          render_error_payload(I18n.t("spree.no_payment_authorization_needed"))
        end

        def handle_response
          if params["response"]["error"]
            invalidate_payment
            render_error_payload(params["response"]["error"]["message"])
          else
            render_serialized_payload { { message: I18n.t("spree.payment_successfully_authorized") } }
          end
        end

        private

        def invalidate_payment
          payment = spree_current_order.payments.find_by!(response_code: params["response"]["error"]["payment_intent"]["id"])
          payment.update(state: "failed", intent_client_key: nil)
        end

        def mock_response
          # This function only used for specs test cases!!!
          return render(json: { client_secret: "mock_data" }, status: :ok)
        end
      end
    end
  end
end
