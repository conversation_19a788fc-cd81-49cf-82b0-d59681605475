# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class SubscribeNewslettersController < ::Spree::Api::V2::ResourceController
        include ::Spree::Admin::Subscribers<PERSON><PERSON><PERSON>

        def create
          if current_store.new_order_notifications_email.blank?
            return render_error_payload("Seller email not set")
          end

          if (current_store.new_order_notifications_email =~ Devise.email_regexp).nil?
            return render_error_payload("Seller email invalid")
          end

          if params[:email].blank?
            return render_error_payload("Subscriber email not set")
          end

          if (params[:email] =~ Devise.email_regexp).nil?
            return render_error_payload("Subscriber email invalid")
          end

          unless Rails.env.test?
            ::Spree::SubscribeNewsletterMailer.new.send_seller_mail(
              current_store,
              params[:email],
            )
          end

          if params[:email]
            subscriber = ::Spree::Subscriber.find_or_create_by(email: params[:email])
            process_subscriber_action(subscriber, "subscribed newsletter") if subscriber
          end

          render(json: { message: "Subscribe success" }, status: :ok)
        rescue => e
          render_error_payload("Subscribe fail #{e.message}")
        end
      end
    end
  end
end
