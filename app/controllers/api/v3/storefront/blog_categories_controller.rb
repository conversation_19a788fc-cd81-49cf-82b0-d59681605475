# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class BlogCategoriesController < ::Api::V3::Storefront::ResourceController
        def model_class
          Spree::BlogCategory
        end

        private

        def spree_permitted_attributes
          []
        end

        protected

        def scope
          @scope ||= Spree::BlogCategory.for_store(current_store).friendly.visible
        end
      end
    end
  end
end