# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class SubscriptionsController < ::Api::V3::Storefront::ResourceController
        before_action :require_spree_current_user

        before_action :require_resource_exists, only: %i[show edit destroy update revise_status fetch_delivery_date fetch_delivery_intervals fetch_subscription_orders]
        before_action :require_resource_active, only: %i[update revise_status]

        def model_class
          Spree::Subscription
        end

        def show
          render_serialized_payload { serialize_resource(resource) }
        end

        def edit
          render_serialized_payload { serialize_resource(resource) }
        end

        def update
          if params[:next_delivery_date].present?
            next_delivery_date = Date.strptime(params[:next_delivery_date], "%m/%d/%Y")
            updated_params = subscription_params.merge(next_delivery_date:)
          else
            updated_params = subscription_params
          end

          if resource.update(updated_params)
            render_serialized_payload { serialize_resource(resource) }
          else
            render_serialized_payload(422) { { errors: resource.errors.full_messages.join(", ") } }
          end
        end

        def revise_status
          new_status = resource.status == "subscribe" ? "unsubscribe" : "subscribe"
          if resource.update(status: new_status)
            render(
              json: {
                message: Spree.t(
                  "admin.subscription.subscription_operation_message",
                  message: new_status.downcase,
                ),
              },
              status: :ok,
            )
          else
            render(json: { errors: resource.errors.full_messages.join(", ") }, status: :unprocessable_entity)
          end
        end

        def fetch_subscription_orders
          orders = resource.orders
          render_serialized_payload { serialize_collection_order(orders) }
        end

        def fetch_delivery_date
          next_date = resource.next_delivery_date.presence || resource.created_at
          interval = resource.interval.presence || "1_week"
          date = fetch_next_order_date(next_date, interval)&.to_date

          render(json: { skip_delivery_date: next_date&.to_date, next_delivery_date: date }, status: :ok)
        end

        def fetch_delivery_intervals
          intervals = Spree::Subscription::DELIVERY_INTERVALS

          response_data = intervals.map do |key, value|
            { label: key, value: value, selected: value == resource.interval }
          end

          render(json: { intervals: response_data }, status: :ok)
        end

        protected

        def spree_permitted_attributes
          []
        end

        private

        def fetch_next_order_date(current_date, interval)
          numeric_part = interval.split("_")[0].to_i
          interval = interval.split("_")[1]
          date = current_date

          # Determine the appropriate interval method
          case interval
          when "day", "days"
            date + numeric_part.days
          when "week", "weeks"
            date + numeric_part.weeks
          when "month", "months"
            date + numeric_part.months
          when "year", "years"
            date + numeric_part.years
          end
        end

        # def serialize_resource(resource)
        #   Spree::Api::V2::Storefront::SubscriptionSerializer.new(resource).serializable_hash
        # end

        # def serialize_collection(collection)
        #   Spree::Api::V2::Storefront::SubscriptionSerializer.new(collection).serializable_hash
        # end

        def serialize_collection_order(collection)
          Api::V3::Storefront::OrderSerializer.render(collection)
        end

        def permitted_resource_params
          subscription_params
        end

        def subscription_params
          permitted_params = params.require(:subscription).permit(
            # :user_id,
            :line_item_id,
            :interval,
            :next_delivery_date,
            :status,
            :quantity,
            :initial_price,
            :recurring_price,
          )

          permitted_params.merge(user_id: spree_current_user.id)
        end

        def resource
          @resource ||= ::Spree::Subscription.find_by(id: params[:id], user_id: spree_current_user.id)
        end

        def collection
          spree_current_user.subscriptions.order(created_at: :desc, id: :desc)
        end

        def serializer_params
          include_fields = params[:include]&.split(",") || []
          super.tap do |params|
            params[:include] = include_fields
          end
        end

        def require_resource_exists
          render(json: { error: Spree.t("admin.subscription.subscription_not_found") }, status: :not_found) and return if resource.nil?
        end

        def require_resource_active
          unless resource.is_active
            render(json: { error: Spree.t("admin.subscription.inactive") }, status: :unprocessable_entity) and return
          end
        end
      end
    end
  end
end
