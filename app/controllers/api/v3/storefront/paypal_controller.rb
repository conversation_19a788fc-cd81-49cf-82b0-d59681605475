# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class PaypalController < ResourceController
        include Api::V3::Storefront::OrderConcern

        def create
          express
        end

        def express
          order = spree_current_order
          raise(ActiveRecord::RecordNotFound) if order.blank?
          # destroy payments if exist for new order
          order.payments.destroy_all
          items = order.line_items.map(&method(:line_item))

          additional_adjustments = order.all_adjustments.additional
          tax_adjustments = additional_adjustments.tax
          shipping_adjustments = additional_adjustments.shipping

          additional_adjustments.eligible.each do |adjustment|
            # Because PayPal doesn't accept $0 items at all. See #10
            # https://cms.paypal.com/uk/cgi-bin/?cmd=_render-content&content_ID=developer/e_howto_api_ECCustomizing
            # "It can be a positive or negative value but not zero."
            next if adjustment.amount.zero?
            next if tax_adjustments.include?(adjustment) || shipping_adjustments.include?(adjustment)

            items << {
              Name: adjustment.label,
              Quantity: 1,
              Amount: {
                currencyID: order.currency,
                value: adjustment.amount,
              },
            }
          end

          ret = express_checkout_request_details(order, items, params["confirm_url"], params["cancel_url"])
          pp_request = provider.build_set_express_checkout(ret)
          begin
            return mock_response if Rails.env.test?
            pp_response = provider.set_express_checkout(pp_request)
            Spree::PaypalExpressRequest.create({
              token: pp_response.Token,
              success: pp_response.success?,
              order: spree_current_order,
              payment_method: payment_method,
              state: "processing",
              response: JSON.dump({
                Timestamp: pp_response.Timestamp,
                Ack: pp_response.Ack,
                CorrelationID: pp_response.CorrelationID,
                Version: pp_response.Version,
                Build: pp_response.Build,
                Token: pp_response.Token,
              }),
            })
            if pp_response.success?
              render_serialized_payload { { redirect: provider.express_checkout_url(pp_response, useraction: "commit") } }
            else
              render_serialized_payload { { error: Spree.t("flash.generic_error", scope: "paypal", reasons: pp_response.errors.map(&:long_message).join(" ")) } }
            end
          rescue SocketError
            render_serialized_payload { { error: Spree.t("flash.connection_failed", scope: "paypal") } }
          end
        end

        def confirm
          pp_request = Spree::PaypalExpressRequest.find_by!(token: params[:token])
          pp_request.payment_method.set_preference(:intents, false)
          pp_request.payment_method.save!

          order = pp_request.order || raise(ActiveRecord::RecordNotFound)
          if pp_request.state != "processing"
            return render_serialized_payload { { error: "invalid paypal express confirm." } }
          end

          if order.complete?
            return render_with_order(order)
          end
          pp_request.state = "confirming"
          pp_request.save

          order_total = order.total
          if order.payments.valid.present?
            order_total = order_total - order.payments.valid.completed.map(&:amount).sum
          end

          payment = order.payments.paypal.checkout.last || order.payments.build
          if payment.persisted?
            payment.update!(
              source: Spree::PaypalExpressCheckout.create!(
                token: params[:token],
                payer_id: params[:payer_id],
              ),
              amount: order_total,
            )
          else
            payment.assign_attributes(
              source: Spree::PaypalExpressCheckout.create!(
                token: params[:token],
                payer_id: params[:payer_id],
              ),
              amount: order_total,
              payment_method: pp_request.payment_method,
            )
            payment.save!
          end

          return render_with_order(order) if Rails.env.test?

          result = complete_service.call(order: order)
          pp_request.state = "confirmed"
          pp_request.save
          render_order(result)
          # if result.success?
          #   render_serialized_payload { serialize_resource(order) }
          # else
          #   render_error_payload(result.error)
          # end
        end

        def complete_service
          Spree::Api::Dependencies.storefront_checkout_complete_service.constantize
        end

        def resource_serializer
          Api::V3::Storefront::OrderSerializer
        end

        def cancel
          order = Spree::PaypalExpressRequest.find_by!(token: params[:token]).order || raise(ActiveRecord::RecordNotFound)
          render_serialized_payload { { state: order.state, paypal_cancel_token: params[:token], action: "cancel" } }
        end

        private

        def line_item(item)
          pack_size = item.listing&.pack_size_value || 1
          item_quantity = item.storefront_sale_channel? ? item.quantity / pack_size : item.quantity
          {
            Name: item.product.name,
            Number: item.variant.sku,
            Quantity: item_quantity,
            Amount: {
              currencyID: item.order.currency,
              value: item.price,
            },
            ItemCategory: "Physical",
          }
        end

        def express_checkout_request_details(order, items, confirmUrl, cancelUrl)
          { SetExpressCheckoutRequestDetails: {
            InvoiceID: "#{order.number}-#{Time.current.strftime("%H%M%S")}",
            BuyerEmail: order.email,
            ReturnURL: confirmUrl,
            CancelURL: cancelUrl,
            SolutionType: payment_method.preferred_solution.present? ? payment_method.preferred_solution : "Mark",
            LandingPage: payment_method.preferred_landing_page.present? ? payment_method.preferred_landing_page : "Billing",
            cppheaderimage: payment_method.preferred_logourl.present? ? payment_method.preferred_logourl : "",
            NoShipping: 1,
            PaymentDetails: [payment_details(items)],
          } }
        end

        def payment_method
          Spree::PaymentMethod.find(params[:payment_method_id])
        end

        def provider
          payment_method.provider
        end

        def payment_details(items)
          # This retrieves the cost of shipping after promotions are applied
          # For example, if shippng costs $10, and is free with a promotion, shipment_sum is now $10
          shipment_sum = spree_current_order.shipments.map(&:discounted_cost).sum

          # This calculates the item sum based upon what is in the order total, but not for shipping
          # or tax.  This is the easiest way to determine what the items should cost, as that
          # functionality doesn't currently exist in Spree core
          item_sum = spree_current_order.total - shipment_sum - spree_current_order.additional_tax_total

          #https://github.com/spree-contrib/better_spree_paypal_express/pull/207
          order_total = spree_current_order.total
          if spree_current_order.payments.present?
            payments = spree_current_order.payments.map(&:amount).sum
            payment_numbers = spree_current_order.payments.map(&:number).join(", ")
            item_sum = item_sum - payments
            order_total = order_total - payments
            items << {
              Name: "Payment #{payment_numbers}",
              Quantity: 1,
              Amount: {
                currencyID: spree_current_order.currency,
                value: -(payments),
              },
            }
          end

          if item_sum.zero?
            # Paypal does not support no items or a zero dollar ItemTotal
            # This results in the order summary being simply "Current purchase"
            {
              OrderTotal: {
                currencyID: spree_current_order.currency,
                value: order_total,
              },
            }
          else
            {
              OrderTotal: {
                currencyID: spree_current_order.currency,
                value: spree_current_order.total,
              },
              ItemTotal: {
                currencyID: spree_current_order.currency,
                value: item_sum,
              },
              ShippingTotal: {
                currencyID: spree_current_order.currency,
                value: shipment_sum,
              },
              TaxTotal: {
                currencyID: spree_current_order.currency,
                value: spree_current_order.additional_tax_total,
              },
              ShipToAddress: address_options,
              PaymentDetailsItem: items,
              ShippingMethod: "Shipping Method Name Goes Here",
              PaymentAction: "Sale",
            }
          end
        end

        def address_options
          return {} unless address_required?

          {
            Name: spree_current_order.bill_address.try(:full_name),
            Street1: spree_current_order.bill_address.address1,
            Street2: spree_current_order.bill_address.address2,
            CityName: spree_current_order.bill_address.city,
            Phone: spree_current_order.bill_address.phone,
            StateOrProvince: spree_current_order.bill_address.state_text,
            Country: spree_current_order.bill_address.country.iso,
            PostalCode: spree_current_order.bill_address.zipcode,
          }
        end

        def address_required?
          payment_method.preferred_solution.eql?("Sole")
        end

        def render_with_order(order)
          render_serialized_payload { serialize_resource(order, collection_serializer, { view: :all }) }
        end

        def mock_response
          random_token = "random-token-for-test-#{SecureRandom.hex(10)}"
          Spree::PaypalExpressRequest.create({
            token: random_token,
            success: true,
            order: spree_current_order,
            payment_method: payment_method,
            state: "processing",
            response: JSON.dump({
              Timestamp: Time.now,
              Ack: "",
              CorrelationID: "",
              Version: "",
              Build: "",
              Token: random_token,
            }),
          })
          render_serialized_payload { { redirect: "https://example.com?xxxxx=xxxxx" } }
        end
      end
    end
  end
end
