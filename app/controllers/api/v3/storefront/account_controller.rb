# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class AccountController < ::Api::V3::Storefront::ResourceController
        include ::Spree::Admin::SubscribersHelper

        include JsonWebToken

        before_action :validate_json_web_token, except: [:oauth_login, :logout, :update, :show]
        before_action :require_spree_current_user, only: [:update, :logout, :show]

        def oauth_login
          provider = params[:provider]
          auth_token = params[:auth_token]
          commit_action = params[:commit]

          return render_error_payload("Missing auth token", :bad_request) unless auth_token

          auth_data = begin
              JWT.decode(auth_token, nil, false).first
            rescue
              nil
            end
          return render_error_payload("Invalid token data", :unauthorized) unless auth_data && auth_data["email"]

          email = auth_data["email"]
          user = Spree::User.find_by(email: email)
          uid = auth_data["sub"]

          token = JsonWebToken.encode(
            provider: provider,
            uid: uid,
            email: email,
          )

          case commit_action
          when "signup"
            if user.present?
              render_error_payload("User with #{email} already exists. Please sign in instead.")
            else
              begin
                password, password_confirmation = check_params_password
                user = nil
                ActiveRecord::Base.transaction do
                  user = create_customer(email, password, password_confirmation, uid)
                end

                render json: create_token(user), status: :ok
              rescue StandardError => e
                render_error_payload("Signup failed: #{e.message}")
              end
            end
          when "signin"
            if user.nil?
              render_error_payload("This email address is not registered. Please sign up first.")
            else
              render json: create_token(user), status: :ok
            end
          else
            render_error_payload("Invalid commit action. Use 'signin' or 'signup'.")
          end
        end

        def update
          spree_authorize! :update, spree_current_user
          result = update_service.call(user: spree_current_user, user_params: user_update_params)
          render_result(result)
        end

        def sign_up
          email = check_email
          password, password_confirmation = check_params_password

          user = nil
          result = nil
          ActiveRecord::Base.transaction do
            user = create_customer(email, password, password_confirmation)
            result = create_token(user)
          end

          render json: result, status: :ok
        rescue StandardError => e
          render_error_payload(e.message)
        end

        def logout
          doorkeeper_token.revoke

          render json: { message: "Logout successful." }, status: :ok
        rescue StandardError => e
          render_error_payload(e.message)
        end

        def reset_password
          email = check_email
          password, password_confirmation = check_params_password

          user = Spree::User.find_by(email: email)
          return render_error_payload("User not found.", :not_found) if user.blank?
          ActiveRecord::Base.transaction do
            result = update_service.call(user: user, user_params: { password: password, password_confirmation: password_confirmation })
            raise result.error unless result.success?
          end

          render json: { message: "Password reset successfully" }, status: :ok
        rescue StandardError => e
          render_error_payload(e.message)
        end

        private

        def resource
          spree_current_user
        end

        def create_customer(email, password, password_confirmation, uid = nil)
          user = Spree::User.find_by(email: email)
          raise "User with #{email} already exists. Please sign in instead." if user.present?

          result = create_service.call(user_params: { email: email, password: password, password_confirmation: password_confirmation, uid: uid })
          if result.success? && result&.value&.email.present?
            subscriber = ::Spree::Subscriber.find_or_create_by(email: result&.value&.email)
            process_subscriber_action(subscriber, "registered") if subscriber
          end

          raise result.error unless result.success?

          result&.value
        end

        def model_class
          Spree.user_class
        end

        def create_service
          Spree::Api::Dependencies.storefront_account_create_service.constantize
        end

        def update_service
          Spree::Api::Dependencies.storefront_account_update_service.constantize
        end

        def user_create_params(email = nil)
          customer_role = ::Spree::Role.where(name: :store_customer).first_or_initialize
          ret = params.require(:user).permit([:email, :first_name, :last_name, :email, :password, :password_confirmation])
            .merge(spree_stores: current_store.default ? [current_store] : [::Spree::Store.default, current_store])
            .merge(role_users: [::Spree::RoleUser.new(store: current_store, role: customer_role)])
          ret[:email] = email if ret[:email].blank?
          ret[:login] = email if ret[:login].blank?
          ret
        end

        def user_update_params
          params.require(:user).permit([:bill_address_id, :ship_address_id, :first_name, :last_name,
                                        { public_metadata: {}, private_metadata: {} }, :selected_locale])
        end

        private

        def spree_permitted_attributes
          []
        end

        def check_email
          @email = @decoded_token["email"]
          raise "Email address is required" if @email.blank?
          @email
        end

        def check_params_password
          password = params.dig(:password)
          password_confirmation = params.dig(:password_confirmation)

          unless password.present? && password == password_confirmation
            raise "Password and confirmation do not match or are blank."
          end

          [password, password_confirmation]
        end

        def check_subscriber(user)
          if user.email
            subscriber = ::Spree::Subscriber.find_or_create_by(email: user.email)
            process_subscriber_action(subscriber, "logged in") if subscriber
          end
        end

        def create_token(user)
          token = Spree::OauthAccessToken.create!(
            resource_owner_id: user.id,
            scopes: "",
            resource_owner_type: "Spree::User",
            expires_in: Doorkeeper.configuration.access_token_expires_in.to_i,
            use_refresh_token: true,
          )

          check_subscriber(user) if token.present?
          {
            # tenant_url: generate_tenant_url(user.tenant_name),
            access_token: token.plaintext_token || token.token,
            token_type: "Bearer",
            expires_in: token.expires_in,
            refresh_token: token.plaintext_refresh_token || token.refresh_token,
            created_at: token.created_at.to_i,
          }
        end
      end
    end
  end
end
