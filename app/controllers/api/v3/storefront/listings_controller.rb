# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class ListingsController < ::Api::V3::Storefront::ResourceController
        def model_class
          Spree::Listing
        end

        def collection
          base_scope = scope
          params = finder_params
          user = params[:user]
          managed_store_ids = []

          if !user.nil? && !user.spree_admin?
            user.spree_roles.each do |role|
              if role.name == 'store_owner' || role.name == 'store_employee'
                store_ids = user.role_users.filter_map { |ru| ru.store_id if ru.role_id == role.id }
                managed_store_ids += store_ids
              end
            end
          end
          managed_store_ids = managed_store_ids.uniq

          # filter active product in sub store for customer or guest user
          if !current_store.default && (user.nil? || (!user.spree_admin? && managed_store_ids.exclude?(current_store.id)))
            unless current_store.published
              @collection ||= base_scope.where(id: nil)
              return @collection
            end
          end

          if params[:sort] == 'featured'
            base_scope = base_scope.joins(:featured_products)
          end
          @collection ||= collection_finder.new(
            scope: base_scope,
            params: params,
            current_currency: nil,
            store_id: current_store.id
          ).execute
        end

        def resource
          @resource ||= ::Spree::Listing.active.find(params[:id])
        end

        def scope(skip_cancancan: false)
          base_scope = current_store.default ? model_class : model_class.for_store(current_store)
          base_scope = base_scope.active
          # base_scope = base_scope.accessible_by(current_ability, :show) unless skip_cancancan
          base_scope = base_scope.includes(scope_includes) if scope_includes.any? && action_name == 'index'
          base_scope
        end

        def filters
          ret = Spree::Api::Products::FiltersPresenter.new(current_store, current_currency, params).to_h
          render json: ret, status: :ok
        rescue StandardError => e
          Rails.logger.error("Failed to Get filters: #{e.message}")
          render json: {error: 'An error occurred while get the filters'}, status: :unprocessable_entity
        end

        protected

        def serializer_params
          include_fields = params[:include]&.split(',') || []
          super.tap { |params|
            params[:include] = include_fields
            # params[:view] = :listing_product if ["index"].exclude?(action_name)
            params[:view] = ['show'].include?(action_name) ? :listing_product_full : :listing_product
          }
        end

        def collection_sorter
          Spree::Listings::Sort
        end

        def collection_finder
          Spree::Listings::Find
        end

        private

        def spree_permitted_attributes
          []
        end

        def fetch_filtered_products
          sorted_collection.joins(:sale_channel)
            .where(spree_sale_channels: {brand: 'storefront'})
        end

        def sorted_collection
          collection_sorter.new(collection, current_currency, params, allowed_sort_attributes).call
        end

        def paginated_collection
          @paginated_collection = collection_paginator.new(fetch_filtered_products, params).call
        end
      end
    end
  end
end
