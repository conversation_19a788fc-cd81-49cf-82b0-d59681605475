# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class ProductsController < ::Api::V3::Storefront::ResourceController
        def model_class
          Spree::Product
        end

        def collection
          base_scope = scope
          params = finder_params
          user = params[:user]
          managed_store_ids = []

          if !user.nil? && !user.spree_admin?
            user.spree_roles.each do |role|
              if role.name == 'store_owner' || role.name == 'store_employee'
                store_ids = user.role_users.filter_map { |ru| ru.store_id if ru.role_id == role.id }
                managed_store_ids += store_ids
              end
            end
          end
          managed_store_ids = managed_store_ids.uniq

          # filter active product in sub store for customer or guest user
          if !current_store.default && (user.nil? || (!user.spree_admin? && managed_store_ids.exclude?(current_store.id)))
            unless current_store.published
              @collection ||= base_scope.where(id: nil)
              return @collection
            end
          end

          if params[:sort] == 'featured'
            base_scope = base_scope.joins(:featured_products)
          end
          @collection ||= collection_finder.new(
            scope: base_scope,
            params: params,
            current_currency: nil,
            store_id: current_store.id
          ).execute
        end

        def resource
          @resource ||= scope.find_by(slug: params[:id]) ||
            scope.find_by(id: params[:id]) ||
            ::Spree::Product.find_by!(slug: params[:id])

          return unless @resource

          sale_channel = @resource.all_active_product_listings.any?
          @resource.active? && sale_channel ? @resource : nil
        end

        def scope(skip_cancancan: false)
          base_scope = current_store.default ? model_class : model_class.for_store(current_store)
          base_scope = base_scope.with_status(:active)
          base_scope = base_scope.accessible_by(current_ability, :show) unless skip_cancancan
          base_scope = base_scope.includes(scope_includes) if scope_includes.any? && action_name == 'index'
          base_scope
        end

        protected

        def serializer_params
          include_fields = params[:include]&.split(',') || []
          super.tap { |params|
            params[:include] = include_fields
            # params[:view] = :product_listing if ["index"].exclude?(action_name)
            params[:view] = :product_listing
          }
        end

        def collection_sorter
          Spree::Api::Dependencies.storefront_products_sorter.constantize
        end

        def collection_finder
          Spree::Api::Dependencies.storefront_products_finder.constantize
        end

        private

        def spree_permitted_attributes
          []
        end

        def fetch_filtered_products
          listing_ids = []
          if params[:filter] && params[:filter][:listing_ids].present?
            listing_ids = params[:filter][:listing_ids].split(',')&.map(&:to_i)
          end
          if listing_ids.any?
            sorted_collection.select('spree_products.*, listings.id as listing_id')
              .joins(listings: :sale_channel)
              .where(listings: {status: 'Active'})
              .where(spree_sale_channels: {brand: 'storefront'})
              .where(listings: {id: listing_ids})
          else
            sorted_collection.select('spree_products.*, listings.id as listing_id')
              .joins(listings: :sale_channel)
              .where(listings: {status: 'Active'})
              .where(spree_sale_channels: {brand: 'storefront'})
          end
        end

        def sorted_collection
          collection_sorter.new(collection, current_currency, params, allowed_sort_attributes).call
        end

        def paginated_collection
          @paginated_collection = collection_paginator.new(fetch_filtered_products, params).call
        end
      end
    end
  end
end
