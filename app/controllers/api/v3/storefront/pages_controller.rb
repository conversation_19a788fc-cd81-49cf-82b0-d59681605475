# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class PagesController < BaseController
        def show
          render_serialized_payload do
            ::Api::V3::Storefront::PageSerializer.render(resource)
          end
        end

        private

        def resource
          @resource ||= current_store.cms_pages.find_by!(type: cms_page_type, locale: locale)
        end

        def cms_page_type
          type = case params[:id].to_s
          when 'home' then 'Homepage'
          else
            "#{params[:id].to_s.classify}Page"
          end
          "Spree::Cms::Pages::#{type}"
        end

        def locale
          params[:locale] || 'en'
        end
      end
    end
  end
end
