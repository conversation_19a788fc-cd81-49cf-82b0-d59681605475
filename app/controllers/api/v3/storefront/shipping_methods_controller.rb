# frozen_string_literal: true

module Api
  module V3
    module Storefront
      class ShippingMethodsController < ::Api::V3::Storefront::ResourceController
        include Api::V3::Storefront::OrderConcern

        before_action :require_spree_current_user
        before_action :ensure_order

        def model_class
          Spree::ShippingMethod
        end

        def list_local_pickup_shipping_methods
          matched_shipping_method = ShippingMethod::ShippingMethodProcessor.new(spree_current_order).local_pickup_shipping_methods
          render_serialized_payload { serialize_resource(matched_shipping_method) }
        end

        def serialize_shipping_methods(local_pickup_shipping_methods)
          Api::V3::Storefront::ShippingMethodSerializer.new(
            local_pickup_shipping_methods,
          ).serializable_hash
        end
      end
    end
  end
end
