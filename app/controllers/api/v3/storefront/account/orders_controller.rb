# frozen_string_literal: true

module Api
  module V3
    module Storefront
      module Account
        class OrdersController < ::Api::V3::Storefront::ResourceController
          before_action :require_spree_current_user

          def model_class
            Spree::Order
          end

          protected

          def spree_permitted_attributes
            []
          end

          def serializer_params
            include_fields = params[:include]&.split(",") || []
            super.tap do |params|
              params[:include] = include_fields
              params[:view] = ["show"].include?(action_name) ? :all : nil
            end
          end

          private

          def collection
            collection_finder.new(user: spree_current_user, store: current_store).execute
          end

          def resource
            resource = resource_finder.new(user: spree_current_user, number: params[:id], store: current_store).execute.take
            raise ActiveRecord::RecordNotFound if resource.nil?

            resource
          end

          def allowed_sort_attributes
            super << :completed_at
          end

          # def collection_serializer
          #   Spree::Api::Dependencies.storefront_order_serializer.constantize
          # end

          # def resource_serializer
          #   Spree::Api::Dependencies.storefront_order_serializer.constantize
          # end

          def collection_finder
            Spree::Api::Dependencies.storefront_completed_order_finder.constantize
          end

          def resource_finder
            Spree::Api::Dependencies.storefront_completed_order_finder.constantize
          end

          def model_class
            Spree::Order
          end
        end
      end
    end
  end
end
