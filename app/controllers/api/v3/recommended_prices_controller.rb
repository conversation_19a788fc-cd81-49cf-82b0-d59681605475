# frozen_string_literal: true

module Api
  module V3
    class RecommendedPricesController < ResourceController
      before_action :find_listing_inventory, only: [:show, :update, :apply, :history]

      def token
        service = PriceAnalytics::Client.new(**price_analytics_connection_info)
        token = service.request_new_access_token

        render json: {host: current_store.price_analytics_url, token: token}
      end

      def show
        unless @recommended_price.persisted?
          @recommended_price.save
        end
        render_serialized_payload { serialize_resource(@recommended_price) }
      end

      def update
        if @recommended_price.update(update_params)
          render_serialized_payload { serialize_resource(@recommended_price) }

        else
          render_error_payload(@recommended_price.errors)
        end
      end

      def apply
        old_price = @recommended_price.listing_inventory.price
        @recommended_price.listing_inventory.price = params[:price]
        @recommended_price.listing_inventory.save!
        @recommended_price.reload

        Spree::PriceHistory.create!(
          variant_id: @recommended_price.variant_id,
          currency: 'USD',
          previous_amount: old_price,
          amount: @recommended_price.listing_inventory.price,
          kind: 'manual',
          strategy: params[:strategy]
        )
        render_serialized_payload { serialize_resource(@recommended_price) }
      end

      def history
        Spree::PriceHistory
          .where(variant_id: @recommended_price.variant_id)
          .order(created_at: :desc)
          .limit(20)
          .map { |price_history|
            {
              date: price_history.created_at,
              previous_amount: price_history.previous_amount,
              amount: price_history.amount,
              kind: price_history.kind,
              strategy: price_history.strategy
            }
          }
          .then { |json| render json: json }
      end

      # GET /api/v3/recommended_prices/export
      # User for 'price-analytics' service
      def export
        json = Spree::RecommendedPrice
                 .where(auto_update: true, status: 'active')
                 .where.not(shipping_cost: nil)
                 .where(auto_update_at: ..Time.current)
                 .joins(:variant).includes(variant: [:product, :prices])
                 .page(1).per(params[:limit])
                 .map { |record| collect_data(record) }
                 .select { |o| o['inventoryCost'].present? }
        render json: json
      end

      # PUT /api/v3/recommended_price
      # User for 'price-analytics' service
      def auto_update
        listing_inventory_id = params.require(:result).require(:input).require(:externalProductId)
        listing_inventory = Spree::ListingInventory.find(listing_inventory_id)
        recommended_price = listing_inventory&.recommended_price
        if recommended_price
          service = PriceAnalytics::Process.new(store: current_store, user: spree_current_user, recommended_price: recommended_price)
          service.update(params.to_unsafe_hash)
          recommended_price.data_updated_at = Time.current
          recommended_price.calc_next_auto_update_at
          recommended_price.save!

          recommended_price.apply_recommended_price!
        end
        render json: {'status': 'ok'}
      end

      protected

      def model_class
        Spree::RecommendedPrice
      end

      def authorize_spree_user
        return if spree_current_user.nil?
        return true if ['token', 'export', 'update', 'auto_update', 'history'].include?(action_name)
        super
      end

      def collect_data(recommended_price)
        listing_inventory = recommended_price.listing_inventory
        variant = recommended_price.variant
        {
          'externalUserId' => params[:externalUserId],
          'externalProductId' => listing_inventory&.id,
          'title' => listing_inventory&.listing&.title,
          'upc' => variant.upc,
          'currentPrice' => listing_inventory&.price,
          'inventoryCost' => variant_inventory_cost(variant),
          'shippingCost' => recommended_price.shipping_cost,
          'marketplaces' => ['ebay'],
          'stock' => variant.stock_items.sum(:count_on_hand),
          'stockIncreasedAt' => variant.stock_items.order(updated_at: :desc).first&.updated_at&.as_json,
          'sales' => variant_sales(variant),
          'lastShippedPrice' => variant_last_shipped_at(variant),
          'envelopCost' => '0.0999',
          'variableCharge' => '0.1325',
          'fixCharge' => '0.4',
          'tax' => '0.10'
        }
      end

      def variant_inventory_cost(variant)
        Spree::StockItemUnit
          .joins(:stock_item)
          .where(state: 'stock', stock_item: {variant_id: variant.id})
          .where.not(vendor_inventory_cost: '')
          .reorder(:variant_id)
          .group(:variant_id)
          .pluck(Arel.sql('AVG(CAST(vendor_inventory_cost AS DOUBLE PRECISION)) as average'))
          .to_a
          .first
      end

      def variant_sales(variant)
        variant
          .line_items
          .joins(:order)
          .where(order: {state: 'complete', completed_at: 14.days.ago.at_beginning_of_day..})
          .group(Arel.sql('DATE("order"."completed_at")'))
          .pluck(Arel.sql('DATE("order"."completed_at")'), Arel.sql('SUM("quantity") as total'))
          .map { |record| {record[0].as_json => record[1].to_i} }
      end

      def variant_last_shipped_at(variant)
        variant.line_items.joins(:order).where(order: {state: 'complete'}).order(completed_at: :desc).first&.order&.completed_at&.as_json
      end

      def find_listing_inventory
        @listing_inventory = Spree::ListingInventory.find(params[:id])
        @recommended_price = @listing_inventory.recommended_price ||
          @listing_inventory.build_recommended_price(variant_id: @listing_inventory.variant_id)
      end

      def update_params
        params.require(:recommended_price).permit(
          :zero_profit_price, :average_price, :average_effective_price,
          :min_price_ebay, :weighted_average_effective,
          :shipping_cost,
          :auto_update, :auto_update_period, :auto_update_at,
          :analysis_id, :stocks_rate_value, :sales_rate_value, :sales_period
        )
      end

      def price_analytics_connection_info
        {
          host: current_store.price_analytics_url,
          client_id: current_store.price_analytics_client_id,
          client_secret: current_store.price_analytics_client_secret
        }
      end
    end
  end
end
