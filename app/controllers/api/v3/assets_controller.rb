# frozen_string_literal: true

module Api
  module V3
    class AssetsController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index

      rescue_from ActiveRecord::SubclassNotFound, with: :subclass_error

      private

      def model_class
        Spree::Asset
      end

      def permitted_resource_params
        params.require(:asset).permit(:type, :viewable_type, :viewable_id, :attachment, :position)
          .tap { |params|
            params[:type] = "Spree::#{(params[:type] || "asset").classify}"
            params[:viewable_type] = "Spree::#{params[:viewable_type].to_s.classify}"
          }
      end

      def subclass_error(e)
        render_error_payload('type or viewable_type not found')
      end
    end
  end
end
