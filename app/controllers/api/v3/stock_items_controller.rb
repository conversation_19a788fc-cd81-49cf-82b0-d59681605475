# frozen_string_literal: true

module Api
  module V3
    class StockItemsController < ResourceController
      before_action :set_filters, only: :index
      after_action :record_activity_log, only: [:create, :update, :destroy]

      private

      def set_filters
        params[:filter] ||= {}
        if params[:product_id]
          params[:filter][:variant_product_id_eq] = params[:product_id]
        else
          # Force use pagination, when not use product scope
          params[:page] ||= 1
        end
        params[:filter][:stock_location_id_eq] = params[:filter].delete(:stock_id_eq) if params[:filter].key?(:stock_id_eq)
      end

      def model_class
        Spree::StockItem
      end

      def scope_includes
        [
          :stock_location,
          :stock_location_section,
          {
            variant: [
              {option_values: [:translations, {option_type: :translations}]},
              {product: :translations}
            ]
          }
        ]
      end

      def permitted_resource_params
        attrs = [:inventory_threshold, :backorderable]
        attrs.push(:stock_id, :stock_location_id, :variant_id, :count_on_hand) if action_name == 'create'
        params.require(:stock_item).permit(*attrs)
          .tap { |p|
            p[:stock_location_section_id] = p.delete(:stock_location_id) if p.key?(:stock_location_id)
            p[:stock_location_id] = p.delete(:stock_id) if p.key?(:stock_id)
          }
      end

      def serializer_params
        super.tap { |params| params[:view] = :index }
      end

      def record_activity_log
        return unless @resource.present?

        product = @resource.product

        ::Spree::ActivityLog.create!(
          loggable: @resource,
          user_id: spree_current_user.id,
          action: ::Spree::ActivityLogActions.get(:stock_item, resolve_stock_item_action),
          role: spree_current_user.spree_roles.first&.name,
          date: Time.current,
          email: spree_current_user.email,
          action_place: determine_stock_item_action_place(product),
          action_name: product&.name || "Unknown Product",
          product_id: product&.id
        )
      end

      def resolve_stock_item_action
          case action_name
          when "update" then :edit
          when "destroy" then :remove
          when "create" then :add
          else :default
          end
        end

      def determine_stock_item_action_place(product)
        return "N/A" unless product && %w[create update destroy].include?(action_name)

        "/admin/inventory/products/#{product.id}/stock"
      end
    end
  end
end
