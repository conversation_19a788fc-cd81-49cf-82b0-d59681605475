# frozen_string_literal: true

module Api
  module V3
    class EmailTemplatesController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index

      def test_mail
        if current_store.email_setting&.intercept_email.present?
          if resource.mailer_class == 'Spree::CustomerMailer'
            if Spree::CustomerMailer.new.send_test_customer_mail(resource.subject, resource.body)
              render_serialized_payload { {status: :email_sent_successfully} }
            else
              render_error_payload(:unable_to_send)
            end
          elsif resource.send_test_email
            render_serialized_payload { {status: :email_sent_successfully} }
          else
            render_error_payload(:unable_to_send)
          end
        else
          render_error_payload(:intercept_email_not_present)
        end
      end

      protected

      def model_class
        Spree::EmailTemplate
      end

      def allowed_sort_attributes
        []
      end

      def permitted_resource_params
        params.require(:email_template).permit(:template_name, :subject, :body,
          :description, :active, :template_type, variables: [], tag_ids: [])
          .tap { |p|
            p[:mailer_class] = 'Spree::CustomerMailer' if action_name == 'create'
          }
      end
    end
  end
end
