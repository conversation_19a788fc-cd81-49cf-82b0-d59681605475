# frozen_string_literal: true

module Api
  module V3
    class DashboardsController < BaseController
      def todo_statistics
        todo_statistics_info = []
        new_orders_scope = Spree::Order.for_store(current_store).where.not(completed_at: nil)
        new_orders_scope = new_orders_scope.where(shipment_state: :ready)
        new_orders_count = new_orders_scope.count

        alert_count = Spree::StockLocation.for_store(current_store).joins(:variants)
                        .where('inventory_threshold is not null and count_on_hand < inventory_threshold and track_inventory = ?', true)
                        .count
        ebay_app = current_store.sale_channels.find_by(brand: 'eBay')&.oauth_application
        ebay_message_count = nil
        if ebay_app.present?
          ebay_message_count = 10
        end

        todo_statistics_info.push({
          new_orders_count: new_orders_count,
          inventory_alert_products_count: alert_count,
          ebay_message_count: ebay_message_count
        })
        render(json: todo_statistics_info)
      end

      def payment_method_statistics
        time_scale = :daily
        start_date = case params[:time_range]
        when '1d'
          time_scale = :hourly
          Time.zone.today
        when '10d'
          Time.zone.today.prev_day(9)
        when '30d'
          Time.zone.today.prev_day(29)
        else
          Time.zone.today.prev_day(29)
        end
        statistics_period = start_date.beginning_of_day..Time.zone.today.end_of_day
        query_sql = sales_chart_query(time_scale, statistics_period).to_sql
        statistics_data = ActiveRecord::Base.connection.exec_query(query_sql)
        render(json: statistics_data)
      end

      def financial_statistics
        time_scale = :daily
        start_date = case params[:time_scale_type]
        when '1d'
          time_scale = :hourly
          Time.zone.today
        when '3d'
          Time.zone.today.prev_day(2)
        when '5d'
          Time.zone.today.prev_day(4)
        when '7d'
          Time.zone.today.prev_day(6)
        when '14d'
          Time.zone.today.prev_day(13)
        when '30d'
          Time.zone.today.prev_day(29)
        else
          Time.zone.today.prev_day(6)
        end
        revenue_data = revenue_query(start_date, time_scale)
        profit_data = profit_query(start_date, time_scale)
        render(json: {revenue_data: revenue_data, profit_data: profit_data})
      end

      def announcements
        announcements = nil
        if params[:all].present? && (params[:all] == 'true' || params[:all] == true)
          announcements = Spree::Announcement.for_store(current_store).order(created_at: :desc)
        else
          per_page = 25
          per_page = params[:per_page] if params[:per_page].present?
          announcements = Spree::Announcement.for_store(current_store).order(created_at: :desc).page(params[:page]).per(per_page)
        end
        render(json: AnnouncementSerializer.render(announcements))
      end

      private

      def payments(time_scale, chart_period)
        Spree::PaymentMethod.for_store(current_store)
          .joins(:payments)
          .where(spree_payments: {created_at: chart_period})
          .select(
            *Spree::Report::QueryTimeScale.select(time_scale, 'spree_payments'),
            'spree_payment_methods.name  as payment_method_name',
            'spree_payments.amount       as payment_amount'
          )
      end

      def payment_chart_query(time_scale, chart_period)
        time_scale_columns = Spree::Report::QueryTimeScale.time_scale_columns(time_scale)
        Spree::Report::QueryFragments
          .from_subquery(payments(time_scale, chart_period))
          .group(*time_scale_columns.collect(&:to_s), 'payment_method_name')
          .order(*time_scale_columns)
          .project(
            *time_scale_columns,
            'payment_method_name',
            'SUM(payment_amount) as payment_amount'
          )
      end

      def financial_time_scale_columns(time_scale)
        case time_scale
        when :hourly
          [:day, :hour]
        when :daily
          [:year, :month, :day]
        end
      end

      def revenue_query(start_date, scale = :daily)
        time_scale_columns = financial_time_scale_columns(scale)
        time_scale_columns_select = time_scale_columns.collect do |time_scale_column|
          ::Spree::Report::QueryFragments.public_send(time_scale_column, 'created_at')
        end
        query_sql = Spree::Report::QueryFragments
                      .from_subquery(
                        Spree::Order.for_store(current_store).where(created_at: start_date.beginning_of_day..Time.zone.today.end_of_day)
                      ).group(*time_scale_columns.collect(&:to_s))
                      .order(*time_scale_columns)
                      .project(
                        *time_scale_columns_select,
                        'SUM(total) as order_amount'
                      ).to_sql
        ActiveRecord::Base.connection.exec_query(query_sql)
      end

      def order_with_line_items(period, time_scale_columns)
        line_item_ar = Spree::LineItem.arel_table
        Spree::Order.for_store(current_store)
          .where.not(completed_at: nil)
          .where(created_at: period)
          .joins(:line_items)
          .group('spree_orders.id', *time_scale_columns.collect(&:to_s))
          .select(
            *time_scale_columns.collect do |column|
              ::Spree::Report::QueryFragments.public_send(column, 'spree_orders.created_at')
            end,
            "(spree_orders.item_total - SUM(#{Spree::Report::QueryFragments.if_null(
              line_item_ar[:cost_price],
              line_item_ar[:price]
            ).to_sql} * spree_line_items.quantity)) as profit_loss"
          )
      end

      def profit_query(start_date, scale = :daily)
        order_with_line_items_ar = Arel::Table.new(:order_with_line_items)
        time_scale_columns = financial_time_scale_columns(scale)
        zero = Arel::Nodes.build_quoted(0.0)
        query_sql = Spree::Report::QueryFragments
                      .from_subquery(order_with_line_items(
                        start_date.beginning_of_day..Time.zone.today.end_of_day,
                        time_scale_columns
                      ), as: :order_with_line_items)
                      .group(*time_scale_columns.collect(&:to_s))
                      .order(*time_scale_columns)
                      .project(
                        *time_scale_columns,
                        Spree::Report::QueryFragments.if_null(
                          Spree::Report::QueryFragments.sum(order_with_line_items_ar[:profit_loss]), zero
                        ).as('profit_loss')
                      ).to_sql
        ActiveRecord::Base.connection.exec_query(query_sql)
      end

      def sales_chart_query(time_scale, chart_period)
        time_scale_columns = Spree::Report::QueryTimeScale.time_scale_columns(time_scale)
        Spree::Report::QueryFragments
          .from_subquery(orders(time_scale, chart_period))
          .group(*time_scale_columns.collect(&:to_s), 'payment_method_name')
          .order(*time_scale_columns)
          .project(
            *time_scale_columns,
            "CASE WHEN application_name IS NULL THEN channel ELSE channel || '-' || application_name END as payment_method_name",
            'SUM(payment_amount) as payment_amount'
          )
      end

      def orders(time_scale, chart_period)
        Spree::Order.for_store(current_store)
          .joins('left join spree_sale_channels on spree_orders.sale_channel_id = spree_sale_channels.id')
          .joins('left join spree_oauth_applications on spree_sale_channels.id = spree_oauth_applications.sale_channel_id')
          .where(spree_orders: {created_at: chart_period})
          .where(spree_orders: {state: 'complete'})
          .select(
            *Spree::Report::QueryTimeScale.select(time_scale, 'spree_orders'),
            'spree_oauth_applications.name  as application_name',
            'spree_orders.total             as payment_amount',
            'spree_orders.channel           as channel'
          )
      end
    end
  end
end
