# frozen_string_literal: true

module Api
  module V3
    class MatchItsController < ResourceController
      before_action :load_product

      def index
        records = scope.where(product_id: @product.id)
        json_str = Api::V3::MatchItSerializer.render(records)
        render(json: json_str)
      end

      def batch_create
        records = []
        params[:product_ids]&.each do |product_id|
          records << Spree::MatchIt.where(matched_product_id: product_id, product_id: @product.id).first_or_create
        end
        render(json: Api::V3::MatchItSerializer.render(records))
      end

      def positions
        records = []
        params[:positions]&.each do |id, new_position|
          ret = Spree::MatchIt.where(id: id).update(position: new_position)
          records << ret[0] if ret.present?
        end
        render(json: Api::V3::MatchItSerializer.render(records))
      end

      protected

      def model_class
        Spree::MatchIt
      end

      def scope
        Spree::MatchIt.where(product_id: @product.id)
      end

      def permitted_resource_params
        params.require(:match_it).permit(:product_id, :product_ids)
      end

      private

      def load_product
        @product = Spree::Product.find_by(slug: params[:product_id])
      end
    end
  end
end
