# frozen_string_literal: true

module Api
  module V3
    class BarcodesController < ResourceController
      def scan
        item = Spree::ScannedItem.find_or_create_by(upc: code, user: spree_current_user)

        response = UpcItem::Lookup.new.get_product_details(code, params[:type], current_store)
        if response
          SpreeInsertion::UpcItem::ScannedItem.new(response[:items].first, item).execute
          item.update!(status: :completed)
          render(json: {status: 'ok'})
        else
          item.update!(status: :failed)
          render(json: {error: 'Product not found'}, status: :unprocessable_entity)
        end
      end

      private

      def authorize_spree_user
        return if spree_current_user.nil?

        case action_name
        when 'scan'
          true
        else
          super
        end
      end

      def code
        params[:id].to_s.strip
      end
    end
  end
end
