# frozen_string_literal: true

require 'uri'
require 'net/http'

module Api
  module V3
    class CampaignsController < ResourceController
      include ::Spree::Api::V3::CampaignHelper
      before_action :validate_subscribers_presence, only: [:create, :update]

      def create
        resource = model_class.new(permitted_resource_params)
        ensure_current_store(resource)

        if resource.save
          assign_subscribers(resource)
          handle_email_dispatch(resource)
          render_serialized_payload(:created) { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      def update
        resource = model_class.find(params[:id])
        ensure_current_store(resource)

        previous_status = resource.status
        previous_scheduled_date = resource.scheduled_date

        if resource.update(permitted_resource_params)
          assign_subscribers(resource)
          handle_scheduling_logic(resource, previous_status, previous_scheduled_date)
          render_serialized_payload(:ok) { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      def list_senders
        response = fetch_smtp2go_senders
        if response[:success]
          render json: {available_senders: response[:data]}, status: :ok
        else
          render json: {error: response[:error]}, status: :unprocessable_entity
        end
      end

      def clone
        campaign = resource.dup
        campaign.assign_attributes(
          sent_count: 0,
          delivered_count: 0,
          opened_count: 0,
          clicked_count: 0,
          unsubscribed_count: 0,
          status: :draft
        )

        if campaign.save!(validate: false)
          render_serialized_payload(:created) { serialize_resource(campaign) }
        else
          render_error_payload(campaign.errors)
        end
      rescue ActiveRecord::RecordInvalid => e
        render_error_payload(e.message)
      end

      private

      def model_class
        Spree::Campaign
      end

      def spree_permitted_attributes
        [
          :id,
          :title,
          :scheduled_date,
          :status,
          :template_id,
          :sender_email,
          :sent_count,
          :delivered_count,
          :opened_count,
          :clicked_count,
          :unsubscribed_count,
          :created_at,
          :updated_at,
          :store_id
        ]
      end

      def permitted_resource_params
        params.require(:campaign).permit(:title, :scheduled_date, :status, :template_id, :sender_email).tap { |p|
          p[:store_id] = current_store.id
        }
      end

      def validate_subscribers_presence
        return if ['active', 'scheduled'].exclude?(params[:campaign][:status])

        if Spree::Subscriber.where(id: params.dig(:campaign, :subscriber_ids)).empty?
          render json: {error: 'Audience has not been assigned to the email campaign'}
        end
      end

      def assign_subscribers(campaign)
        campaign.subscribers = Spree::Subscriber.where(id: params.dig(:campaign, :subscriber_ids))
      end

      def handle_email_dispatch(campaign)
        if campaign.active?
          send_campaign_emails_now(campaign)
        elsif campaign.scheduled?
          schedule_campaign_emails(campaign)
        end
      end

      def handle_scheduling_logic(campaign, previous_status, previous_scheduled_date)
        case [previous_status, campaign.status]
        when ['scheduled', 'scheduled']
          reschedule_campaign_emails(campaign) if previous_scheduled_date != campaign.scheduled_date
        when [nil, 'scheduled'], ['draft', 'scheduled'], ['archived', 'scheduled'], ['sent', 'scheduled'], ['active', 'scheduled']
          schedule_campaign_emails(campaign)
        when ['scheduled', 'active'], ['scheduled', 'draft'], ['scheduled', 'archived']
          cancel_scheduled_emails(campaign)
          send_campaign_emails_now(campaign) if campaign.status == 'active'
        when [nil, 'active'], ['draft', 'active'], ['archived', 'active'], ['sent', 'active']
          send_campaign_emails_now(campaign)
        end
      end

      def fetch_smtp2go_senders
        url = URI('https://api.smtp2go.com/v3/allowed_senders/view')
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true
        request = Net::HTTP::Post.new(url, {
          'accept' => 'application/json',
          'Content-Type' => 'application/json',
          'X-Smtp2go-Api-Key' => current_store.campaign_email_setting&.api_key
        })

        response = http.request(request)
        parsed_response = JSON.parse(response.body)

        if parsed_response['data']
          {success: true, data: parsed_response['data']['allowed_senders']}
        else
          {success: false, error: parsed_response['error'] || 'Unknown error'}
        end
      rescue StandardError => e
        {success: false, error: e.message}
      end
    end
  end
end
