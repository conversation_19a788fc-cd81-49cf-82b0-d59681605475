# frozen_string_literal: true

module Api
  module V3
    class TaxRatesController < ResourceController
      protected

      def model_class
        Spree::TaxRate
      end

      def scope_includes
        [:zone, :tax_category]
      end

      def permitted_resource_params
        params.require(:tax_rate).permit(
          :name,
          :tax_category_id,
          :zone_id,
          :amount,
          :included_in_price,
          :show_rate_in_label,
          calculator_attributes: [
            :type, :preferences
          ]
        )
      end
    end
  end
end
