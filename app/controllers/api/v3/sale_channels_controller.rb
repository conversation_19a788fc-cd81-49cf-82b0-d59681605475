# frozen_string_literal: true

module Api
  module V3
    class SaleChannelsController < ResourceController
      def fetch_walmart_package_type
        oauth_application = @resource.oauth_application
        package_types = ::Walmart::ShippingApis::PackageType.new(oauth_application.store.id, oauth_application.id).call
        render json: {message: 'success', data: package_types}
      rescue StandardError => e
        render_error_payload(e)
      end

      protected

      def model_class
        Spree::SaleChannel
      end

      def scope_includes
        [:oauth_application]
      end

      def allowed_sort_attributes
        []
      end

      def permitted_resource_params
        params.require(:sale_channel).permit(:name)
      end
    end
  end
end
