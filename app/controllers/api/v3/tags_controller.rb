# frozen_string_literal: true

module Api
  module V3
    class TagsController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index
      before_action -> { params[:sort] ||= '-created_at' }, only: :index

      def batch_create
        collection = params[:tags]
                       .split(/[\r\n,]+/)
                       .filter_map(&:strip)
                       .map do |name|
          model_class.create(name: name, store: current_store)
        end

        render_serialized_payload do
          serialize_collection(collection)
        end
      end

      def batch_destroy
        ids = params[:ids].split(',').map { |id| id.strip.to_i }
        model_class.destroy(ids)
        head(:no_content)
      end

      protected

      def model_class
        Spree::Tag
      end

      def allowed_sort_attributes
        [:name, :created_at]
      end

      def authorize_spree_user
        if ['batch_create', 'batch_destroy'].include?(action_name)
          true
        else
          super
        end
      end

      def permitted_resource_params
        params.require(:tag).permit(:name, :description)
      end
    end
  end
end
