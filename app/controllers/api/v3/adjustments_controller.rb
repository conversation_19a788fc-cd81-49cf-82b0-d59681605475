# frozen_string_literal: true

module Api
  module V3
    class AdjustmentsController < ResourceController
      before_action :load_order
      after_action :delete_promotion_from_order, only: [:destroy], if: -> { resource.destroyed? && resource.promotion? }
      after_action :update_totals, only: [:create, :update, :destory]

      def index
        render_serialized_payload { serialize_collection(collection) }
      end

      def create
        resource = @order.adjustments.build(order: @order)
        resource.assign_attributes(permitted_resource_params)

        if resource.save
          render_serialized_payload(:created) { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      def open
        adjustments = @order.all_adjustments.finalized
        adjustments.update_all(state: 'open') # rubocop:disable Rails/SkipsModelValidations

        render_serialized_payload { serialize_collection(collection) }
      end

      def close
        adjustments = @order.all_adjustments.not_finalized
        adjustments.update_all(state: 'closed') # rubocop:disable Rails/SkipsModelValidations

        render_serialized_payload { serialize_collection(collection) }
      end

      private

      def load_order
        @order ||= current_store.orders.find(params[:order_id])
      end

      def model_class
        Spree::Adjustment
      end

      def parent_scope
        load_order.all_adjustments
      end

      def scope
        parent_scope
      end

      def authorize_spree_user
        case action_name
        when 'open' then true
        when 'close' then true
        else
          super
        end
      end

      def permitted_resource_params
        params.require(:adjustment).permit(:label, :amount)
      end

      def update_totals
        @order.reload.update_with_updater!
      end

      def delete_promotion_from_order
        return if resource.source.nil?

        @order.promotions.delete(resource.source.promotion)
      end
    end
  end
end
