# frozen_string_literal: true

module Api
  module V3
    class FrequentlyBoughtsController < ResourceController
      before_action :load_listing

      def index
        records = scope.where(listing_id: @listing.id)
        json_str = Api::V3::FrequentlyBoughtSerializer.render(records)
        render(json: json_str)
      end

      def batch_create
        records = []
        params[:listing_ids]&.each do |listing_id|
          records << Spree::FrequentlyBought.where(matched_listing_id: listing_id, listing_id: @listing.id).first_or_create
        end
        render(json: Api::V3::FrequentlyBoughtSerializer.render(records))
      end

      def positions
        records = []
        params[:positions]&.each do |record|
          id = record[:id]
          new_position = record[:position]
          next if id.blank? || new_position.blank?

          ret = Spree::FrequentlyBought.where(id: id).update(position: new_position)
          records << ret[0] if ret.present?
        end
        render(json: Api::V3::FrequentlyBoughtSerializer.render(records))
      end

      protected

      def model_class
        Spree::FrequentlyBought
      end

      def authorize_spree_user
        return if spree_current_user.nil?

        case action_name
        when 'batch_create'
          spree_authorize!(:create, model_class)
        when 'positions'
          spree_authorize!(:update, model_class)
        else
          super
        end
      end

      def scope
        load_listing if @listing.blank?
        Spree::FrequentlyBought.where(listing_id: @listing.id)
      end

      def spree_permitted_attributes
        model_class.column_names.map(&:to_sym)
      end

      def permitted_resource_params
        params.require(:frequently_bought).permit(:listing_id, :listing_ids)
      end

      private

      def load_listing
        @listing = Spree::Listing.find_by(id: params[:listing_id])
      end
    end
  end
end
