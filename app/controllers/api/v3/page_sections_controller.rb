# frozen_string_literal: true

module Api
  module V3
    class PageSectionsController < ResourceController
      rescue_from ActiveRecord::SubclassNotFound, with: :render_withtype_error
      rescue_from ActiveModel::UnknownAttributeError, with: :render_unknownattribute_error

      def index1
        cms_sections = current_store.cms_sections
        cms_sections = current_store.cms_sections.where(cms_page_id: params[:cms_page_id]) if params[:cms_page_id]

        per_page = 25
        per_page = params[:per_page] if params[:per_page].present?
        cms_sections = cms_sections.page(params[:page]).per(per_page)

        render(json: CmsSectionSerializer.render(cms_sections))
      end

      def create1
        cms_page = current_store.cms_pages.find_by(id: params[:cms_page_id])
        unless cms_page
          return render_error_payload('CMS Page can not found')
        end

        unless validate_linked_resource_parms
          return render_error_payload('validate linked_resource parms failed')
        end

        cms_section = cms_page.cms_sections.build(
          permitted_resource_params.merge(
            cms_page: cms_page
          )
        )

        cms_section.link_type_one = params[:link_type_one] if params[:link_type_one]
        cms_section.link_one = params[:link_one] if params[:link_one]
        cms_section.title_one = params[:title_one] if params[:title_one]
        cms_section.link_type_two = params[:link_type_two] if params[:link_type_two]
        cms_section.link_two = params[:link_two] if params[:link_two]
        cms_section.title_two = params[:title_two] if params[:title_two]
        cms_section.link_type_three = params[:link_type_three] if params[:link_type_three]
        cms_section.link_three = params[:link_three] if params[:link_three]
        cms_section.title_three = params[:title_three] if params[:title_three]

        case params[:type]
        when 'Spree::Cms::Sections::HeroImage'
          cms_section.title = params[:title] if params[:title]
          cms_section.button_text = params[:button_text] if params[:button_text]
          cms_section.gutters = params[:gutters] if params[:gutters]
          if params[:image_one]
            cms_section.build_image_one
            cms_section.image_one.attachment = params[:image_one]
          end
        when 'Spree::Cms::Sections::ImageGallery'
          cms_section.display_labels = params[:display_labels] if params[:display_labels]
          if params[:image_one]
            cms_section.build_image_one
            cms_section.image_one.attachment = params[:image_one]
          end
          if params[:image_two]
            cms_section.build_image_two
            cms_section.image_two.attachment = params[:image_two]
          end
          if params[:image_three]
            cms_section.build_image_three
            cms_section.image_three.attachment = params[:image_three]
          end
        when 'Spree::Cms::Sections::SideBySideImages'
          cms_section.gutters = params[:gutters] if params[:gutters]
          cms_section.subtitle_one = params[:subtitle_one] if params[:subtitle_one]
          cms_section.subtitle_two = params[:subtitle_two] if params[:subtitle_two]
          cms_section.subtitle_three = params[:subtitle_three] if params[:subtitle_three]

          if params[:image_one]
            cms_section.build_image_one
            cms_section.image_one.attachment = params[:image_one]
          end
          if params[:image_two]
            cms_section.build_image_two
            cms_section.image_two.attachment = params[:image_two]
          end
          if params[:image_three]
            cms_section.build_image_three
            cms_section.image_three.attachment = params[:image_three]
          end
        end

        if cms_section.save
          render(json: CmsSectionSerializer.render(cms_section))
        else
          render_error_payload(cms_section.errors)
        end
      end

      def update1
        cms_section = current_store.cms_sections.find(params[:id])
        if cms_section.update(permitted_resource_params)
          render(json: CmsSectionSerializer.render(cms_section))
        else
          render_error_payload(cms_section.errors)
        end
      end

      protected

      def model_class
        Spree::CmsSection
      end

      def parent_scope
        current_store.cms_pages.find(params[:page_id]).cms_sections
      end

      def scope
        parent_scope
      end

      def permitted_resource_params
        params.require(:section).permit(:type, :name, :position, :visible, properties: {})
          .tap { |p| p[:type] = "Spree::Cms::Sections::#{p[:type].classify}" }
          .tap { |p| p[:cms_page_id] = params[:page_id] }
      end

      def validate_linked_resource_parms
        valid_resource_types = ['Spree::Taxon', 'Spree::Product', 'Spree::CmsPage']
        if params[:linked_resource_type]
          if valid_resource_types.exclude?(params[:linked_resource_type])
            return false
          end

          if params[:linked_resource_id]
            linked_resource = nil
            case params[:linked_resource_type]
            when 'Spree::Taxon'
              linked_resource = current_store.taxons.find_by(id: params[:linked_resource_id])
            when 'Spree::Product'
              linked_resource = current_store.products.find_by(id: params[:linked_resource_id])
            when 'Spree::CmsPage'
              linked_resource = current_store.cms_pages.find_by(id: params[:linked_resource_id])
            end
            unless linked_resource
              return false
            end
          end
        end

        if params[:link_type_one]
          if valid_resource_types.exclude?(params[:link_type_one])
            return false
          end
        end

        if params[:link_type_two]
          if valid_resource_types.exclude?(params[:link_type_two])
            return false
          end
        end

        if params[:link_type_three]
          if valid_resource_types.exclude?(params[:link_type_three])
            return false
          end
        end

        true
      end

      def render_withtype_error(exception)
        render_error_payload('The specified CMS Section type is not supported', 422)
      end

      def render_unknownattribute_error(exception)
        render_error_payload('The specified attribute not support by the CMS Section type', 422)
      end
    end
  end
end
