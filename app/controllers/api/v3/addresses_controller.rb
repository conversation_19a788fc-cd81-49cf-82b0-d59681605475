# frozen_string_literal: true

module Api
  module V3
    class AddressesController < ResourceController
      before_action -> { params[:page] ||= 1 }, only: :index

      private

      def model_class
        Spree::Address
      end

      def scope_includes
        [:country, :state]
      end

      def permitted_resource_params
        params.require(:address).permit(
          :user_id, :label, :firstname, :lastname,
          :address1, :address2, :city, :state_id, :state_name, :country_id, :zipcode,
          :company, :phone, :alternative_phone
        )
      end
    end
  end
end
