# frozen_string_literal: true

module Api
  module V3
    class RefundReasonsController < ResourceController
      def index
        collection = Spree::RefundReason.all
        render_serialized_payload { serialize_collection(collection) }
      end

      protected

      def model_class
        Spree::RefundReason
      end

      def permitted_resource_params
        params.require(:refund_reason).permit(
          :name,
          :active,
          :mutable
        )
      end
    end
  end
end
