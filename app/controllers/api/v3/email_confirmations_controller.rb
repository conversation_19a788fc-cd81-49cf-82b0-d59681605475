# frozen_string_literal: true

module Api
  module V3
    class EmailConfirmationsController < BaseController
      include JsonWebToken
      include Api::V3::FetchUserTenants

      before_action :normalize_email
      before_action :check_confirmation, only: [:send_code, :resend_code]
      before_action :find_email_confirmation_record, except: :send_code
      before_action :prevent_rapid_code_requests, only: [:send_code, :resend_code]

      def send_code
        admin_user = Spree::AdminUser.find_by(email: @normalized_email)

        if admin_user.present? && admin_user.otp_required_for_login
          response = {
            message: "Verification will be done using Google Authenticator",
            two_factor_type: "google_authenticator",
          }
        else
          @record = Spree::EmailConfirmation.find_or_initialize_by(email: @normalized_email)
          if @record.new_record?
            @record.save!
          else
            @record.regenerate_code!
          end

          deliver_code_email(@record) unless Rails.env.test?

          response = {
            message: "Confirmation code sent to #{@record.email}",
            two_factor_type: "email",
            record: EmailConfirmationSerializer.render_as_hash(@record)
          }
        end

        render json: response, status: :ok
      rescue ActiveRecord::RecordInvalid => e
        render_error_payload(e.record&.errors&.full_messages || e.message, :bad_request)
      end

      def resend_code
        @record.regenerate_code!
        deliver_code_email(@record) unless Rails.env.test?

        render json: {
          message: "Confirmation code resent to #{@record.email}",
          two_factor_type: "email",
          record: EmailConfirmationSerializer.render_as_hash(@record)
        }, status: :ok
      end

      def verify_code
        if Time.current > @record.expires_at
          return render_error_payload('Confirmation Code Expired. Please regenerate code and try again')
        end

        if @record.otp_code != params[:confirmation_code].to_s.strip
          return render_error_payload('Invalid confirmation code')
        end

        @record.update!(confirmed: true)

        case params[:commit]
        when 'sign_up'
          render json: {
            message: 'Email verified successfully.',
            access_token: JsonWebToken.encode(email: @record.email)
          }, status: :ok
        when 'sign_in'
          fetch_user_tenants(@record.email)
        else
          render_error_payload('Invalid action', :bad_request)
        end
      end

      private

      def normalize_email
        return render_error_payload('Please provide an email address', :bad_request) if params[:email].blank?

        @normalized_email = params[:email].to_s.strip.downcase
      end

      def check_confirmation
        case params[:commit]
        when 'sign_up'
          if Spree::Organisation.exists?(admin_email: @normalized_email)
            render_error_payload("Organisation with #{@normalized_email} already exists. Please sign in instead!")
          end
        when 'sign_in'
          unless Spree::AdminUser.exists?(email: @normalized_email)
            render_error_payload("User not found with #{@normalized_email}.", :not_found)
          end
        else
          render_error_payload('Invalid action', :bad_request)
        end
      end

      def find_email_confirmation_record
        @record = Spree::EmailConfirmation.find_by(email: @normalized_email)
        render_error_payload('Email confirmation record not found', :not_found) unless @record
      end

      def prevent_rapid_code_requests
        record = @record || Spree::EmailConfirmation.find_by(email: @normalized_email)
        return if record.blank? || record.new_record?

        if record.updated_at > 1.minute.ago && !Rails.env.test?
          render_error_payload('Please wait a minute before requesting a new code')
        end
      end

      def deliver_code_email(record)
        Spree::EmailConfirmationMailer.send_confirmation_code(record).deliver_now
      end
    end
  end
end
