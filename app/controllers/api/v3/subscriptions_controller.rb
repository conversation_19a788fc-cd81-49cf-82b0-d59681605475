# frozen_string_literal: true

module Api
  module V3
    class SubscriptionsController < ResourceController
      def revise_status
        return render_error_payload(Spree.t('admin.subscription.inactive')) unless resource.is_active

        new_status = resource.status == 'subscribe' ? 'unsubscribe' : 'subscribe'
        if resource.update(status: new_status)
          render_serialized_payload { serialize_resource(resource) }
        else
          render_error_payload('Unable to update subscription status')
        end
      end

      protected

      def model_class
        Spree::Subscription
      end

      def scope
        model_class
      end

      def collection
        @collection ||= scope.ransack(params[:filter]).result.order(created_at: :desc, id: :desc)
      end

      def spree_permitted_attributes
        model_class.column_names.map(&:to_sym)
      end
    end
  end
end
