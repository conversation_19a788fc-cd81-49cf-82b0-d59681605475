# frozen_string_literal: true

module Api
  module V3
    class WalmartSpecsController < ResourceController
      def resource
        nil
      end

      def item_spec
        result = Walmart::CategoryService.new.item_spec(params.dig(:product_type_id), params.dig(:feed_type))
        if result.success?
          render json: result.content, status: :ok
        else
          render json: {message: result.error_message}, status: result.status
        end
      end

      def item_match_spec
        result = Walmart::CategoryService.new.item_match_spec
        if result.success?
          render json: result.content, status: :ok
        else
          render json: {message: result.error_message}, status: result.status
        end
      end

      def product_types
        result = Walmart::CategoryService.new.product_types(params.dig(:id))
        if result.success?
          render json: result.content, status: :ok
        else
          render json: {message: result.error_message}, status: result.status
        end
      end

      def product_type_groups
        result = Walmart::CategoryService.new.product_type_groups(params.dig(:id))
        if result.success?
          render json: result.content, status: :ok
        else
          render json: {message: result.error_message}, status: result.status
        end
      end

      def categories
        include_product_types = params.dig(:include_product_types)
        result = Walmart::CategoryService.new.categories(include_product_types == 'true')
        if result.success?
          render json: result.content, status: :ok
        else
          render json: {message: result.error_message}, status: result.status
        end
      end
    end
  end
end
