# frozen_string_literal: true

module Api
  module V3
    class SectionItemsController < ResourceController
      def index
        render_serialized_payload { serialize_collection(collection) }
      end

      private

      def collection
        @collection ||= Spree::SectionItem.where(category: params[:category], store_id: current_store.id)
      end

      def serialize_collection(collection)
        collection_serializer.render_as_hash(collection)
      end

      def collection_serializer
        ::Api::V3::SectionItemSerializer
      end
    end
  end
end
