# frozen_string_literal: true

module Api
  module V3
    class ImagesController < ResourceController
      private

      def model_class
        Spree::Image
      end

      def parent_scope
        Spree::Product.find(params[:product_id]).variant_images
      end

      def scope
        parent_scope
      end

      def permitted_resource_params
        params.require(:image).permit(:variant_id, :position, :attachment, :alt)
          .tap { |p|
            if p.key?(:variant_id)
              p[:viewable_type] = 'Spree::Variant'
              p[:viewable_id] = p.delete(:variant_id)
            end
          }
      end
    end
  end
end
