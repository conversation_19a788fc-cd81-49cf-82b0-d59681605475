# frozen_string_literal: true

module Api
  module V3
    class OptionTypesController < ResourceController
      protected

      def model_class
        Spree::OptionType
      end

      def parent_scope
        model_class
      end

      def scope_includes
        [option_values: [:translations]]
      end

      def permitted_resource_params
        params.require(:option_type).permit(
          :name,
          :presentation,
          :filterable,
          :position,
          option_values_attributes: [
            :id,
            :name,
            :presentation,
            :position
          ]
        )
      end
    end
  end
end
