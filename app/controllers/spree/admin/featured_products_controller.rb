# frozen_string_literal: true

module Spree
  module Admin
    class FeaturedProductsController < ResourceController
      def batch_create
        first = nil
        params[:listing_ids]&.split(",")&.each do |listing_id|
          listing = Spree::Listing.find(listing_id)
          product = listing.product
          featured = Spree::FeaturedProduct.find_or_create_by(product_id: product.id, store: current_store, listing_id: listing.id)
          if first.nil?
            first = featured
          end
        end

        if first.present?
          flash[:success] = flash_message_for(first, :successfully_created)
        end

        respond_with do |format|
          format.html { redirect_to(location_after_save) }
          format.turbo_stream if create_turbo_stream_enabled?
          format.js   { render(layout: false) }
        end
      rescue StandardError
        respond_with do |format|
          format.html { render(action: :new, status: :unprocessable_entity) }
          format.turbo_stream if create_turbo_stream_enabled?
          format.js { render(layout: false, status: :unprocessable_entity) }
        end
      end
    end
  end
end
