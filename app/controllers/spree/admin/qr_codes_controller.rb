# frozen_string_literal: true

module Spree
  module Admin
    class QrCodesController < Spree::Admin::BaseController
      SUPPORTED_QR_CODES = ["upc_scanner", "image_scanner"].freeze

      def show
        @command = ::Admin::GenerateQrCodeCommand.call(store: current_store, user: current_spree_user, request: request)
        @qr_code = @command.result
        template = SUPPORTED_QR_CODES.include?(request.parameters[:id]) ? request.parameters[:id] : :show
        render(template)
      end
    end
  end
end
