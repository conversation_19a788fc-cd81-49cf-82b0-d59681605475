# frozen_string_literal: true

module Spree
  module Admin
    class SubscriptionsController < ResourceController
      helper "subscription"

      def index
        session[:subscription_query] = {}
        params[:q] ||= {}
        params[:q][:status_in] ||= Spree::Subscription.statuses.keys - ["unsubscribe"]

        permitted_params = params.require(:q).permit(
          :next_delivery_date_between,
          :listing_title_cont, # Ensure this line is present
          status_in: [],
          line_item_id_in: [],
        )

        session[:subscription_query] = session[:subscription_query].merge(permitted_params)

        per_page = params[:per_page].presence || Spree::Backend::Config[:admin_products_per_page]

        @search = Spree::Subscription.all.ransack(session[:subscription_query])
        @subscriptions = @search.result.order(created_at: :desc, id: :desc).page(params[:page]).per(per_page)
      end

      def show
        @subscription = Spree::Subscription.find_by(id: params[:id])
      end

      def edit
        @subscription = Spree::Subscription.find_by(id: params[:id])
      end

      def update
        @subscription = Spree::Subscription.find_by(id: params[:id])
        unless @subscription.is_active
          flash[:error] = Spree.t("admin.subscription.inactive")
          redirect_to admin_subscriptions_path
          return
        end

        if @subscription.update(subscription_params)
          redirect_to(admin_subscriptions_path, notice: Spree.t("admin.subscription.subscription_successfully", message: "updated"))
        else
          render(:edit)
        end
      end

      def revise_status
        @subscription = Subscription.find(params[:id])

        unless @subscription.is_active
          flash[:error] = Spree.t("admin.subscription.inactive")
          redirect_to admin_subscription_path(@subscription)
          return
        end

        new_status = @subscription.status == "subscribe" ? "unsubscribe" : "subscribe"

        if @subscription.update(status: new_status)
          flash[:success] = Spree.t("admin.subscription.subscription_operation_message", message: new_status)
        else
          flash[:error] = "Unable to update subscription."
        end
        redirect_to admin_subscription_path(@subscription)
      end

      private

      def subscription_params
        params.require(:subscription).permit(
          :user_id,
          :line_item_id,
          :interval,
          :next_delivery_date,
          :status,
          :quantity,
          :initial_price,
          :recurring_price,
        )
      end
    end
  end
end
