# frozen_string_literal: true

module Spree
  module Admin
    class WebhooksController < ApplicationController
      skip_before_action :verify_authenticity_token

      def easypost_hook
        unless params["description"] == "tracker.created"
          render(json: { status: 200, message: "success" })
          return
        end
        api_key = Spree::EasypostSetting.last.key
        EasyPost.api_key = api_key
        shipment = EasyPost::Shipment.retrieve(params["result"]["shipment_id"])
        order_shipment = Spree::Shipment.find_by(number: shipment["reference"])

        if order_shipment.present? && order_shipment.tracking.blank?
          original_shipping_amount_paid = order_shipment.cost
          order_shipment.update(
            tracking: shipment["tracking_code"],
            tracking_label: shipment["postage_label"]["label_url"],
            buyer_paid_amount: original_shipping_amount_paid,
            cost: shipment["selected_rate"]["rate"],
          )
          sync_easypost_shipment(shipment, order_shipment)

          render(json: { status: 200, message: "success" })
        else
          log_webhook("(#{params["result"]["shipment_id"]})Order shipment not found", shipment["reference"], "failed")
          render(json: { status: 404, message: "Order shipment not found" })
        end
      rescue StandardError => e
        log_webhook(
          "(#{params["result"]["shipment_id"]})Webhook processing error: #{e.message}",
          params["result"]["shipment_id"],
          "failed",
        )
        render(json: { status: 500, message: "error" })
      end

      def amazon_hook
        if params["detail-type"] == "LISTINGS_ITEM_ISSUES_CHANGE" || params["detail-type"] == "LISTINGS_ITEM_STATUS_CHANGE"
          make_announcements
        else
          report_processing
        end
      end

      def walmart_hook
        oauth_application_id = params.dig(:oauth_application_id)
        source_data = params.dig(:source)
        payload_data = params.dig(:payload)
        event_type = params.dig(:source, :eventType)
        raise "source is empty" if source_data.blank?
        raise "payload is empty" if payload_data.blank?

        unless event_type == "RETURN_INVOICED"
          oauth_application = Spree::OauthApplication.find_by(id: oauth_application_id)
          raise "invalid oauth_application_id: #{oauth_application_id}" if oauth_application.blank?
        end

        case event_type
        when "PO_CREATED" || "PO_LINE_AUTOCANCELLED" || "ORDER_STATUS_UPDATE" || "INTENT_TO_CANCEL"
          purchaged_order_id = params.dig("payload", "purchaseOrderId")
          walmart_order_hook = Spree::WalmartOrderHook.create!(order_id: purchaged_order_id, event_type: event_type, hook_received_at: Time.zone.now, payload: payload_data)
          sku = params.dig("payload", "orderLines")&.first.dig("sku")
          listing = Spree::Listing.find_by(sku: sku)
          store_id  = listing.store.id || Spree::Store.default.id
          Walmart::SyncOrderJob.perform_later(store_id, oauth_application_id, purchaged_order_id)
        when "OFFER_PUBLISHED" || "OFFER_UNPUBLISHED" || "INVENTORY_OOS"
          walmart_order_hook = nil
          walmart_handle_item(oauth_application, source_data, payload_data)
        when "RETURN_INVOICED"
          return_orders = params.dig("payload", "returnOrders") || []
          return_orders.each do |order_data|
            normalized_data = Return::WalmartReturnNormalizer.normalize(order_data)
            walmart_order_hook = Spree::WalmartOrderHook.create!(order_id: normalized_data["purchaseOrderId"], event_type: event_type, hook_received_at: Time.zone.now, payload: payload_data)

            Return::HandleReturnWebhook.new(order_data: normalized_data, event_type: event_type).call
          end

          render json: { status: 200 }
        else
          # code to execute if none of the above conditions are met
          raise "Unknown event type: #{event_type}"
        end
      rescue StandardError => e
        walmart_order_hook.update!(error_details: e.message) if walmart_order_hook.present?
        Rails.logger.info("Walmart walmart_hook error: #{e.message}")
        render(json: { status: 200 })
      end

      def amazon_lwa_rotation
        notification_type = params.dig("Records", 0, "body", "notificationType")

        case notification_type
        when "APPLICATION_OAUTH_CLIENT_SECRET_EXPIRY"
          expiry_time = Time.zone.parse(params.dig(
            "Records",
            0,
            "body",
            "payload",
            "applicationOAuthClientSecretExpiry",
            "clientSecretExpiryTime",
          ))
          client_id = params.dig("Records", 0, "body", "payload", "applicationOAuthClientSecretExpiry", "clientId")
          trigger_time = expiry_time - 10.days

          Apartment.tenant_names.each do |tenant|
            Apartment::Tenant.switch(tenant) do
              sale_channels = Spree::SaleChannel
                .includes(:oauth_application)
                .where(brand: "amazon")
              oauth_application = sale_channels
                .filter_map(&:oauth_application)
                .first

              next if oauth_application.blank?

              store_id = ouath_application.store.id
              # Scheduled the job to be performed exactly at trigger_time
              lwa_credentials = Spree::AmazonLwaCredentials.find_or_create_by(client_id: client_id)
              lwa_credentials.update(refreshed: true)
              ::Amazon::RotateLwaCredentialsJob.set(wait_until: trigger_time).perform_later(store_id, ouath_application.id)

              # If we found an oauth_application, break the loop
              break if oauth_application.present?
            end
          end

        when "APPLICATION_OAUTH_CLIENT_NEW_SECRET"
          new_client_id = params.dig("Records", 0, "body", "payload", "applicationOAuthClientNewSecret", "clientId")
          new_client_secret = params.dig("Records", 0, "body", "payload", "applicationOAuthClientNewSecret", "newClientSecret")
          new_expiry_date = params.dig("Records", 0, "body", "payload", "applicationOAuthClientNewSecret", "newClientSecretExpiryTime")
          parsed_date_time = new_expiry_date.present? ? Time.zone.parse(new_expiry_date) : new_expiry_date
          Spree::AmazonLwaCredentials.first.update(
            client_id: new_client_id,
            client_secret: new_client_secret,
            expiry_date: parsed_date_time,
            refreshed: false,
          )
        end
      end

      def ebay_return_hook
        raw = request.raw_post
        parsed_response = Hash.from_xml(raw)
        oauth_application_id = params.dig(:oauth_application_id)
        store_id = Spree::Store.default.id
        event_type = parsed_response.dig('Envelope', 'Body', 'NotificationEvent', 'NotificationEventName')

        ebay_order_hook = Spree::WalmartOrderHook.create!(payload: parsed_response)
        raise "Missing event type" if event_type.blank?
        return_id = parsed_response.dig('Envelope', 'Body', 'NotificationEvent', "ReturnId")
        raise "Missing return ID in payload" if return_id.blank?

        case event_type
        when "ReturnShipped"
          response = Ebay::ReturnApis::GetReturnOrder.new(store_id, oauth_application_id).get_return(return_id.to_i)
          normalized_data = Return::EbayReturnNormalizer.normalize(response)
          # Create an eBay Return Hook record (reuse WalmartOrderHook table)
          ebay_order_hook.update(order_id: normalized_data["purchaseOrderId"], event_type: event_type, hook_received_at: Time.zone.now)

          Return::HandleReturnWebhook.new(order_data: normalized_data, event_type: event_type).call
        else
          raise "Unknown event type: #{event_type}"
        end

        render json: { status: 200 }
      rescue StandardError => e
        ebay_order_hook.update!(error_details: e.message) if ebay_order_hook.present?
        Rails.logger.info("Walmart walmart_hook error: #{e.message}")
        render(json: { status: 200 })
      end

      private

      def sync_easypost_shipment(shipment, order_shipment)
        # get rate from the response
        rate = shipment.selected_rate
        shipping_method = find_or_create_shipping_method(rate, order_shipment)
        shipping_method.calculator
        # Create the easypost rate
        spree_rate = ::Spree::ShippingRate.new(
          cost: rate.rate,
          easy_post_shipment_id: rate.shipment_id,
          easy_post_rate_id: rate.id,
          shipping_method: shipping_method,
          selected: true,
        )
        order_shipment.selected_shipping_rate.update(selected: false, buyer_selected: true)
        order_shipment.shipping_rates = [spree_rate]
        order_shipment.save!
      end

      def find_or_create_shipping_method(rate, order_shipment)
        method_name = "#{rate.carrier} #{rate.service}"
        ::Spree::ShippingMethod.find_or_create_by(admin_name: method_name) do |r|
          r.name = method_name
          r.store_id = order_shipment.order.store.id
          r.display_on = "both"
          r.code = rate.service
          r.calculator = ::Spree::Calculator::Shipping::EasypostRate.create
          # get shipping category where use_easypost is true
          r.shipping_categories = [order.store.shipping_categories.where(use_easypost: true).first]
        end
      end

      def log_webhook(error_details, shipment_number, state)
        log_data = { shipment_number => error_details }
        Spree::ImportLog.create(
          log_type: 1,
          error_details: log_data,
          state: state,
          store_id: Spree::Store.first.id,
        )
      end

      def make_announcements
        payload = params["detail"]["Payload"]
        sku = payload["Sku"]
        seller_id = payload["SellerId"]
        tenant_name = RedisStore.get("#{sku}-#{seller_id}")
        Apartment::Tenant.switch(tenant_name) do
          listing = Spree::Listing.find_by(sku: sku)
          announcement = Spree::Announcement.find_by(subject: listing)

          if announcement.present?
            announcement.update(title: "Amazon error occurred in listing with sku => #{sku}")
          else
            Spree::Announcement.create(
              subject: listing,
              title: "Amazon error occurred in listing with sku => #{sku}",
            )
          end
        end
      end

      def report_processing
        report_payload = JSON.parse(params.dig("Records", 0, "body"))
        report_id = report_payload.dig("payload", "reportProcessingFinishedNotification", "reportId")
        report_log = Spree::AmazonReportDetail.where(report_id: report_id).first
        payload_status = report_payload.dig("payload", "reportProcessingFinishedNotification", "processingStatus")
        raise StandardError, "Report processing status is #{payload_status}" if payload_status == ("CANCELLED" || "FATAL")

        if report_log.status == "initialized"
          Apartment::Tenant.switch!(report_log.tenant_name)
          store = report_log.store_id
          oauth_application_id = report_log.oauth_application_id
          report_document_id = report_payload.dig("payload", "reportProcessingFinishedNotification", "reportDocumentId")
          raise StandardError, "Report Document Id is not Present" if report_document_id.blank?

          document_details = Amazon::ReportApis::GetReportDocument.new(store, oauth_application_id).get_report_document(report_document_id)
          url = document_details.dig("url")
          raise StandardError, "Url for fetching the listings is missing" if url.blank?

          url = URI.parse(url)
          response = Net::HTTP.get_response(url)
          if response.is_a?(Net::HTTPSuccess)
            data = response.body
            Connector::Amazon::Report.new(data, store).format_listing_response(oauth_application_id)
            report_log.update!(status: "done")
          else
            raise StandardError, "Listing's data not found"
          end
        end
      rescue StandardError => e
        report_log.update!(status: "failed")
        report_log.report_announcement(e.message)
      end

      def walmart_handle_item(oauth_application, source, payload)
        return if oauth_application.blank? || source.blank? || payload.blank?

        event_type = source.dig(:eventType)
        sku = payload.dig(:sku)
        return if event_type.blank? || sku.blank?

        Spree::WalmartItemSyncRecord.find_or_create(
          event_type,
          sku,
          oauth_application.store_id,
          oauth_application.id,
          { source: source, payload: payload },
        )

        if Sidekiq::Queue.new(Walmart::SyncWalmartJob.queue_name).size.zero?
          Walmart::SyncWalmartJob.perform_later(oauth_application.store_id, oauth_application.id, false)
        end
      end
    end
  end
end
