# frozen_string_literal: true

module Spree
  module Admin
    class InvoiceSettingsController < BaseController
      # before_action :load_data, only: :edit
      def edit
        @invoice_setting = Spree::InvoiceSetting.where(store: current_store).first_or_initialize
        if @invoice_setting.country.nil?
          @invoice_setting.country = current_store.default_country
          @invoice_setting.save
        end
      end

      def update
        @invoice_setting = Spree::InvoiceSetting.where(store: current_store).first
        @invoice_setting.update!(invoice_setting_params)
        redirect_to(edit_admin_invoice_settings_path)
      end

      private

      def invoice_setting_params
        params.require(:invoice_setting).permit(
          :name,
          :address,
          :city,
          :zipcode,
          :state_name,
          :state_id,
          :country_id,
          :message,
          :logo,
        )
      end
    end
  end
end
