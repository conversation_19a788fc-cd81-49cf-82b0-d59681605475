# frozen_string_literal: true

module Spree
  module Admin
    class TagsController < ResourceController
      def batch_create
        names = params[:tags].split(/[\r\n,]+/)
        names.each do |name|
          name.strip!
          next if name.blank?

          tag = model_class.new
          tag.name = name
          tag.store_id = current_store.id
          tag.save
        end
        redirect_to(admin_tags_path)
      end

      def batch_delete
        ids = params[:ids].split(",")
        model_class.destroy(ids)
        redirect_to(admin_tags_path)
      end

      def search
        params[:filter] ||= {}
        list = scope.ransack(params[:filter]).result.limit(10)
        render(json: { data: list.map { |t| { id: t.id, attributes: { name: t.name } } } }, status: :ok)
      end

      protected

      def scope
        current_store.tags
      end

      def find_resource
        scope.find(params[:id])
      end

      def collection
        return @collection if @collection.present?

        params[:q] ||= {}
        @search = scope.ransack(params[:q])
        @collection = @search.result.page(params[:page]).per(params[:per_page])
      end
    end
  end
end
