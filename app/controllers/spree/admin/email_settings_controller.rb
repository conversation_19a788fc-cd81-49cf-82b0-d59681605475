# frozen_string_literal: true

module Spree
  module Admin
    class EmailSettingsController < ResourceController
      def edit
        params[:email_setting_type] ||= 'store_email'
        setting_type = params[:email_setting_type] == 'campaign_email' ? :campaign_email_setting : :email_setting
        @email_setting = current_store.public_send(setting_type) || current_store.public_send("build_#{setting_type}")
      end

      def update
        setting_type = email_setting_params[:email_setting_type] == 'campaign_email' ? :campaign_email_setting : :email_setting
        email_setting = current_store.public_send(setting_type)
        if email_setting.update(email_setting_params)
          redirect_to edit_admin_email_settings_path(email_setting_type: email_setting.email_setting_type), notice: 'Email setting updated successfully.'
        else
          flash[:error] = email_setting.errors.full_messages.join(",\n ")
          redirect_to edit_admin_email_settings_path(email_setting_type: email_setting.email_setting_type)
        end
      end

      private

      def email_setting_params
        params.require(:email_setting).permit(
          :email_from,
          :email_bcc,
          :intercept_email,
          :mail_delivery,
          :store_id,
          :api_key,
          :email_setting_type,
          smtp: [:domain, :address, :port, :secure_connection_type, :authentication, :user_name, :password],
        )
      end
    end
  end
end
