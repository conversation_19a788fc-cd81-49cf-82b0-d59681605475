# frozen_string_literal: true

module Spree
  module Admin
    class MatchItsController < ResourceController
      before_action :load_product
      belongs_to "spree/product", find_by: :slug

      def batch_create
        first = nil
        params[:listing_ids]&.split(",").each do |listing_id|
          listing = Spree::Listing.find(listing_id)
          product_id = listing.product.id
          match_it = Spree::MatchIt.where(matched_product_id: product_id, product_id: @product.id, listing_id: listing_id).first_or_create
          if match_it.previous_changes.key?('id')
            record_activity_log(match_it)
          end
          if first.nil?
            first = match_it
          end
        end
        flash[:success] = flash_message_for(first, :successfully_created)
        respond_with do |format|
          format.html { redirect_to(location_after_save) }
          format.turbo_stream if create_turbo_stream_enabled?
          format.js   { render(layout: false) }
        end
      rescue
        respond_with do |format|
          format.html { render(action: :new, status: :unprocessable_entity) }
          format.js { render(layout: false, status: :unprocessable_entity) }
        end
      end

      private

      def record_activity_log(match_it)
        ::Spree::ActivityLog.create!(
          loggable: match_it,
          user_id: spree_current_user.id,
          action: ::Spree::ActivityLogActions.get(:match_it, :add),
          role: spree_current_user.spree_roles.first&.name,
          date: Time.current,
          email: spree_current_user.email,
          action_place: admin_product_match_its_path(@product.slug),
          action_name: @product.name,
          product_id: @product.id
        )
      end

      def load_product
        @product = scope.friendly.find(params[:product_id])
      end

      def scope
        current_store.products
      end
    end
  end
end
