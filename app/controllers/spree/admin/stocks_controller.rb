# frozen_string_literal: true

module Spree
  module Admin
    class StocksController < Spree::Admin::BaseController
      def index
        @line_items = Spree::LineItem.includes(:order)
        @line_items = @line_items.includes(variant: [:product, stock_items: [:stock_location, :stock_location_section]])
        @line_items = @line_items.where(spree_orders: { number: params[:order_number] })
        @groups = @line_items.group_by(&:variant)
      end

      def print
        @line_items = Spree::LineItem.includes(:order)
        @line_items = @line_items.includes(variant: [:product, stock_items: [:stock_location, :stock_location_section]])
        @line_items = @line_items.where(spree_orders: { number: params[:order_number] })
        @groups = @line_items.group_by(&:variant)
        render(layout: "layouts/print")
      end
    end
  end
end
