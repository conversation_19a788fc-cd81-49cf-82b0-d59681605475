# frozen_string_literal: true

module Spree
  module Admin
    class GalleryItemsController < ResourceController
      before_action :find_order_object, only: [:get_order_object, :remove_order_image]
      before_action :find_listing_object, only: [:get_listing_object, :remove_listing_image]
      include ::Spree::BaseHelper

      def index
        @permissions = current_user_permissions.find_permission
        @gallery_items = GalleryItem.where(store_id: current_store.id).order(created_at: :desc).page(params[:page])
          .per(params[:per_page] || Spree::Backend::Config[:admin_products_per_page])
        db_name = ActiveRecord::Base.connection.current_database
        query = ActiveRecord::Base.sanitize_sql(["SELECT pg_database_size(?) AS size", db_name])
        db_size = ActiveRecord::Base.connection.execute(query).first["size"]
        attachments_count = ActiveStorage::Blob.joins(:attachments).count
        attachments_size = ActiveStorage::Blob.joins(:attachments).sum(:byte_size)
        storage_root_path = Rails.application.config.active_storage.service_configurations["local"]["root"]
        storage_root_size = folder_size(storage_root_path)
        @space_statistics_info = []
        @space_statistics_info.push({
          db_name: db_name,
          db_size: db_size,
          attachments_count: attachments_count,
          attachments_size: attachments_size,
          storage_root_path: storage_root_path,
          storage_root_size: storage_root_size,
        })
      end

      def new
        @gallery_item = GalleryItem.new
      end

      def create
        params[:gallery_item][:file].each do |f|
          gallery_item = current_store.gallery_items.new(gallery_item_params)
          gallery_item.file.attach(f)
          gallery_item.save
        end

        redirect_to(admin_gallery_items_path)
      rescue
        redirect_back(
          fallback_location: new_admin_gallery_item_path,
          flash: { error: "Cannot create Gallery item make sure you have attached images" },
        )
      end

      def list_product
        @objects = current_store.products
        @objects = @objects.page(params[:page]).per(13)
      end

      def list_order
        @objects = current_store.orders
        @objects = @objects.page(params[:page]).per(13)
      end

      def list_listing
        @objects = current_store.listing
        @objects = @objects.page(params[:page]).per(13)
      end

      def get_product_object # rubocop:disable Naming/AccessorMethodName
        @object = current_store.products.find_by(id: params[:object_id])
      end

      def get_order_object # rubocop:disable Naming/AccessorMethodName
      end

      def get_listing_object # rubocop:disable Naming/AccessorMethodName
      end

      def remove_order_image
        image = @object.images.find_by(id: params[:image_id])

        if image&.purge
          render(json: { status: "success", message: "Image deleted successfully" })
        else
          render(json: { status: "error", error: "Failed to delete image" }, status: :unprocessable_entity)
        end
      end

      def remove_listing_image
        image = @object.image_files.find_by(id: params[:image_id])

        if image.attachments.each(&:purge)
          render(json: { status: "success", message: "Image deleted successfully" })
        else
          render(json: { status: "error", error: "Failed to delete image" }, status: :unprocessable_entity)
        end
      end

      def search
        scope = params[:scope]
        query = params[:query]

        if scope.present?
          case scope
          when "Product"
            @object = Product.join_translation_table(::Spree::Product).where(
              "#{::Spree::Product.translation_table_alias}.name ILIKE ?",
              "%#{query}%",
            )
          when "Order"
            @object = Order.where("number ILIKE ?", "%#{query}")
          when "Listing"
            @object = Listing.where("title ILIKE ?", "%#{query}%")
          end
          @objects = @object&.page(params[:page])&.per(13)
        else
          redirect_to(admin_gallery_items_path)
          nil
        end
      end

      private

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end

      def gallery_item_params
        params.require(:gallery_item).permit(:title, :uploaded_by)
      end

      def find_order_object
        @object = current_store.orders.find_by(number: params[:object_number])
      end

      def find_listing_object
        @object = current_store.listing.find_by(id: params[:object_id])
      end
    end
  end
end
