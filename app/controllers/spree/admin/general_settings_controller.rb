# frozen_string_literal: true

module Spree
  module Admin
    class GeneralSettingsController < Spree::Admin::BaseController
      include Spree::Backend::Callbacks

      before_action :update_currency_settings, only: :update

      def edit
        @permissions = current_user_permissions.find_permission
        @preferences_security = []
      end

      def update
        changes_made = false

        params.each do |name, value|
          next unless Spree::Config.has_preference?(name)

          if Spree::Config[name] != value
            Spree::Config[name] = value
            changes_made = true
          end
        end

        if changes_made
          flash[:success] = Spree.t(:successfully_updated, resource: Spree.t(:general_settings))
        else
          flash[:notice] = Spree.t(:no_update)
        end

        redirect_to(spree.edit_admin_general_settings_path)
      end

      def clear_cache
        Rails.cache.clear
        invoke_callbacks(:clear_cache, :after)
        head(:no_content)
      end

      private

      def update_currency_settings
        params.each do |name, value|
          next unless Spree::Config.has_preference?(name) && name.eql?("supported_currencies")

          value = update_value(value)
          Spree::Config[name] = value
        end
      end

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end

      def update_value(value)
        value.split(",")
          .map { |curr| ::Money::Currency.find(curr.strip).try(:iso_code) }
          .push(Spree::Config[:currency])
          .uniq
          .compact
          .join(",")
      end
    end
  end
end
