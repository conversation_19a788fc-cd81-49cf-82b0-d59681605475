# frozen_string_literal: true

module Spree
  module Admin
    class MetabaseSettingsController < ResourceController
      def new
        @organisations = Spree::Organisation.where(name: Apartment::Tenant.current)
      end

      def edit
        @organisations = Spree::Organisation.where(name: Apartment::Tenant.current)
      end

      def create
        @metabase_setting = Spree::MetabaseSetting.new(metabase_setting_params)
        if @metabase_setting.save!
          redirect_to(
            admin_metabase_settings_path,
            flash: { success: "Metabase setting created successfully" },
          )
        else
          redirect_back(
            fallback_location: edit_admin_metabase_setting_path,
            flash: { error: @metabase_setting.errors.full_messages.join(", ") },
          )
        end
      end

      def update
        if @metabase_setting.update(metabase_setting_params)
          redirect_to(
            admin_metabase_settings_path,
            flash: { success: "Metabase setting updated successfully" },
          )
        else
          redirect_back(
            fallback_location: edit_admin_metabase_setting_path,
            flash: { error: @metabase_setting.errors.full_messages.join(", ") },
          )
        end
      end

      def destroy
        if @metabase_setting.destroy!
          flash[:success] = flash_message_for(@metabase_setting, :successfully_removed)
        else
          flash[:error] = @metabase_setting.errors.full_messages.join(", ")
        end
        respond_with(@metabase_setting) do |format|
          format.html { redirect_to(location_after_destroy) }
          format.js   { render_js_for_destroy }
        end
      end

      private

      def collection
        current_tenant = ::Spree::Organisation.where(name: Apartment::Tenant.current)
        @collection ||= Spree::MetabaseSetting.where(organisation: current_tenant)
      end

      def metabase_setting_params
        params.require(:metabase_setting).permit(:organisation_id, :store_id, :metabase_site_url, :metabase_secret_key)
      end
    end
  end
end
