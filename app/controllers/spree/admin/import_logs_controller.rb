# frozen_string_literal: true

module Spree
  module Admin
    class ImportLogsController < ResourceController
      def index
        @import_logs = ImportLog.where(store_id: current_store.id, log_type: params[:log_type]).order(created_at: :desc).page(params[:page])
          .per(params[:per_page] || Spree::Backend::Config[:admin_products_per_page])
      end

      def import_sample
        respond_to do |format|
          format.xlsx do
            send_data(
              File.read(Rails.public_path.join("sample/sample.xlsx").to_s),
              type: "application/xlsx; header=present",
              disposition: "attachment",
              filename: "sample.xlsx",
            )
          end
        end
      end

      def clear
        ImportLog.where(store_id: current_store.id).delete_all
        redirect_to(admin_import_logs_path)
      end
    end
  end
end
