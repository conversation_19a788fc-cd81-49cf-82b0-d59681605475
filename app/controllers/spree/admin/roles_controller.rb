# frozen_string_literal: true

module Spree
  module Admin
    class RolesController < ResourceController
      layout "spree/layouts/admin"
      before_action :find_object, only: [:edit, :destroy]
      before_action :check_permission_for_role, only: [:new, :edit]

      def index
        @roles = Spree::Role.all
        @roles = @roles.page(params[:page]).per(15)
      end

      def new
      end

      def edit
        respond_with(@object) do |format|
          format.html { render(layout: !request.xhr?) }
          format.js   { render(layout: false) } if request.xhr?
        end
      end

      def create
        invoke_callbacks(:create, :before)
        @object.attributes = permitted_resource_params
        role_name_unique = Spree::Role.where(name: @object.name)&.none?

        if role_name_unique && @object.save
          create_permissions_for_role(@object, permission_params) if params[:role][:permissions].present?
          invoke_callbacks(:create, :after)
          flash[:success] = flash_message_for(@object, :successfully_created)
          respond_with(@object) do |format|
            format.html { redirect_to(location_after_save) }
            # format.turbo_stream if turbo_enabled?
            format.js   { render(layout: false) }
          end
        else
          unless role_name_unique
            @object.errors.add(:name, :taken)
          end
          invoke_callbacks(:create, :fails)
          respond_with(@object) do |format|
            format.html { render(action: :new, status: :unprocessable_entity) }
            format.js { render(layout: false, status: :unprocessable_entity) }
          end
        end
      end

      def update
        invoke_callbacks(:update, :before)
        if @object.update(permitted_resource_params)
          update_permissions_for_role(@object, permission_params || {})
          set_current_store
          invoke_callbacks(:update, :after)
          respond_with(@object) do |format|
            format.html do
              flash[:success] = flash_message_for(@object, :successfully_updated)
              redirect_to(location_after_save)
            end
            format.js { render(layout: false) }
          end
        else
          invoke_callbacks(:update, :fails)
          respond_with(@object) do |format|
            format.html { render(action: :edit, status: :unprocessable_entity) }
            format.js { render(layout: false, status: :unprocessable_entity) }
          end
        end
      end

      def destroy
        if spree_current_user&.spree_roles&.first&.permission&.manage_roles
        else
          message = "You are not authorized to do this action"
          flash[:error] = message
          respond_with(@object) do |format|
            format.html { redirect_to(collection_url) }
            format.js { render_js_for_destroy }
          end
          return
        end
        invoke_callbacks(:destroy, :before)
        if @object.destroy
          invoke_callbacks(:destroy, :after)
          flash[:success] = flash_message_for(@object, :successfully_removed)
        else
          invoke_callbacks(:destroy, :fails)
          flash[:error] = @object.errors.full_messages.join(", ")
        end

        respond_with(@object) do |format|
          format.html { redirect_to(location_after_destroy) }
          format.js   { render_js_for_destroy }
        end
      end

      private

      def permitted_resource_params
        params.require(:role).permit(:name)
      end

      def permission_params
        all_permissions = [
          :view_finance_and_sales,
          :create_and_edit_product,
          :allow_product_delete,
          :view_product_cost_price,
          :manage_stock,
          :create_and_edit_listing,
          :manage_sale_channel,
          :create_and_edit_order,
          :create_and_edit_users,
          :send_email,
          :delete_users,
          :view_activity_log,
          :sale_and_finance_report,
          :product_and_inventory_report,
          :create_and_edit_store,
          :publish_unpublish_store,
          :delete_store,
          :manage_store_payment_configuration,
          :manage_webhooks,
          :manage_roles,
          :show_dashboard,
          :show_inventory,
          :manage_listings,
          :manage_orders,
          :manage_users,
          :access_reports,
          :store_settings,
          :general_settings,
          :manage_blogs,
          :view_blogs,
          :create_and_edit_blogs,
          :delete_blogs,
        ]
        permissions = params.dig(:role, :permissions) || ActionController::Parameters.new
        permissions = permissions.permit(*all_permissions)
        all_permissions.each { |permission| permissions[permission] = false unless permissions.key?(permission) }
        permissions
      end

      def find_object
        @object = Spree::Role.find_by(id: params[:id])
      end

      def create_permissions_for_role(role, permission_params)
        role.create_permission(permission_params)
      end

      def update_permissions_for_role(role, permission_params)
        if role.permission.present?
          role.permission.update(permission_params)
        else
          role.create_permission(permission_params)
        end
      end

      def check_permission_for_role
        if spree_current_user&.spree_roles&.first&.permission&.manage_roles&.present?
        else
          message = "You are not authorized to do this action"
          flash[:error] = message
          redirect_to(admin_roles_path)
          return # rubocop:disable Style/RedundantReturn
        end
      end
    end
  end
end
