# frozen_string_literal: true

module Spree
  module Api
    module V2
      module DataFeeds
        class MetaController < ::Spree::Api::V2::BaseController
          def rss_feed
            send_data(data_feeds_meta_rss_service.value[:file], filename: "products.xml", type: "text/xml")
          end

          private

          def settings
            @settings ||= Spree::DataFeed::Meta.find_by!(store: current_store, slug: params[:slug], active: true)
          end

          def data_feeds_meta_rss_service
            # Spree::Dependencies.data_feeds_meta_rss_service.constantize.new.call(settings)
            "Spree::DataFeeds::Meta::Rss".constantize.new.call(settings)
          end
        end
      end
    end
  end
end
