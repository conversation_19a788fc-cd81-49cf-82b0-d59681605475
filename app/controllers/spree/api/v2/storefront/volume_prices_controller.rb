# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class VolumePricesController < ::Spree::Api::V2::BaseController
          def fetch_volume_pricing
            table = Spree::VolumePrice.arel_table
            variant = Spree::Variant.find_by(id: params["volume_pricing"]["variant_id"])
            listing_id = params["volume_pricing"]["listing_id"]
            volume_prices = Spree::VolumePrice.where(
              table[:variant_id].eq(variant&.id)
                .and(table[:listing_id].in(listing_id)),
            ).order(position: :asc)
            render(json: volume_prices, each_serializer: Spree::Api::V2::Storefront::VolumePriceSerializer)
          end
        end
      end
    end
  end
end
