# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class ImageUploadController < ::Spree::Api::V2::ResourceController
          include ::Spree::Image::Configuration

          # skip_before_action :verify_authenticity_token
          before_action :authenticate, only: :upload
          before_action :set_resource

          def upload
            payload = request.parameters.except(:controller, :action)
            payload = payload.merge(type: "close_modal")

            QrCodeChannel.broadcast_to(broadcasting, payload)
          end

          def successful_upload
            if params[:variant_id].present?
              variant = Spree::Variant.find_by(id: params[:variant_id])
              option_text = variant&.options_text.presence || "master"
              custom_filename = "#{option_text}_#{variant&.id}" || ""
            end
            payload = request.parameters.except(:controller, :action)
            payload = payload.merge(type: "refresh")

            @images = []

            params.each do |param_name, file|
              next unless param_name.start_with?("file_") && file.respond_to?(:tempfile)

              filename = params[:variant_id].present? ? custom_filename + "_" + file.original_filename : file.original_filename
              @images << { io: file, filename: filename }
            end

            token = params[:user_token]
            user = Spree::User.find_by(user_token: token)

            case @resource.class.name
            when "Spree::Product"
              @images.each do |image_attributes|
                image = @resource.images.create!(attachment: image_attributes)
                Spree::ImageUpload.create!(imageable: @resource, image_id: image.attachment.id, user: user) if user.present?
                @resource.update(uploaded_by: user&.email) if user&.present?
              end unless @images.empty?
            when "Spree::Listing"
              unless @images.empty?
                @resource.image_files.attach(@images)
                image_attachment = @resource.image_files.last
                Spree::ImageUpload.create!(imageable: @resource, image_id: image_attachment.id, user: user) if user.present?
                @resource.update(uploaded_by: user&.email) if user&.present?
              end
            when "Spree::Order"
              @images.each do |image|
                @resource.images.attach(image)
                attachment = @resource.images.attachments.last
                Spree::ImageUpload.create!(imageable: @resource, image_id: attachment.id, user: user) if user.present?
                @resource.update(uploaded_by: user&.email) if user&.present?
              end unless @images.empty?
            end

            if @images.present?
              QrCodeChannel.broadcast_to(broadcasting, payload)
              render(json: @images, status: :created)
            else
              render(json: { errors: ["No valid files were provided."] }, status: :unprocessable_entity)
            end
          end

          def remove_image
            image = @resource.images.find_by(id: params[:image_id])

            if image&.purge
              render(json: { message: "Image deleted successfully" }, status: :ok)
            else
              render(json: { error: "Failed to delete image" }, status: :unprocessable_entity)
            end
          end

          private

          def broadcasting
            @broadcasting ||= authenticate_spree_user
          end

          def authenticate
            unless authenticate_spree_user
              render(json: { error: "User token missing or incorrect!" }, status: :unprocessable_entity)
            end
          end

          def authenticate_spree_user
            token = params[:user_token]
            return if token.blank?

            @user = Spree::User.find_by(user_token: token)
            return if @user.blank?

            [Apartment::Tenant.current, @user]
          end

          def set_resource
            resource_class = case params[:resource]
            when "product"
              Spree::Product
            when "listing"
              Spree::Listing
            when "order"
              Spree::Order
            end
            @resource = resource_class.find(params[:resource_id])
          end
        end
      end
    end
  end
end
