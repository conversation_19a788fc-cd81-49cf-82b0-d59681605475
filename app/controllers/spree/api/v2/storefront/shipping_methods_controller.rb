# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class ShippingMethodsController < ::Spree::Api::V2::BaseController
          include Spree::Api::V2::Storefront::OrderConcern

          def list_local_pickup_shipping_methods
            matched_shipping_method = ShippingMethod::ShippingMethodProcessor.new(spree_current_order).local_pickup_shipping_methods
            render_serialized_payload { serialize_shipping_methods(matched_shipping_method) }
          end

          def serialize_shipping_methods(local_pickup_shipping_methods)
            ::Spree::Api::V2::Storefront::ShippingMethodSerializer.new(
              local_pickup_shipping_methods,
            ).serializable_hash
          end
        end
      end
    end
  end
end
