# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class CampaignWebhooksController < ::Spree::Api::V2::BaseController
          def webhook
            Rails.logger.info("=====\n params: #{params} \n=====")

            webhook_data = params['campaign_webhook']
            return head :ok unless webhook_data

            event_type = webhook_data['event']
            return head :ok unless event_type

            Rails.logger.info("=====\n message_id: #{webhook_data['Message-Id']},\n event_type: #{event_type} \n=====")

            campaign_subscriber = Spree::CampaignSubscriber.find_by(message_id: webhook_data['Message-Id'])
            return head :ok unless campaign_subscriber

            campaign = campaign_subscriber.campaign
            return head :ok unless campaign

            subscriber = campaign_subscriber.subscriber
            return head :ok unless subscriber

            case event_type
            when "delivered"
              unless campaign_subscriber.delivered
                campaign_subscriber.update_columns(delivered: true)
                campaign.increment!(:delivered_count)
              end
            when "click"
              unless campaign_subscriber.clicked
                campaign_subscriber.update_columns(clicked: true)
                campaign.increment!(:clicked_count)
              end
            when "open"
              unless campaign_subscriber.opened
                campaign_subscriber.update_columns(opened: true)
                campaign.increment!(:opened_count)
              end
            when "unsubscribe"
              unless campaign_subscriber.unsubscribed
                campaign_subscriber.update_columns(unsubscribed: true)
                subscriber.update_columns(campaign_subscribed: false)
                campaign.increment!(:unsubscribed_count)
              end
            when "resubscribe"
              if campaign_subscriber.unsubscribed
                campaign_subscriber.update_columns(unsubscribed: false)
                subscriber.update_columns(campaign_subscribed: true)
                campaign.decrement!(:unsubscribed_count)
              end
            else
              Rails.logger.warn("Unknown event type: #{event_type}")
            end

            head :ok
          end
        end
      end
    end
  end
end
