# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        module Account
          class ReturnAuthorizationsController < ::Spree::Api::V2::ResourceController
            before_action :require_spree_current_user

            before_action :set_order, only: [:index, :show, :new, :create]

            def index
              collection = Spree::ReturnAuthorization.where(order_id: @current_order&.id)
              render_serialized_payload do
                serialize_collection(collection)
              end
            end

            def show
              collection = Spree::ReturnAuthorization.find_by(id: params[:id])
              render_serialized_payload do
                serialize_collection(collection)
              end
            end

            def new
              @return_authorization = @current_order.return_authorizations.build
              load_return_items
              load_reimbursement_types # reason
              response_hash = build_response(@form_return_items)
              render(json: response_hash)
            end

            def create
              @return_authorization = @current_order.return_authorizations.create!(return_authorization_params)
              # HotFix:- Getting some issue with image base 64, So used attach method
              @return_authorization.return_items.each do |return_item|
                params[:return_items_attributes][return_item.inventory_unit.id.to_s][:images].each do |image|
                  return_item.images.attach(data: image)
                end
              end
              render_serialized_payload do
                serialize_collection(@return_authorization)
              end
            end

            private

            def serialize_collection(collection)
              collection_serializer.new(collection).serializable_hash
            end

            def collection_serializer
              Spree::Api::V2::Storefront::ReturnAuthorizationSerializer
            end

            def return_authorization_params
              params.require(:return_authorization).permit(
                :order_id,
                :memo,
                :stock_location_id,
                :return_authorization_reason_id,
                return_items_attributes: [
                  :inventory_unit_id,
                  :reason,
                  :return_authorization_reason_id,
                  :return_quantity,
                ],
              ).tap do |wl|
                wl[:stock_location_id] = @current_order.shipments.take.stock_location_id
                wl[:return_authorization_reason_id] = find_or_create_authorization_reason
              end
            end

            def find_or_create_authorization_reason
              Spree::ReturnAuthorizationReason.find_or_create_by!(name: "Customer request", active: true).id
            end

            def set_order
              @current_order = Spree::Order.find_by(number: params[:order_number])
            end

            def load_return_items
              all_inventory_units = @current_order.inventory_units
              associated_inventory_units = @return_authorization.return_items.map(&:inventory_unit)
              unassociated_inventory_units = all_inventory_units - associated_inventory_units
              new_return_items = unassociated_inventory_units.map do |new_unit|
                @return_authorization.return_items.build(inventory_unit: new_unit).tap(&:set_default_pre_tax_amount)
              end
              @form_return_items = (@return_authorization.return_items + new_return_items).sort_by(&:inventory_unit_id).uniq
            end

            def load_reimbursement_types
              @reimbursement_types = Spree::ReimbursementType.all.active
            end

            def build_response(form_return_items)
              response_arr_hash = []
              hash_key = [
                :id,
                :return_authorization_id,
                :inventory_unit_id,
                :pre_tax_amount,
                :editable,
                :return_quantity,
                :quantity,
                :variant_name,
              ]
              form_return_items.each do |f|
                response_hash = hash_key.index_with { |v| custom_attribute(v.to_s, f) }
                response_arr_hash << response_hash
              end
              response_arr_hash
            end

            def custom_attribute(key, return_item)
              if key == "editable"
                allow_return_item_changes = !return_item.return_authorization.customer_returned_items?
                return_item.inventory_unit.shipped? && allow_return_item_changes && return_item.reimbursement.nil?
              elsif key == "return_quantity"
                return_item.return_quantity
              elsif key == "quantity"
                return_item.inventory_unit.quantity
              elsif key == "variant_name"
                return_item.inventory_unit.variant.name
              else
                return_item.public_send(key)
              end
            end
          end
        end
      end
    end
  end
end
