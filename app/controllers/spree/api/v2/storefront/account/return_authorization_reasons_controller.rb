# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        module Account
          class ReturnAuthorizationReasonsController < ::Spree::Api::V2::ResourceController
            def index
              collection = Spree::ReturnAuthorizationReason.all.active
              render(json: collection.to_json)
            end
          end
        end
      end
    end
  end
end
