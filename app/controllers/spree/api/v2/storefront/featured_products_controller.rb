# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Storefront
        class FeaturedProductsController < ::Spree::Api::V2::ResourceController
          include ::Spree::Admin::ProductsHelper

          protected

          def collection
            @collection ||= Spree::FeaturedProduct.for_store(current_store)
          end

          def collection_serializer
            Spree::V2::Storefront::FeaturedProductSerializer
          end
        end
      end
    end
  end
end
