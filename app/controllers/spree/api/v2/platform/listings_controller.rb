# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Platform
        class ListingsController < ::Spree::Api::V2::ResourceController
          def collection
            listings = Spree::Listing.joins(:sale_channel).where(store_id: current_store.id, sale_channel: {brand: "storefront"}).Active
            es_listing = AxelSpree::Elasticsearch::Listing.new
            query_params = {
              title: params.dig(:filter, :name_i_cont) || params.dig(:filter, :name_or_master_sku_cont),
            }
            es_listing.backend(query_params)
            listings.use_elasticsearch(es_listing, listings)
            listings
          end
          
          def index
            render_serialized_payload do
              Spree::Api::V2::Platform::ListingSerializer.new(collection).serializable_hash
            end
          end
        end
      end
    end
  end
end
