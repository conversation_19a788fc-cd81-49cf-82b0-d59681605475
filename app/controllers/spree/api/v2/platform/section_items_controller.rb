# frozen_string_literal: true

module Spree
  module Api
    module V2
      module Platform
        class SectionItemsController < ::Spree::Api::V2::ResourceController
          def index
            render_serialized_payload do
              serialize_collection(collection)
            end
          end

          private

          def collection
            @collection ||= Spree::SectionItem.where(category: params[:category], store_id: current_store.id)
          end

          def serialize_collection(collection)
            collection_serializer.new(collection).serializable_hash
          end

          def collection_serializer
            ::Spree::Api::V2::Platform::SectionItemSerializer
          end
        end
      end
    end
  end
end
