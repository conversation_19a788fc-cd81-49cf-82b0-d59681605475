# frozen_string_literal: true

module AuthorizeUser
  def current_user_permissions
    @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
  end

  def permissions
    @permissions ||= current_user_permissions.find_permission
  end

  def authorized_to_view_blogs?
    unless permissions&.view_blogs
      return render json: { errors: "You are not authorised to perform this action." }, status: :unprocessable_entity
    end
  end

  def authorized_to_create_edit_blogs?
    unless permissions&.create_and_edit_blogs
      return render json: { errors: "You are not authorised to perform this action." }, status: :unprocessable_entity
    end
  end

  def authorized_to_delete_blogs?
    unless permissions&.delete_blogs
      return render json: { errors: "You are not authorised to perform this action." }, status: :unprocessable_entity
    end
  end
end
