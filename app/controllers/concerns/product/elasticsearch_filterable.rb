# frozen_string_literal: true

module Product
  module ElasticsearchFilterable
    extend ActiveSupport::Concern

    private

    def elasticsearch_query?
      [:name_cont, :variants_including_master_upc_cont, :variants_including_master_sku_cont].any? do |key|
        params.dig(:filter, key).present?
      end
    end

    def build_sort_order
      # params.dig(:sort) can be "name asc" or "name desc" or "name" or "-name"

      field, sort_direction = params.dig(:sort).to_s.split(" ")
      if sort_direction.blank? && field.present?
        sort_direction = field.start_with?("-") ? "desc" : "asc"
      end

      field = field.gsub("-", "") if field.present? && field.start_with?("-")

      field ||= "keyword_name"
      sort_direction = ["asc", "desc"].include?(sort_direction) ? sort_direction : "asc"

      field = "keyword_#{field}" if field == "name"

      order = AxelSpree::Elasticsearch::Search::Order.new(field, sort_direction).to_hash
    end

    def build_query_params
      return @query_params if @query_params.present?

      with_deleted = false
      with_discontinued = false
      with_deleted = params.dig(:filter, :deleted_at_null) == "0" if params.key?(:filter) && params[:filter].key?(:deleted_at_null)
      with_discontinued = params.dig(:filter, :not_discontinued) != "1" if params.key?(:filter) && params[:filter].key?(:not_discontinued)

      taxons = []

      taxons_id_in = params.dig(:filter, :taxons_id_in)
      if taxons_id_in.present?
        taxons_id_in.each do |taxon_id|
          if taxon_id.present?
            taxons << taxon_id
          end
        end
      end

      @query_params = {
        name: params.dig(:filter, :name_cont),
        upc: params.dig(:filter, :variants_including_master_upc_cont),
        sku: params.dig(:filter, :variants_including_master_sku_cont),
        taxons: taxons,
        with_deleted: with_deleted,
        with_discontinued: with_discontinued,
      }

      # delete the params.dig(:filter, :name_cont)
      params[:filter].delete(:name_cont)

      @query_params
    end

    def update_filter_params
      params[:filter] ||= {}
    end

    def apply_elasticsearch_filtering
      return unless elasticsearch_query?

      @collection = collection

      order = build_sort_order
      query_params = build_query_params

      @base_collection = scope.ransack(params[:filter]).result

      mode = params[:mode] || "standard"
      es_product = AxelSpree::Elasticsearch::Product.new
      es_product.backend(query_params).order(order).mode(mode)
      @collection.use_elasticsearch(es_product, @base_collection)
    end
  end
end
