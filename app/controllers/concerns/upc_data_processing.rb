# frozen_string_literal: true

# Created a concern as there is common code in scanned items and products controller
module UpcDataProcessing
  extend ActiveSupport::Concern

  private

  def process_upc_data(product_params, common_add_stock_flow = nil)
    @scanned_item = Spree::ScannedItem.find_by(id: product_params[:scanned_item_id])
    if product_params[:upc_item_db_data].blank?
      raise "upc item data can't be blank"
    end

    upc_data = JSON.parse(product_params[:upc_item_db_data])
    if upc_data["title"].blank?
      raise "upc item title can't be blank"
    end

    product_keys = [
      { source: "title", target: "name" },
      { source: "lbs", float: true },
      { source: "oz", float: true },
      { source: "height", float: true },
      { source: "width", float: true },
      { source: "length", target: "depth", float: true },
      { source: "description" },
    ]
    upc_product_params = product_params.to_h
    upc_product_params[:upc] = @scanned_item.upc
    product_keys.each do |key|
      key_sym = (key[:target] || key[:source]).to_sym
      source_key = key[:source]
      if upc_data[source_key].presence
        if key[:float] && upc_data[source_key].is_a?(String)
          source_matches = upc_data[source_key].match(/[.\d]+/)
          upc_product_params[key_sym] = source_matches[0].to_f unless source_matches.nil?
        else
          upc_product_params[key_sym] = upc_data[source_key]
        end
      end
    end
    product = current_store.products.new(upc_product_params)
    if upc_data["categories"].present?
      taxonomy = current_store.taxonomies.where("lower(name) = 'categories'").first_or_create!(name: "Categories")
      parent_taxon = taxonomy.root
      final_taxons = nil
      upc_data["categories"].split(">").each do |taxon| # "Paintings>Nature>Seascape"
        taxon = taxon.strip
        parent_taxon = parent_taxon.children.where("lower(name) = ?", taxon.downcase).first_or_create!(name: taxon)
        final_taxons = parent_taxon
      end
      product.taxons << final_taxons unless product.taxons.find_by(name: final_taxons.name)
    end
    product.stores << current_store
    if product.save!
      ::Spree::ActivityLog.create!(
        loggable: product,
        user_id: spree_current_user.id,
        action: ::Spree::ActivityLogActions.get(:product, resolve_product_action),
        role: spree_current_user.spree_roles.first&.name,
        date: Time.current,
        email: spree_current_user.email,
        action_place: resolve_product_action_place(product),
        action_name: product.name,
        product_id: product.id
      )

      upc_data.keys.select { |key| product_keys.pluck(:source).index(key).nil? }.each_with_index do |key, idx|
        val = upc_data[key]
        next unless val.presence

        property = Spree::Property.where("lower(name) = ? and store_id = ?", key.downcase, current_store.id)
          .first_or_create(name: key, filter_param: key, presentation: key.capitalize, store: current_store)
        upc_product_property = product.product_properties.new({ position: idx, value: val, property: property })
        upc_product_property.save!
      end
      if params[:product][:images].present?
        selected_keys = params[:product][:images].select { |_key, value| value == "1" }.keys
        save_product_image(product.master, selected_keys.map(&:to_i))
      end
      flash[:success] = flash_message_for(product, :successfully_created)
      @scanned_item.destroy
      path = if common_add_stock_flow
        scan_vin_to_add_stock_admin_products_path(variant: product.master.id)
      else
        edit_admin_product_path(product)
      end
      redirect_to(path)
    end
  end

  def resolve_product_action
    case action_name
    when "create", "create_product" then :add
    else :default
    end
  end

  def resolve_product_action_place(product)
    case action_name
    when "create", "create_product"
      edit_admin_product_path(product)
    else
      "N/A"
    end
  end

  def save_product_image(p_variant, img_index)
    image_array = @scanned_item.images
    selected_images = img_index.map { |index| image_array[index] }
    selected_images.each do |img_url|
      ActiveRecord::Base.transaction do
        filename = img_url
        existing_img = p_variant.images.select { |img| filename == img.attachment.blob.filename.to_s }
        if existing_img.blank?
          image = URI.parse(img_url).open
          p_variant.images.create!(attachment: { io: image, filename: filename })
        end
      rescue StandardError => e
        Rails.logger.debug { "Exception #{e} for #{image} and product - #{p_variant.product.id}" }
      end
    end
  end
end
