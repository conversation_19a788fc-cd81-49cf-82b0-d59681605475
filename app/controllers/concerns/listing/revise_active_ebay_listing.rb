# frozen_string_literal: true

module Listing
  module ReviseActiveEbayListing
    extend ActiveSupport::Concern

    def revise_active_ebay_listing
      ActiveRecord::Base.transaction do
        listing = find_listing
        product = find_product

        record_activity_log(listing, action_type(params))
        ::Listing::UpdateListingService.call(listing, params, request, spree_current_user)
        ::Listing::ValidateListingService.call(listing, params)

        if params.dig(:listing, :submit_type) != "save to draft"
          response = Listing::UpdateEbayService.call(listing, params, request, current_store, spree_current_user, product)
          ::Listing::HandleResponseService.call(listing, response, params, product)
        end
      end
    rescue StandardError => e
      render_error_payload(e)
    end

    def quantity_price_quick_edit_ebay_listing
      ActiveRecord::Base.transaction do
        listing = find_listing
        product = listing.product

        record_activity_log(listing, "quantity_price_quick_edit")

        ::Listing::ValidateListingService.call(listing, params)
        ::Listing::UpdateListingService.call(listing, params, request, spree_current_user)
        if listing.status == "Active"
          response = Listing::UpdateEbayService.call(listing, params, request, current_store, spree_current_user, product)
          ::Listing::HandleResponseService.call(listing, response, params, product)
        end

        render_serialized_payload { serialize_resource(listing) }
      end
    rescue ActiveRecord::RecordNotFound, ActiveRecord::RecordInvalid => e
      render_error_payload(e)
    rescue StandardError => e
      render_error_payload(e)
    end

    private

    def find_listing
      listing = Spree::Listing.find_by(id: params[:id])
      raise ActiveRecord::RecordNotFound, "Listing not found" unless listing

      listing
    end

    def find_product
      product = Spree::Product.find_by(id: params[:listing][:product_id])
      raise ActiveRecord::RecordNotFound, "Product not found" unless product

      product
    end

    def action_type(params)
      submit_type = params.dig(:listing, :submit_type)

      case submit_type
      when 'save to draft', 'publish'
        'update'
      when 'revise', 'update'
        'revise'
      else
        raise StandardError, 'Submit type not defined'
      end
    end

  end
end
