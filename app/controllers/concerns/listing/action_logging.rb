# frozen_string_literal: true

module Listing
  module ActionLogging
    extend ActiveSupport::Concern

    def record_activity_log(listing, action)
      return unless listing && spree_current_user

      action_key, action_place = determine_action_and_action_place(action, listing)

      ::Spree::ActivityLog.create!(
        loggable: listing,
        user_id: spree_current_user.id,
        action: ::Spree::ActivityLogActions.get(:listing, action_key),
        role: spree_current_user.spree_roles.first&.name,
        date: Time.current,
        email: spree_current_user.email,
        action_place: action_place,
        action_name: listing.title,
        product_id: listing.product&.id
      )
    end

    private

    def determine_action_and_action_place(action, listing)
      case action
      when "new", "create"
        [:add, "/admin/listing/#{listing.id}/edit"]
      when "update", "revise_active_listing"
        [:edit, "/admin/listing/#{listing.id}/edit"]
      when "destroy", "end_item"
        [:remove, "/admin/listing"]
      else
        [:default, "N/A"]
      end
    end

    def action_logger
      return unless @listing

      record_activity_log(@listing, params[:action])
    end

    def set_listing
      @listing = Spree::Listing.find_by(id: params[:id])
    end
  end
end
