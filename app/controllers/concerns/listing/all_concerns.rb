# frozen_string_literal: true

module Listing
  module AllConcerns
    extend ActiveSupport::Concern
    # Automatically apply Elasticsearch filtering logic to the index action
    include Listing::ElasticsearchFilterable
    # Create Activity log and ActionLog
    include Listing::ActionLogging
    # For some common methods applicable to all sale channel listings.
    include Listing::SaleChannelApiClient
    # Update ebay listing
    include Listing::ReviseActiveEbayListing
    # Update Storefront listing
    include Listing::ReviseActiveStorefrontListing
    # Update walmart listing
    include Listing::ReviseActiveWalmartListing
    # Update Amazon listing
    include Listing::ReviseActiveAmazonListing
    # Related to Walmart
    include Listing::Walmart
  end
end
