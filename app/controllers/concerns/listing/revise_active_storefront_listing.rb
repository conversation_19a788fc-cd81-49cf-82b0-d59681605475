# frozen_string_literal: true

module Listing
  module ReviseActiveStorefrontListing
    extend ActiveSupport::Concern

    def revise_active_storefront_listing
      begin
        ActiveRecord::Base.transaction do
          validate_authorized_user
          @listing = fetch_listing
          @product = fetch_product
          validate_pack_size
          validate_variant_selected_or_not

          ::Listing::UpdateStorefrontService.new(@listing, update_listing_params, params, spree_current_user, request).call
          log_action_details(@listing, params[:action])
        end
      rescue ActiveRecord::RecordNotFound, ActiveRecord::RecordInvalid => e
        Rails.logger.error("============================== Error updating storefront listing: #{e.message} ==============================")
        render_error_payload(e.message)
      rescue StandardError => e
        Rails.logger.error("============================== Unexpected error: #{e.message} ==============================")
        render_error_payload("An unexpected error occurred. Please try again later.")
      end
    end

    private

    def fetch_listing
      listing = Spree::Listing.find_by(id: params[:id])
      raise ActiveRecord::RecordNotFound, "Listing not fount with id - #{params[:id]}" if listing.nil?

      listing
    end

    def fetch_product
      product = Spree::Product.find_by(id: params.dig(:listing, :product_id))
      raise ActiveRecord::RecordNotFound, "Product not fount with id - #{params.dig(:listing, :product_id)}" if product.nil?
      raise ActiveRecord::RecordNotFound, "Stock items are missing for Product - #{product.name}" if product.stock_items.blank?
      raise ActiveRecord::RecordInvalid.new, "Please update the product status to active first" if params.dig(:listing, :submit_type) == 'publish' && !product.active?

      product
    end

    def update_listing_params
      params.require(:listing).permit(
        :sale_channel_id, :product_id, :start_time, :end_time, :status,
        :sku, :title, :description, :pack_size_toggle, :pack_size_value, :shipping_category_id,
        volume_prices_attributes: [
          :id, :variant_id, :discount_type, :range, :amount, 
          :position, :role_id, :name, :_destroy
        ]
      ).tap do |permitted_params|
        permitted_params[:status] = convert_status(permitted_params[:status]) if permitted_params[:status].present?
        permitted_params[:pack_size_value] = permitted_params[:pack_size_toggle] ? permitted_params[:pack_size_value] : 1
      end
    end

    def validate_authorized_user
      unless authorized_to_create_listing?
        Rails.logger.warn("Unauthorized attempt to update listing")
        raise ActiveRecord::RecordInvalid.new, "You are not authorized to perform this action"
      end
    end

    def validate_pack_size
      submit_type = params.dig(:listing, :submit_type)
      return unless submit_type == 'publish' || submit_type == 'update'

      return unless params.dig(:listing, :pack_size_toggle)

      pack_size = params.dig(:listing, :pack_size_value).to_i
      raise ActiveRecord::RecordInvalid.new, "Pack size value must be ≤ #{@product.total_available}" if pack_size > @product.total_available
    end

    def validate_variant_selected_or_not
      submit_type = params.dig(:listing, :submit_type)
      return unless submit_type == 'publish' || submit_type == 'update'

      variants = params.dig(:listing, :variants)

      raise ActiveRecord::RecordInvalid.new, "Variants data not present" if variants.blank?

      return unless @product.variants.any?

      raise ActiveRecord::RecordInvalid.new, "At least one variant must be selected" unless variants.any? { |item| item[:is_selected] }
    end

    def log_action_details(listing, action)
      Rails.logger.info("Action #{action} performed on Listing ID #{listing.id}")
      record_activity_log(listing, "Edit Listing")
    end
  end
end
