# frozen_string_literal: true

module Listing
  module SaleChannelApiClient
    extend ActiveSupport::Concern

    # Sync listings with external channels (eBay, Amazon, Walmart)
    def sync
      channels_to_sync.find_each do |channel|
        sync_listing_with_channel(channel) if channel.oauth_application.present?
      end

      render_serialized_payload { { status: :ok } }
    end

    private

    # Fetch sale channels based on the provided brand in the params
    def channels_to_sync
      Spree::SaleChannel.where(brand: params[:channel]).includes(:oauth_application)
    end

    # Sync listings with the appropriate sale channel
    def sync_listing_with_channel(channel)
      sync_class = sync_class_for_channel(channel)
      if sync_class
        perform_sync(channel.oauth_application, sync_class)
      else
        raise_unsupported_channel_error(channel)
      end
    end

    # Returns the appropriate sync class based on the active sale channel
    def sync_class_for_channel(channel)
      case channel.brand.downcase
      when "ebay" then ::SyncEbayJob
      when "amazon" then ::Amazon::SyncAmazonJob
      when "walmart" then ::Walmart::SyncWalmart<PERSON>ob
      end
    end

    # Enqueue the job for syncing the listing
    def perform_sync(oauth_application, sync_class)
      sync_class.perform_later(current_store, oauth_application.id)
    end

    # Raise an error when the sale channel is unsupported
    def raise_unsupported_channel_error(channel)
      raise ArgumentError, "Unsupported sale channel: #{channel.name}"
    end

    # Set the active sale channel from params
    def set_sale_channel
      return if params[:action] == "sync"
      @sale_channel = Spree::SaleChannel.find_by(id: params[:sale_channel_id])
      raise ActiveRecord::RecordNotFound, "Sale channel not found" unless @sale_channel
    end
  end
end
