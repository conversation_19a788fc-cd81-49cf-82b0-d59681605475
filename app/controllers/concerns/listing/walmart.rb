# frozen_string_literal: true

module Listing
  module Walmart
    extend ActiveSupport::Concern

    def fetch_walmart_package_type
      listing = find_listing
      package_types = ::Walmart::ShippingApis::PackageType.new(listing.store_id, listing.oauth_application_id).call
      render json: { message: "success", data: package_types }
    rescue StandardError => e
      render_error_payload(e)
    end

    def end_walmart_listing
      listing = find_listing
      result = ::Listing::UpdateWalmartService.new(listing, params, spree_current_user, request, "api_v3").end_walmart_listing
      if result.success?
        render json: Api::V3::ListingSerializer.render(@resource.reload)
      else
        render json: { error: "Failed to end item on Walmart" }, status: :unprocessable_entity
      end
    rescue StandardError => e
      render_error_payload(e)
    end

    def get_walmart_product_id
      listing = find_listing
      result = ::Walmart::ItemService.new(listing, params).get_product_id_via_item_id
      if result.success?
        render json: { message: "success", data: result }
      else
        render json: { error: "Failed to get product id on Walmart" }, status: :unprocessable_entity
      end
    end
  end
end
