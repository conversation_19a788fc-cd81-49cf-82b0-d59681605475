# frozen_string_literal: true

module Listing
  module ReviseActiveWalmartListing
    extend ActiveSupport::Concern

    def revise_walmart_active_listing
      ActiveRecord::Base.transaction do
        listing = Spree::Listing.find_by(id: params[:id])

        ::Listing::UpdateWalmartService.new(listing, params, spree_current_user, request, "api_v3").revise_walmart_listing
        ::Listing::ValidateListingService.call(listing, params_for_validate(listing))
        if params[:commit] != "save to draft"
          record_activity_log(listing, action_type(params))
        end
      end
    rescue StandardError => e
      render_error_payload(e)
    end

    # def quantity_price_quick_edit_walmart_listing
    def quantity_price_quick_edit_walmart_listing
      ActiveRecord::Base.transaction do
        listing = find_listing

        ::Listing::ValidateListingService.call(listing, params)
        ::Listing::UpdateWalmartService.new(listing, params, spree_current_user, request, "api_v3").update_quantity
        record_activity_log(listing, action_name)
        render_serialized_payload { serialize_resource(listing) }
      end
    rescue ActiveRecord::RecordNotFound, ActiveRecord::RecordInvalid => e
      Rails.logger.info("entering quantity_price_quick_edit_walmart_listing - Error quick updating Walmart listing: #{e.message}")
      render_error_payload(e)
    rescue StandardError => e
      Rails.logger.info("entering quantity_price_quick_edit_walmart_listing - Unexpected error quick updating Walmart listing: #{e.message}")
      render_error_payload(e)
    end

    def params_for_validate(listing)
      product = listing.product
      params_variants = params.dig(:listing, :walmart, :variants) || params.dig(:walmart, :variants) || []
      first_params_variants = params_variants.first || {}

      additional = if product.variants.empty? || product.option_types.empty?
          {
            quantity: first_params_variants&.dig(:inventory_content, :shipNodes, 0, :quantity, :inputQty),
          }
        else
          variants = params_variants.map do |variant|
            {
              id: variant.dig(:variant_id),
              quantity: variant.dig(:inventory_content, :shipNodes, 0, :quantity, :inputQty),
            }
          end
        end

      params.merge(additional)
    end
  end
end
