# frozen_string_literal: true

module Admin
  class InlineEditableComponent < ApplicationComponent
    def initialize(model:, attribute:, edit_url: nil, form_url: nil)
      super
      @model = model
      @attribute = attribute
      @edit_url = edit_url
    end

    def frame_id
      @frame_id ||= Digest::MD5.hexdigest(dom_id(@model, "#{@attribute}_turbo_frame"))
    end

    def edit_url
      @edit_url ||= helpers.edit_polymorphic_path([:admin, @model])
    end

    def form_url
      @form_url ||= helpers.polymorphic_path([:admin, @model])
    end
  end
end
