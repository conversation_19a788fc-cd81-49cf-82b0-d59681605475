# frozen_string_literal: true

module Admin
  class InlineEditableFieldComponent < ApplicationComponent
    def initialize(model:, attribute:, cancel_url: nil)
      super
      @model = model
      @attribute = attribute
    end

    def frame_id
      @frame_id ||= Digest::MD5.hexdigest(dom_id(@model, "#{@attribute}_turbo_frame"))
    end

    def cancel_url
      @cancel_url ||= helpers.polymorphic_path([:admin, @model])
    end
  end
end
