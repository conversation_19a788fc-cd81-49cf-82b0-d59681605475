# frozen_string_literal: true

module Admin
  class ModalComponent < ApplicationComponent
    def initialize(frame: nil, size: nil, scrollable: true, backdrop: "static", refresh_on_close: false, center: false, zindex: nil)
      super
      @size = size.presence || "modal-xl"
      @scrollable = scrollable
      @backdrop = backdrop
      @refresh_on_close = refresh_on_close
      @frame = frame.presence || :modal
      @center = center
      @zindex = zindex || 1050
    end

    def modal_dialog_class
      class_list = ["modal-dialog"]
      class_list << @size
      class_list << "modal-dialog-scrollable" if @scrollable
      class_list << "modal-dialog-centered" if @center
      class_list.join(" ")
    end

    def backdrop_zindex
      @zindex - 1
    end
  end
end
