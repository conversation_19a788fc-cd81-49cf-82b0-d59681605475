# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  module Spree
    module Report
      module TimedResultDecorator
        def populate_observations
          @results.each do |result|
            matching_observation = @observations.find { |observation| observation.describes?(result, time_scale) }
            next if matching_observation.nil?

            matching_observation.populate(result)
          end
        end
      end
    end
  end
end

Spree::Report::TimedResult.prepend(AxelSpree::Spree::Report::TimedResultDecorator)
