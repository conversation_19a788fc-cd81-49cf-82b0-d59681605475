export default function e(){return{validate(e){if(e.value===""){return{meta:null,valid:true}}let t;switch(true){case/^M\d{9}$/.test(e.value):case/^M-\d{4}-\d{4}-\d{1}$/.test(e.value):case/^M\s\d{4}\s\d{4}\s\d{1}$/.test(e.value):t="ISMN10";break;case/^9790\d{9}$/.test(e.value):case/^979-0-\d{4}-\d{4}-\d{1}$/.test(e.value):case/^979\s0\s\d{4}\s\d{4}\s\d{1}$/.test(e.value):t="ISMN13";break;default:return{meta:null,valid:false}}let a=e.value;if("ISMN10"===t){a=`9790${a.substr(1)}`}a=a.replace(/[^0-9]/gi,"");let s=0;const l=a.length;const d=[1,3];for(let e=0;e<l-1;e++){s+=parseInt(a.charAt(e),10)*d[e%2]}s=(10-s%10)%10;return{meta:{type:t},valid:`${s}`===a.charAt(l-1)}}}}