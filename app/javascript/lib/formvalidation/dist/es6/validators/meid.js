import t from"../algorithms/luhn";export default function e(){return{validate(e){if(e.value===""){return{valid:true}}let r=e.value;if(/^[0-9A-F]{15}$/i.test(r)||/^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}[- ][0-9A-F]$/i.test(r)||/^\d{19}$/.test(r)||/^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}[- ]\d$/.test(r)){const e=r.charAt(r.length-1).toUpperCase();r=r.replace(/[- ]/g,"");if(r.match(/^\d*$/i)){return{valid:t(r)}}r=r.slice(0,-1);let a="";let i;for(i=1;i<=13;i+=2){a+=(parseInt(r.charAt(i),16)*2).toString(16)}let l=0;for(i=0;i<a.length;i++){l+=parseInt(a.charAt(i),16)}return{valid:l%10===0?e==="0":e===((Math.floor((l+10)/10)*10-l)*2).toString(16).toUpperCase()}}if(/^[0-9A-F]{14}$/i.test(r)||/^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}$/i.test(r)||/^\d{18}$/.test(r)||/^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}$/.test(r)){return{valid:true}}return{valid:false}}}}