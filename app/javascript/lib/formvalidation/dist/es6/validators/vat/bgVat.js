import t from"../../utils/isValidDate";export default function r(r){let e=r;if(/^BG[0-9]{9,10}$/.test(e)){e=e.substr(2)}if(!/^[0-9]{9,10}$/.test(e)){return{meta:{},valid:false}}let s=0;let n=0;if(e.length===9){for(n=0;n<8;n++){s+=parseInt(e.charAt(n),10)*(n+1)}s=s%11;if(s===10){s=0;for(n=0;n<8;n++){s+=parseInt(e.charAt(n),10)*(n+3)}s=s%11}s=s%10;return{meta:{},valid:`${s}`===e.substr(8)}}else{const r=r=>{let e=parseInt(r.substr(0,2),10)+1900;let s=parseInt(r.substr(2,2),10);const n=parseInt(r.substr(4,2),10);if(s>40){e+=100;s-=40}else if(s>20){e-=100;s-=20}if(!t(e,s,n)){return false}const a=[2,4,8,5,10,9,7,3,6];let l=0;for(let t=0;t<9;t++){l+=parseInt(r.charAt(t),10)*a[t]}l=l%11%10;return`${l}`===r.substr(9,1)};const s=t=>{const r=[21,19,17,13,11,9,7,3,1];let e=0;for(let s=0;s<9;s++){e+=parseInt(t.charAt(s),10)*r[s]}e=e%10;return`${e}`===t.substr(9,1)};const n=t=>{const r=[4,3,2,7,6,5,4,3,2];let e=0;for(let s=0;s<9;s++){e+=parseInt(t.charAt(s),10)*r[s]}e=11-e%11;if(e===10){return false}if(e===11){e=0}return`${e}`===t.substr(9,1)};return{meta:{},valid:r(e)||s(e)||n(e)}}}