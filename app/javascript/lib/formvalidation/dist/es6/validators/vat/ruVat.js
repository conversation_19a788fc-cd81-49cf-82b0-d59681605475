export default function t(t){let e=t;if(/^RU([0-9]{10}|[0-9]{12})$/.test(e)){e=e.substr(2)}if(!/^([0-9]{10}|[0-9]{12})$/.test(e)){return{meta:{},valid:false}}let r=0;if(e.length===10){const t=[2,4,10,3,5,9,4,6,8,0];let s=0;for(r=0;r<10;r++){s+=parseInt(e.charAt(r),10)*t[r]}s=s%11;if(s>9){s=s%10}return{meta:{},valid:`${s}`===e.substr(9,1)}}else if(e.length===12){const t=[7,2,4,10,3,5,9,4,6,8,0];const s=[3,7,2,4,10,3,5,9,4,6,8,0];let a=0;let l=0;for(r=0;r<11;r++){a+=parseInt(e.charAt(r),10)*t[r];l+=parseInt(e.charAt(r),10)*s[r]}a=a%11;if(a>9){a=a%10}l=l%11;if(l>9){l=l%10}return{meta:{},valid:`${a}`===e.substr(10,1)&&`${l}`===e.substr(11,1)}}return{meta:{},valid:true}}