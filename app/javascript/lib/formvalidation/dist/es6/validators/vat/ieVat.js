export default function t(t){let e=t;if(/^IE[0-9][0-9A-Z*+][0-9]{5}[A-Z]{1,2}$/.test(e)){e=e.substr(2)}if(!/^[0-9][0-9A-Z*+][0-9]{5}[A-Z]{1,2}$/.test(e)){return{meta:{},valid:false}}const r=t=>{let e=t;while(e.length<7){e=`0${e}`}const r="WABCDEFGHIJKLMNOPQRSTUV";let s=0;for(let t=0;t<7;t++){s+=parseInt(e.charAt(t),10)*(8-t)}s+=9*r.indexOf(e.substr(7));return r[s%23]};if(/^[0-9]+$/.test(e.substr(0,7))){return{meta:{},valid:e.charAt(7)===r(`${e.substr(0,7)}${e.substr(8)}`)}}else if("ABCDEFGHIJKLMNOPQRSTUVWXYZ+*".indexOf(e.charAt(1))!==-1){return{meta:{},valid:e.charAt(7)===r(`${e.substr(2,5)}${e.substr(0,1)}`)}}return{meta:{},valid:true}}