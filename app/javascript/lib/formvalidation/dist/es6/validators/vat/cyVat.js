export default function t(t){let e=t;if(/^CY[0-5|9][0-9]{7}[A-Z]$/.test(e)){e=e.substr(2)}if(!/^[0-5|9][0-9]{7}[A-Z]$/.test(e)){return{meta:{},valid:false}}if(e.substr(0,2)==="12"){return{meta:{},valid:false}}let r=0;const s={0:1,1:0,2:5,3:7,4:9,5:13,6:15,7:17,8:19,9:21};for(let t=0;t<8;t++){let a=parseInt(e.charAt(t),10);if(t%2===0){a=s[`${a}`]}r+=a}return{meta:{},valid:`${"ABCDEFGHIJKLMNOPQRSTUVWXYZ"[r%26]}`===e.substr(8,1)}}