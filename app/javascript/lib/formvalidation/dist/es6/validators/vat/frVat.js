import t from"../../algorithms/luhn";export default function e(e){let r=e;if(/^FR[0-9A-Z]{2}[0-9]{9}$/.test(r)){r=r.substr(2)}if(!/^[0-9A-Z]{2}[0-9]{9}$/.test(r)){return{meta:{},valid:false}}if(r.substr(2,4)!=="000"){return{meta:{},valid:t(r.substr(2))}}if(/^[0-9]{2}$/.test(r.substr(0,2))){return{meta:{},valid:r.substr(0,2)===`${parseInt(r.substr(2)+"12",10)%97}`}}else{const t="0123456789ABCDEFGHJKLMNPQRSTUVWXYZ";let e;if(/^[0-9]$/.test(r.charAt(0))){e=t.indexOf(r.charAt(0))*24+t.indexOf(r.charAt(1))-10}else{e=t.indexOf(r.charAt(0))*34+t.indexOf(r.charAt(1))-100}return{meta:{},valid:(parseInt(r.substr(2),10)+1+Math.floor(e/11))%11===e%11}}}