import t from"../../utils/isValidDate";export default function e(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(e)){return{meta:{},valid:false}}const a=e.replace(/-/g,"");const r=parseInt(a.substr(0,2),10);const s=parseInt(a.substr(2,2),10);let n=parseInt(a.substr(4,2),10);switch(true){case"5678".indexOf(a.charAt(6))!==-1&&n>=58:n+=1800;break;case"0123".indexOf(a.charAt(6))!==-1:case"49".indexOf(a.charAt(6))!==-1&&n>=37:n+=1900;break;default:n+=2e3;break}return{meta:{},valid:t(n,s,r)}}