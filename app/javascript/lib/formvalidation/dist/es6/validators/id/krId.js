import t from"../../utils/isValidDate";export default function e(e){const a=e.replace("-","");if(!/^\d{13}$/.test(a)){return{meta:{},valid:false}}const s=a.charAt(6);let r=parseInt(a.substr(0,2),10);const c=parseInt(a.substr(2,2),10);const n=parseInt(a.substr(4,2),10);switch(s){case"1":case"2":case"5":case"6":r+=1900;break;case"3":case"4":case"7":case"8":r+=2e3;break;default:r+=1800;break}if(!t(r,c,n)){return{meta:{},valid:false}}const l=[2,3,4,5,6,7,8,9,2,3,4,5];const o=a.length;let i=0;for(let t=0;t<o-1;t++){i+=l[t]*parseInt(a.char<PERSON>t(t),10)}const u=(11-i%11)%10;return{meta:{},valid:`${u}`===a.charAt(o-1)}}