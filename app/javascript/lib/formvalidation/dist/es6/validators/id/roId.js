import t from"../../utils/isValidDate";export default function e(e){if(!/^[0-9]{13}$/.test(e)){return{meta:{},valid:false}}const a=parseInt(e.charAt(0),10);if(a===0||a===7||a===8){return{meta:{},valid:false}}let r=parseInt(e.substr(1,2),10);const s=parseInt(e.substr(3,2),10);const n=parseInt(e.substr(5,2),10);const i={1:1900,2:1900,3:1800,4:1800,5:2e3,6:2e3};if(n>31&&s>12){return{meta:{},valid:false}}if(a!==9){r=i[a+""]+r;if(!t(r,s,n)){return{meta:{},valid:false}}}let l=0;const f=[2,7,9,1,4,6,3,5,8,2,7,9];const o=e.length;for(let t=0;t<o-1;t++){l+=parseInt(e.charAt(t),10)*f[t]}l=l%11;if(l===10){l=1}return{meta:{},valid:`${l}`===e.charAt(o-1)}}