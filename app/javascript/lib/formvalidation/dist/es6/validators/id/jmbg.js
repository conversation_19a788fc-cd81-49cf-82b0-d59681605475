export default function t(t,r){if(!/^\d{13}$/.test(t)){return false}const e=parseInt(t.substr(0,2),10);const s=parseInt(t.substr(2,2),10);const n=parseInt(t.substr(7,2),10);const a=parseInt(t.substr(12,1),10);if(e>31||s>12){return false}let u=0;for(let r=0;r<6;r++){u+=(7-r)*(parseInt(t.charAt(r),10)+parseInt(t.charAt(r+6),10))}u=11-u%11;if(u===10||u===11){u=0}if(u!==a){return false}switch(r.toUpperCase()){case"BA":return 10<=n&&n<=19;case"MK":return 41<=n&&n<=49;case"ME":return 20<=n&&n<=29;case"RS":return 70<=n&&n<=99;case"SI":return 50<=n&&n<=59;default:return true}}