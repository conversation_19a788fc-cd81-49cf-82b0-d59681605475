export default function t(t){const e=t.replace(/\D/g,"");if(!/^\d{11}$/.test(e)||/^1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11}|0{11}$/.test(e)){return{meta:{},valid:false}}let a=0;let r;for(r=0;r<9;r++){a+=(10-r)*parseInt(e.charAt(r),10)}a=11-a%11;if(a===10||a===11){a=0}if(`${a}`!==e.charAt(9)){return{meta:{},valid:false}}let f=0;for(r=0;r<10;r++){f+=(11-r)*parseInt(e.charAt(r),10)}f=11-f%11;if(f===10||f===11){f=0}return{meta:{},valid:`${f}`===e.charAt(10)}}