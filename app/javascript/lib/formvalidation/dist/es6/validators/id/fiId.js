import t from"../../utils/isValidDate";export default function s(s){if(!/^[0-9]{6}[-+A][0-9]{3}[0-9ABCDEFHJKLMNPRSTUVWXY]$/.test(s)){return{meta:{},valid:false}}const r=parseInt(s.substr(0,2),10);const a=parseInt(s.substr(2,2),10);let e=parseInt(s.substr(4,2),10);const n={"+":1800,"-":1900,A:2e3};e=n[s.charAt(6)]+e;if(!t(e,a,r)){return{meta:{},valid:false}}const u=parseInt(s.substr(7,3),10);if(u<2){return{meta:{},valid:false}}const i=parseInt(s.substr(0,6)+s.substr(7,3)+"",10);return{meta:{},valid:"0123456789ABCDEFHJKLMNPRSTUVWXY".charAt(i%31)===s.charAt(10)}}