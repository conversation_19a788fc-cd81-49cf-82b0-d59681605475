export default function t(t){let s=t.toUpperCase();if(!/^(1|2)\d{2}\d{2}(\d{2}|\d[A-Z]|\d{3})\d{2,3}\d{3}\d{2}$/.test(s)){return{meta:{},valid:false}}const e=s.substr(5,2);switch(true){case/^\d{2}$/.test(e):s=t;break;case e==="2A":s=`${t.substr(0,5)}19${t.substr(7)}`;break;case e==="2B":s=`${t.substr(0,5)}18${t.substr(7)}`;break;default:return{meta:{},valid:false}}const r=97-parseInt(s.substr(0,13),10)%97;const a=r<10?`0${r}`:`${r}`;return{meta:{},valid:a===s.substr(13)}}