import e from"../../utils/format";import r from"./arId";import a from"./baId";import o from"./bgId";import m from"./brId";import t from"./chId";import s from"./clId";import i from"./cnId";import l from"./coId";import c from"./czId";import u from"./dkId";import d from"./esId";import f from"./fiId";import b from"./frId";import k from"./hkId";import p from"./hrId";import v from"./idId";import I from"./ieId";import n from"./ilId";import h from"./isId";import y from"./krId";import g from"./ltId";import C from"./lvId";import R from"./meId";import E from"./mkId";import L from"./mxId";import S from"./myId";import O from"./nlId";import K from"./noId";import M from"./peId";import w from"./plId";import x from"./roId";import z from"./rsId";import H from"./seId";import T from"./siId";import A from"./smId";import B from"./thId";import N from"./trId";import U from"./twId";import j from"./uyId";import D from"./zaId";export default function F(){const F=["AR","BA","BG","BR","CH","CL","CN","CO","CZ","DK","EE","ES","FI","FR","HK","HR","ID","IE","IL","IS","KR","LT","LV","ME","MK","MX","MY","NL","NO","PE","PL","RO","RS","SE","SI","SK","SM","TH","TR","TW","UY","ZA"];return{validate(P){if(P.value===""){return{valid:true}}const Y=Object.assign({},{message:""},P.options);let Z=P.value.substr(0,2);if("function"===typeof Y.country){Z=Y.country.call(this)}else{Z=Y.country}if(F.indexOf(Z)===-1){return{valid:true}}let G={meta:{},valid:true};switch(Z.toLowerCase()){case"ar":G=r(P.value);break;case"ba":G=a(P.value);break;case"bg":G=o(P.value);break;case"br":G=m(P.value);break;case"ch":G=t(P.value);break;case"cl":G=s(P.value);break;case"cn":G=i(P.value);break;case"co":G=l(P.value);break;case"cz":G=c(P.value);break;case"dk":G=u(P.value);break;case"ee":G=g(P.value);break;case"es":G=d(P.value);break;case"fi":G=f(P.value);break;case"fr":G=b(P.value);break;case"hk":G=k(P.value);break;case"hr":G=p(P.value);break;case"id":G=v(P.value);break;case"ie":G=I(P.value);break;case"il":G=n(P.value);break;case"is":G=h(P.value);break;case"kr":G=y(P.value);break;case"lt":G=g(P.value);break;case"lv":G=C(P.value);break;case"me":G=R(P.value);break;case"mk":G=E(P.value);break;case"mx":G=L(P.value);break;case"my":G=S(P.value);break;case"nl":G=O(P.value);break;case"no":G=K(P.value);break;case"pe":G=M(P.value);break;case"pl":G=w(P.value);break;case"ro":G=x(P.value);break;case"rs":G=z(P.value);break;case"se":G=H(P.value);break;case"si":G=T(P.value);break;case"sk":G=c(P.value);break;case"sm":G=A(P.value);break;case"th":G=B(P.value);break;case"tr":G=N(P.value);break;case"tw":G=U(P.value);break;case"uy":G=j(P.value);break;case"za":G=D(P.value);break;default:break}const V=e(P.l10n&&P.l10n.id?Y.message||P.l10n.id.country:Y.message,P.l10n&&P.l10n.id&&P.l10n.id.countries?P.l10n.id.countries[Z.toUpperCase()]:Z.toUpperCase());return Object.assign({},{message:V},G)}}}