export default function e(e){if(e.length<8){return{meta:{},valid:false}}let t=e;if(t.length===8){t=`0${t}`}if(!/^[0-9]{4}[.]{0,1}[0-9]{2}[.]{0,1}[0-9]{3}$/.test(t)){return{meta:{},valid:false}}t=t.replace(/\./g,"");if(parseInt(t,10)===0){return{meta:{},valid:false}}let a=0;const l=t.length;for(let e=0;e<l-1;e++){a+=(9-e)*parseInt(t.charAt(e),10)}a=a%11;if(a===10){a=0}return{meta:{},valid:`${a}`===t.charAt(l-1)}}