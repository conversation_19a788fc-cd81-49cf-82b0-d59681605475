export default function t(t){const e=t.toUpperCase();if(!/^[A-MP-Z]{1,2}[0-9]{6}[0-9A]$/.test(e)){return{meta:{},valid:false}}const s="ABCDEFGHIJKLMNOPQRSTUVWXYZ";const n=e.charAt(0);const r=e.charAt(1);let a=0;let c=e;if(/^[A-Z]$/.test(r)){a+=9*(10+s.indexOf(n));a+=8*(10+s.indexOf(r));c=e.substr(2)}else{a+=9*36;a+=8*(10+s.indexOf(n));c=e.substr(1)}const o=c.length;for(let t=0;t<o-1;t++){a+=(7-t)*parseInt(c.charAt(t),10)}const f=a%11;const l=f===0?"0":11-f===10?"A":`${11-f}`;return{meta:{},valid:l===c.charAt(o-1)}}