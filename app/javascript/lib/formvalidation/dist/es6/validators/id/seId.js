import t from"../../algorithms/luhn";import s from"../../utils/isValidDate";export default function r(r){if(!/^[0-9]{10}$/.test(r)&&!/^[0-9]{6}[-|+][0-9]{4}$/.test(r)){return{meta:{},valid:false}}const e=r.replace(/[^0-9]/g,"");const a=parseInt(e.substr(0,2),10)+1900;const n=parseInt(e.substr(2,2),10);const i=parseInt(e.substr(4,2),10);if(!s(a,n,i)){return{meta:{},valid:false}}return{meta:{},valid:t(e)}}