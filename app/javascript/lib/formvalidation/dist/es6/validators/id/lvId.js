import t from"../../utils/isValidDate";export default function e(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{5}$/.test(e)){return{meta:{},valid:false}}const r=e.replace(/\D/g,"");const a=parseInt(r.substr(0,2),10);const s=parseInt(r.substr(2,2),10);let n=parseInt(r.substr(4,2),10);n=n+1800+parseInt(r.charAt(6),10)*100;if(!t(n,s,a,true)){return{meta:{},valid:false}}let l=0;const i=[10,5,8,4,2,1,6,3,7,9];for(let t=0;t<10;t++){l+=parseInt(r.charAt(t),10)*i[t]}l=(l+1)%11%10;return{meta:{},valid:`${l}`===r.charAt(10)}}