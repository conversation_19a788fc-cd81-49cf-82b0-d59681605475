import t from"../../utils/isValidDate";export default function e(e){if(!/^\d{10}$/.test(e)&&!/^\d{6}\s\d{3}\s\d{1}$/.test(e)){return{meta:{},valid:false}}const s=e.replace(/\s/g,"");let r=parseInt(s.substr(0,2),10)+1900;let a=parseInt(s.substr(2,2),10);const l=parseInt(s.substr(4,2),10);if(a>40){r+=100;a-=40}else if(a>20){r-=100;a-=20}if(!t(r,a,l)){return{meta:{},valid:false}}let i=0;const n=[2,4,8,5,10,9,7,3,6];for(let t=0;t<9;t++){i+=parseInt(s.charAt(t),10)*n[t]}i=i%11%10;return{meta:{},valid:`${i}`===s.substr(9,1)}}