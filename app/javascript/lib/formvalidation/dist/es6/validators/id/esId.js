export default function t(t){const e=/^[0-9]{8}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(t);const s=/^[XYZ][-]{0,1}[0-9]{7}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(t);const n=/^[A-HNPQS][-]{0,1}[0-9]{7}[-]{0,1}[0-9A-J]$/.test(t);if(!e&&!s&&!n){return{meta:{},valid:false}}let r=t.replace(/-/g,"");let l;let a;let f=true;if(e||s){a="DNI";const t="XYZ".indexOf(r.charAt(0));if(t!==-1){r=t+r.substr(1)+"";a="NIE"}l=parseInt(r.substr(0,8),10);l="TRWAGMYFPDXBNJZSQVHLCKE"[l%23];return{meta:{type:a},valid:l===r.substr(8,1)}}else{l=r.substr(1,7);a="CIF";const t=r[0];const e=r.substr(-1);let s=0;for(let t=0;t<l.length;t++){if(t%2!==0){s+=parseInt(l[t],10)}else{const e=""+parseInt(l[t],10)*2;s+=parseInt(e[0],10);if(e.length===2){s+=parseInt(e[1],10)}}}let n=s-Math.floor(s/10)*10;if(n!==0){n=10-n}if("KQS".indexOf(t)!==-1){f=e==="JABCDEFGHI"[n]}else if("ABEH".indexOf(t)!==-1){f=e===""+n}else{f=e===""+n||e==="JABCDEFGHI"[n]}return{meta:{type:a},valid:f}}}