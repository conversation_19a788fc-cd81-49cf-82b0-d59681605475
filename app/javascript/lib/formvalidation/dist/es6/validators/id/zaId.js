import t from"../../algorithms/luhn";import e from"../../utils/isValidDate";export default function r(r){if(!/^[0-9]{10}[0|1][8|9][0-9]$/.test(r)){return{meta:{},valid:false}}let s=parseInt(r.substr(0,2),10);const a=(new Date).getFullYear()%100;const l=parseInt(r.substr(2,2),10);const n=parseInt(r.substr(4,2),10);s=s>=a?s+1900:s+2e3;if(!e(s,l,n)){return{meta:{},valid:false}}return{meta:{},valid:t(r)}}