import t from"../../utils/isValidDate";export default function e(e){if(!/^\d{9,10}$/.test(e)){return{meta:{},valid:false}}let r=1900+parseInt(e.substr(0,2),10);const s=parseInt(e.substr(2,2),10)%50%20;const a=parseInt(e.substr(4,2),10);if(e.length===9){if(r>=1980){r-=100}if(r>1953){return{meta:{},valid:false}}}else if(r<1954){r+=100}if(!t(r,s,a)){return{meta:{},valid:false}}if(e.length===10){let t=parseInt(e.substr(0,9),10)%11;if(r<1985){t=t%10}return{meta:{},valid:`${t}`===e.substr(9,1)}}return{meta:{},valid:true}}