export default function t(){return{validate(t){if(t.value===""){return{valid:true}}if(!/^[a-hj-npr-z0-9]{8}[0-9xX][a-hj-npr-z0-9]{8}$/i.test(t.value)){return{valid:false}}const e=t.value.toUpperCase();const r={A:1,B:2,C:3,D:4,E:5,F:6,G:7,H:8,J:1,K:2,L:3,M:4,N:5,P:7,R:9,S:2,T:3,U:4,V:5,W:6,X:7,Y:8,Z:9,0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9};const a=[8,7,6,5,4,3,2,10,0,9,8,7,6,5,4,3,2];const l=e.length;let n=0;for(let t=0;t<l;t++){n+=r[`${e.charAt(t)}`]*a[t]}let u=`${n%11}`;if(u==="10"){u="X"}return{valid:u===e.charAt(8)}}}}