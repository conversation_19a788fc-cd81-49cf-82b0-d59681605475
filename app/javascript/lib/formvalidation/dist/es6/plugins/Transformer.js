import t from"../core/Plugin";export default class e extends t{constructor(t){super(t);this.valueFilter=this.getElementValue.bind(this)}install(){this.core.registerFilter("field-value",this.valueFilter)}uninstall(){this.core.deregisterFilter("field-value",this.valueFilter)}getElementValue(t,e,i,s){if(this.opts[e]&&this.opts[e][s]&&"function"===typeof this.opts[e][s]){return this.opts[e][s].apply(this,[e,i,s])}return t}}