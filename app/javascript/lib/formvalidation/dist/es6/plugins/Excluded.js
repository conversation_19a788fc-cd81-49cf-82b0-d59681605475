import t from"../core/Plugin";export default class e extends t{constructor(t){super(t);this.opts=Object.assign({},{excluded:e.defaultIgnore},t);this.ignoreValidationFilter=this.ignoreValidation.bind(this)}static defaultIgnore(t,e,i){const r=!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length);const n=e.getAttribute("disabled");return n===""||n==="disabled"||e.getAttribute("type")==="hidden"||!r}install(){this.core.registerFilter("element-ignored",this.ignoreValidationFilter)}uninstall(){this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter)}ignoreValidation(t,e,i){return this.opts.excluded.apply(this,[t,e,i])}}