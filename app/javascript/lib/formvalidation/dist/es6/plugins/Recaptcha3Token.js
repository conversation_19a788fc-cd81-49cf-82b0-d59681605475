import e from"../core/Plugin";export default class t extends e{constructor(e){super(e);this.opts=Object.assign({},{action:"submit",hiddenTokenName:"___hidden-token___"},e);this.onValidHandler=this.onFormValid.bind(this)}install(){this.core.on("core.form.valid",this.onValidHandler);this.hiddenTokenEle=document.createElement("input");this.hiddenTokenEle.setAttribute("type","hidden");this.core.getFormElement().appendChild(this.hiddenTokenEle);const e=typeof window[t.LOADED_CALLBACK]==="undefined"?()=>{}:window[t.LOADED_CALLBACK];window[t.LOADED_CALLBACK]=()=>{e()};const o=this.getScript();if(!document.body.querySelector(`script[src="${o}"]`)){const e=document.createElement("script");e.type="text/javascript";e.async=true;e.defer=true;e.src=o;document.body.appendChild(e)}}uninstall(){this.core.off("core.form.valid",this.onValidHandler);const e=this.getScript();const t=[].slice.call(document.body.querySelectorAll(`script[src="${e}"]`));t.forEach((e=>e.parentNode.removeChild(e)));this.core.getFormElement().removeChild(this.hiddenTokenEle)}onFormValid(){window["grecaptcha"].execute(this.opts.siteKey,{action:this.opts.action}).then((e=>{this.hiddenTokenEle.setAttribute("name",this.opts.hiddenTokenName);this.hiddenTokenEle.value=e;const t=this.core.getFormElement();if(t instanceof HTMLFormElement){t.submit()}}))}getScript(){const e=this.opts.language?`&hl=${this.opts.language}`:"";return"https://www.google.com/recaptcha/api.js?"+`onload=${t.LOADED_CALLBACK}&render=${this.opts.siteKey}${e}`}}t.LOADED_CALLBACK="___reCaptcha3Loaded___";