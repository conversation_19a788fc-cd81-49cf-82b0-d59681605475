import e from"../utils/classSet";import t from"./Framework";export default class r extends t{constructor(e){super(Object.assign({},{formClass:"fv-plugins-turret",messageClass:"form-message",rowInvalidClass:"fv-invalid-row",rowPattern:/^field$/,rowSelector:".field",rowValidClass:"fv-valid-row"},e))}onIconPlaced(t){const r=t.element.getAttribute("type");const o=t.element.parentElement;if("checkbox"===r||"radio"===r){o.parentElement.insertBefore(t.iconElement,o.nextSibling);e(t.iconElement,{"fv-plugins-icon-check":true})}}}