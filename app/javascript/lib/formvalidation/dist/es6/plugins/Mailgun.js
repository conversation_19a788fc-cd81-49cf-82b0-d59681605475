import s from"../core/Plugin";import e from"./Alias";export default class i extends s{constructor(s){super(s);this.opts=Object.assign({},{suggestion:false},s);this.messageDisplayedHandler=this.onMessageDisplayed.bind(this)}install(){if(this.opts.suggestion){this.core.on("plugins.message.displayed",this.messageDisplayedHandler)}const s={mailgun:"remote"};this.core.registerPlugin("___mailgunAlias",new e(s)).addField(this.opts.field,{validators:{mailgun:{crossDomain:true,data:{api_key:this.opts.apiKey},headers:{"Content-Type":"application/json"},message:this.opts.message,name:"address",url:"https://api.mailgun.net/v3/address/validate",validKey:"is_valid"}}})}uninstall(){if(this.opts.suggestion){this.core.off("plugins.message.displayed",this.messageDisplayedHandler)}this.core.removeField(this.opts.field)}onMessageDisplayed(s){if(s.field===this.opts.field&&"mailgun"===s.validator&&s.meta&&s.meta["did_you_mean"]){s.messageElement.innerHTML=`Did you mean ${s.meta["did_you_mean"]}?`}}}