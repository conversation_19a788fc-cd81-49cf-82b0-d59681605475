import e from"../utils/classSet";import t from"./Framework";export default class n extends t{constructor(e){super(Object.assign({},{formClass:"fv-plugins-shoelace",messageClass:"fv-help-block",rowInvalidClass:"input-invalid",rowPattern:/^(.*)(col|offset)-[0-9]+(.*)$/,rowSelector:".input-field",rowValidClass:"input-valid"},e))}onIconPlaced(t){const n=t.element.parentElement;const l=t.element.getAttribute("type");if("checkbox"===l||"radio"===l){e(t.iconElement,{"fv-plugins-icon-check":true});if("LABEL"===n.tagName){n.parentElement.insertBefore(t.iconElement,n.nextSibling)}}}}