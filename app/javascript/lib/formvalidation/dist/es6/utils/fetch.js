const e=e=>Array(e).fill("").map((e=>Math.random().toString(36).charAt(2))).join("");export default function t(t,n){const o=e=>Object.keys(e).map((t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`)).join("&");return new Promise(((s,a)=>{const r=Object.assign({},{crossDomain:false,headers:{},method:"GET",params:{}},n);const d=Object.keys(r.params).map((e=>`${encodeURIComponent(e)}=${encodeURIComponent(r.params[e])}`)).join("&");const c=t.indexOf("?");const i="GET"===r.method?`${t}${c?"?":"&"}${d}`:t;if(r.crossDomain){const t=document.createElement("script");const n=`___FormValidationFetch_${e(12)}___`;window[n]=e=>{delete window[n];s(e)};t.src=`${i}${c?"&":"?"}callback=${n}`;t.async=true;t.addEventListener("load",(()=>{t.parentNode.removeChild(t)}));t.addEventListener("error",(()=>a));document.head.appendChild(t)}else{const e=new XMLHttpRequest;e.open(r.method,i);e.setRequestHeader("X-Requested-With","XMLHttpRequest");if("POST"===r.method){e.setRequestHeader("Content-Type","application/x-www-form-urlencoded")}Object.keys(r.headers).forEach((t=>e.setRequestHeader(t,r.headers[t])));e.addEventListener("load",(function(){s(JSON.parse(this.responseText))}));e.addEventListener("error",(()=>a));e.send(o(r.params))}}))}