import $ from 'jquery'

const { createApp, ref, onMounted, watchEffect } = window.Vue
const app = createApp({
  setup () {
    const loading = ref(false)
    const uploader = ref(null)
    const fileList = ref([])
    const previewSize = ref(parseInt((document.body.clientWidth - 32) / 3))
    const handleDeleteItem = file => {
      fileList.value = fileList.value.filter(i => i !== file)
    }
    const handleOpenCamera = () => {
      let counter = 5
      const timer = setInterval(() => {
        if (--counter > 0) {
          showDialog()
        } else {
          clearInterval(timer)
          window.vant.closeDialog()
        }
      }, 1000)
      const showDialog = () => {
        window.vant.showConfirmDialog({
          title: 'Tips',
          message: 'Do you want to continue taking photos?',
          confirmButtonText: `Continue(${counter}s)`,
          cancelButtonText: 'Cancel'
        })
          .then(() => {
            uploader.value?.chooseFile()
          })
          .catch(console.log)
          .finally(() => {
            clearInterval(timer)
          })
      }

      showDialog()
    }
    const handleUpload = () => {
      const { resourceId } = $('#resource-data').data()
      const { resourceClass } = $('#resource-class').data()
      const { userToken } = $('#user-data').data()
      const { variantId } = $('#variant-id').data()
      const failedList = []
      const startUpload = (i = 0) => {
        const item = fileList.value[i]
        const formData = new FormData()

        if (!item) {
          setTimeout(() => {
            loading.value = false
            fileList.value = failedList
          }, 3000)

          return window.vant.showSuccessToast('Image(s) uploaded successfully')
        }

        item.status = 'uploading'
        item.message = 'Uploading'

        formData.append(`file_${i}`, item.file)

        $.ajax({
          url: '/old_admin/image_uploader' + '?resource_id=' + resourceId + '&user_token=' + userToken + '&resource=' + resourceClass + '&variant_id=' + variantId,
          type: 'post',
          data: formData,
          processData: false,
          contentType: false,
          success () {
            item.status = 'done'
            item.message = 'Uploaded'

            startUpload(++i)
          },
          error (error) {
            const message = error.statusText || error.responseJSON?.error || `${i}th image upload failed`

            item.status = 'failed'
            item.message = 'Upload failed'

            failedList.push(item)

            window.vant.showNotify({
              message,
              type: 'warning'
            })

            startUpload(++i)
          }
        })
      }

      loading.value = true

      startUpload()
    }

    onMounted(() => {
      watchEffect(() => {
        if (!fileList.value.length) {
          window.vant.showToast({
            duration: 10000,
            wordBreak: 'break-word',
            message: 'Automatically opening the camera failed. Click anywhere on the screen to open the camera.'
          })
        } else {
          window.vant.closeToast()
        }
      })

      uploader.value?.chooseFile()
    })

    return {
      loading,
      uploader,
      fileList,
      previewSize,
      handleDeleteItem,
      handleOpenCamera,
      handleUpload
    }
  }
})
window.initDevtools = () => {
  const script = document.createElement('script')

  script.src = 'https://cdnjs.cloudflare.com/ajax/libs/vConsole/3.15.1/vconsole.min.js'

  document.body.appendChild(script)

  script.onload = () => {
    window.vconsole = new window.VConsole()
  }
}

app.use(window.vant).use(window.vant.Lazyload).mount('#app')
