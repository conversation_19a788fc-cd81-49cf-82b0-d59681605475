import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = [
    'customMessage',
    'customMessageAvailable'
  ]

  connect () {
  }

  handleTemplateChange (e) {
    const shippingEmailId = e.target.value
    const customMessageAvailable = JSON.parse(this.customMessageAvailableTarget.value)[shippingEmailId].custom_message
    if (customMessageAvailable) {
      this.customMessageTarget.disabled = false
      this.customMessageTarget.placeholder = 'Add message to tracking email'
    } else {
      this.customMessageTarget.disabled = true
      this.customMessageTarget.placeholder = 'custom message is not used in the email template'
    }
  }

  hideAndShowInsureAmount (e) {
    const insureForAmountDiv = $('#insure_for_amount_div')
    const costToInsureDiv = $('#cost_to_insure_div')
    const transferSymbolDiv = $('#transfer_symbol_div')

    if (!e.target.checked) {
      insureForAmountDiv.removeClass('d-block')
      insureForAmountDiv.addClass('d-none')
      costToInsureDiv.removeClass('d-block')
      costToInsureDiv.addClass('d-none')
      transferSymbolDiv.removeClass('d-block')
      transferSymbolDiv.addClass('d-none')
    } else {
      insureForAmountDiv.removeClass('d-none')
      insureForAmountDiv.addClass('d-block')
      costToInsureDiv.removeClass('d-none')
      costToInsureDiv.addClass('d-block')
      transferSymbolDiv.removeClass('d-none')
      transferSymbolDiv.addClass('d-block')
    }
  }

  transferToInsureCost (e) {
    const costToInsureElement = document.getElementById('cost_to_insure_element')

    if (e.currentTarget.value.includes('.')) {
      const parts = e.currentTarget.value.split('.')
      if (parts[1].length > 2) {
        parts[1] = parts[1].slice(0, 2)
        e.currentTarget.value = parts.join('.')
      }
    }

    if (e.currentTarget.value.length > 7) {
      e.currentTarget.value = ''
      costToInsureElement.setAttribute('value', '')
      return
    }

    const el = e.srcElement
    if (!/^(\d+\.?)?\d{0,2}$/.test(el.value)) {
      e.currentTarget.value = ''
      costToInsureElement.setAttribute('value', '')
      return
    }

    const amount = parseFloat(e.currentTarget.value)
    if (isNaN(amount) || amount > 5000) {
      e.currentTarget.value = ''
      costToInsureElement.setAttribute('value', '')
      return
    }

    let newAmount = (amount / 100).toFixed(5)
    if (newAmount < 0.5) {
      newAmount = 0.5
    }
    costToInsureElement.setAttribute('value', newAmount)
  }
}
