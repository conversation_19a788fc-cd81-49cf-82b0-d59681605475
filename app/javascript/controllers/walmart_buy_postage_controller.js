import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['packageType', 'length', 'width', 'height', 'weight']

  connect () {
    this.element.addEventListener('click', (event) => {
      if (event.target.classList.contains('walmart-buy-postage')) {
        this.handleBuyPostageClick(event)
      }
    })

    const rateList = document.getElementById('walmart-shipping-rates-list')

    if (rateList) {
      rateList.addEventListener('click', (event) => {
        const listItem = event.target.closest('.list-group-rate-item')
        const confirmButton = $('#walmart-buy-postage-confirm')[0]

        if (listItem) {
          const radioButton = listItem.querySelector('.form-check-input')

          if (radioButton) {
            radioButton.checked = true
            confirmButton.classList.remove('disabled')
          }
        } else {
          confirmButton.classList.toggle('disabled', !listItem)
        }
      })
    }
  }

  openModal (event) {
    $('#upc_text_scan_modal').modal('show')
    this.fetchPackageType()
  }

  fetchPackageType () {
    const saleChannelId = $('#upcSearchModalLabel')[0].getAttribute('sale_channel_id')
    window.showLoading()
    fetch(`/old_admin/listings/fetch_walmart_package_type?id=${saleChannelId}`)
      .then(response => response.json())
      .then(data => {
        if (data && data.data && data.data.data && data.data.data.length > 0) {
          this.populatePackageTypes(data.data.data)
        }
        window.hideLoading()
      })
      .catch(error => console.error('Error fetching product groups:', error))
  }

  populatePackageTypes (packageTypesValue) {
    const selectElement = $('#package_type_select')[0]
    selectElement.innerHTML = ''

    const defaultOption = document.createElement('option')
    defaultOption.text = 'Select'
    defaultOption.value = ''
    selectElement.appendChild(defaultOption)

    packageTypesValue.forEach(pkg => {
      const displayValue = pkg.packageTypeDisplayName === 'Custom Package'
        ? pkg.packageTypeDisplayName
        : `${pkg.packageTypeDisplayName} - ${pkg.length}" x ${pkg.width}" x ${pkg.height}"`

      const option = document.createElement('option')
      option.value = pkg.id
      option.text = displayValue

      option.setAttribute('data-length', pkg.length)
      option.setAttribute('data-width', pkg.width)
      option.setAttribute('data-height', pkg.height)
      option.setAttribute('data-name', `${pkg.packageTypeDisplayName}`)

      selectElement.appendChild(option)
    })
  }

  createShipmentIdArray (event) {
    const orderPackageNumber = event.target.dataset.orderPackageNumber
    const checkboxes = document.querySelectorAll(`[id^="checkbox_name_"][class*="${orderPackageNumber}"]`)
    const shipmentIdArray = []
    checkboxes.forEach(function (checkbox) {
      if (checkbox.checked) {
        const shipmentId = checkbox.dataset.shipmentIdTarget
        shipmentIdArray.push(shipmentId)
      }
    })
    return shipmentIdArray
  }

  appendDimensionValues (event) {
    const selectedOption = event.currentTarget.options[event.currentTarget.selectedIndex]
    const { length, width, height } = selectedOption.dataset

    if (!length || !width || !height) {
      console.error('Missing package dimensions (length, width, height) in the selected option.')
      return
    }

    const $lengthField = $('#walmart_length')
    const $widthField = $('#walmart_width')
    const $heightField = $('#walmart_height')

    $lengthField.val(length)
    $widthField.val(width)
    $heightField.val(height)
  }

  validateFields () {
    const packageType = this.packageTypeTarget.value
    const length = this.lengthTarget.value
    const width = this.widthTarget.value
    const height = this.heightTarget.value
    const weight = this.weightTarget.value

    if (!packageType || !length || !width || !height || !weight) {
      return false
    }
    return true
  }

  renderShippingRates (event) {
    if (!this.validateFields()) {
      alert('Please fill in all required fields: Package Type, Dimensions, and Weight.')
      return
    }

    const shipmentNumber = event.target.dataset.shipmentNumber
    const refresh = event.target.dataset.refresh
    const url = Spree.url(Spree.routes.shipments_api_v2 + '/' + shipmentNumber + '/shipping_rates')
    const shipmentIdArray = this.createShipmentIdArray(event)

    const weightValue = $('#walmart_weight').val()
    const weightUnit = $('#weight_unit').val()
    const length = $('#walmart_length').val()
    const width = $('#walmart_width').val()
    const height = $('#walmart_height').val()
    const dimensionUnit = $('#dimension_unit').val()

    const hdr = Spree.apiV2Authentication() /* global Spree */
    hdr['Content-Type'] = 'application/json'

    fetch(url, {
      method: 'PUT',
      headers: hdr,
      body: JSON.stringify({
        refresh,
        weight: weightValue,
        weight_unit: weightUnit,
        width,
        length,
        height,
        dimension_unit: dimensionUnit,
        buy_postage: 'walmart',
        shipment_ids: shipmentIdArray
      })
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Error ${response.status}: ${response.statusText}`)
        }
        return response.json()
      })
      .then(responseData => {
        const rateListDiv = document.getElementById('walmart-shipping-rates-list')
        const shippingMethodRow = rateListDiv.closest('.row')
        const allRatesHTML = responseData.data
          .map(data => this.generateRadioButton(data))
          .join('')

        if (rateListDiv && shippingMethodRow) {
          rateListDiv.innerHTML = allRatesHTML
          shippingMethodRow.classList.remove('d-none')
          $('#buy-postage-confirm').removeClass('disabled')
        }
      })
      .catch(error => {
        console.error('Error:', error)
        this.showFlash('error', error.message)
        window.hideLoading()
      })
  }

  generateRadioButton (data) {
    const dt = data.attributes

    return `
      <div class="list-group-rate-item" data-id="${data.id}">
        <div class="form-check" style="display: flex;">
          <input class="form-check-input" type="radio" name="${dt.shipping_method_admin_name}" id="radio-${data.id}" value="${data.id}" data-target-carrierServiceType="${dt.name}" data-target-carrier="${dt.code}">
          <strong style="width: 40%; max-width: 40%;">${dt.shipping_method_admin_name}</strong>
          <strong style="width: 40%; max-width: 40%; margin-left:20px;">${dt.shipping_method_admin_name}</strong>
          <span class="form-check-label" style="float: right;margin-left: 20px;">${dt.display_cost}</span>
        </div>
      </div>`
  }

  handleBuyPostageClick (event) {
    const shipmentIdArray = this.createShipmentIdArray(event)
    let carrier
    let carrierServicetype
    let shippingRateId
    const link = event.target

    if (link.classList.contains('disabled')) {
      return
    }

    Array.from(document.querySelectorAll('.list-group-rate-item')).forEach(item => {
      if (item.querySelector('input').checked) {
        carrier = item.querySelector('input').dataset.targetCarrier
        carrierServicetype = item.querySelector('input').dataset.targetCarrierservicetype
        shippingRateId = item.querySelector('input').value
      }
    })

    const shipmentNumberValue = link.dataset.shipmentNumber
    const url = Spree.url(Spree.routes.shipments_api_v2 + '/' + shipmentNumberValue + '/buy_postage')
    const url2 = Spree.url(Spree.routes.shipments_api_v2 + '/' + shipmentNumberValue + '/attach_pdf')

    const shipmentEmailId = document.getElementById('shipment_email_id')?.value || ''
    const customMessage = document.getElementById('custom_message')?.value || ''
    const insure = document.getElementById('insure_element')?.checked || false
    const insureForAmount = document.getElementById('insure_for_amount_element')?.value || ''

    const weight = $('#walmart_weight').val()
    const { length, width, height } = this.getDimensions()

    if ((length || width || height) && !(length && width && height)) {
      this.showFlash('error', 'Dimension length/width/height should be all filled or all empty')
      return
    }

    const dimensionsMatch = this.checkDimensions(weight)
    if (dimensionsMatch) {
      this.buyPostage(url, shipmentIdArray, shipmentEmailId, customMessage, insure, insureForAmount, weight, length, width, height, carrier, carrierServicetype, shippingRateId, url2)
    } else {
      this.showDimensionError()
    }
  }

  getDimensions () {
    return {
      length: $('#walmart_length').val(),
      width: $('#walmart_width').val(),
      height: $('#walmart_height').val()
    }
  }

  checkDimensions () {
    const customMessage = $('#package_type_select').find('option:selected')[0].dataset.name === 'Custom Package'

    if (customMessage) {
      return true
    }

    const lengthTarget = $('#walmart_length').data('target-length')
    const widthTarget = $('#walmart_width').data('target-width')
    const heightTarget = $('#walmart_height').data('target-height')

    const lengthValue = $('#walmart_length').val()
    const widthValue = $('#walmart_width').val()
    const heightValue = $('#walmart_height').val()

    return (
      lengthValue === lengthTarget &&
      widthValue === widthTarget &&
      heightValue === heightTarget
    )
  }

  buyPostage (url, shipmentIdArray, shipmentEmailId, customMessage, insure, insureForAmount, weight, length, width, height, carrier, carrierServicetype, shippingRateId, url2) {
    const dimensionUnit = $('#dimension_unit').val()
    const weightUnit = $('#weight_unit').val()
    const attachPdfUrl = url2

    $.ajax({
      type: 'PUT',
      url,
      data: {
        selected_shipping_rate_id: shippingRateId,
        shipment_email_id: shipmentEmailId,
        custom_message: customMessage,
        insure,
        insure_for_amount: insureForAmount,
        weight,
        width,
        length,
        height,
        shipment_ids: shipmentIdArray,
        carrier,
        carrier_service_type: carrierServicetype,
        buy_postage: 'walmart',
        dimension_unit: dimensionUnit,
        weight_unit: weightUnit
      },
      headers: Spree.apiV2Authentication()
    })
      .done(() => {
        this.attachPdf(url2)
      })
      .fail(msg => {
        alert(msg.responseJSON.error)
      })
  }

  attachPdf (attachPdfUrl) {
    $.ajax({
      type: 'GET',
      url: attachPdfUrl,
      headers: Spree.apiV2Authentication()
    })
      .done(() => window.location.reload())
      .fail(msg => {
        alert(msg.responseJSON.error)
      })
  }

  showDimensionError () {
    const errorDiv = document.getElementById('buy-alert')
    if (errorDiv) {
      errorDiv.textContent = 'Weight/Dimensions have been changed. Please wait until shipping rates are being refreshed.'
      errorDiv.style.display = 'block'
    }
    const refreshButton = document.querySelector('button.refresh-rates')
    if (refreshButton) {
      refreshButton.click()
    }
  }
}
