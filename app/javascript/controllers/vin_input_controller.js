import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = [
    'vin',
    'vinUpdate',
    'vrn',
    'vrd',
    'vic',
    'expiryDate',
    'quantity',
    'packSize',
    'expiryType',
    'expiryField'
  ]

  connect () {
    this.vinTargets.forEach((e) => { e.addEventListener('input', this.inputVin.bind(this)) })
  }

  inputVin (e) {
    if (this.timer) clearTimeout(this.timer)
    const vin = e.target.value
    if (!vin) return

    this.timer = setTimeout(() => this.searchVin(vin, e.target), 250)
  }

  searchVin (vin, currentVin) {
    const pageLoading = document.getElementById('page-loading')
    pageLoading.style.display = 'flex'
    pageLoading.classList.remove('d-none')

    fetch(`/old_admin/stock_item_units/pricecompare_info?vin=${vin}`)
      .then(response => response.json())
      .then(data => {
        document.getElementById('page-loading').style.display = 'none'
        const flashType = data.success ? 'success' : 'error'
        window.show_flash(flashType, data.message)
        if (flashType === 'error') {
          return
        }

        const vinIndex = this.vinTargets.indexOf(currentVin)
        if (this.vicTargets[vinIndex] && data.vendor_inventory_cost) {
          this.vicTargets[vinIndex].value = data.vendor_inventory_cost
        }

        if (this.vrnTargets[vinIndex] && data.vendor_receipt_number) {
          this.vrnTargets[vinIndex].value = data.vendor_receipt_number
        }

        if (this.vrdTargets[vinIndex] && data.vendor_receipt_date) {
          this.vrdTargets[vinIndex].value = data.vendor_receipt_date
        }

        if (this.expiryDateTargets[vinIndex] && data.expiry_date) {
          this.expiryDateTargets[vinIndex].value = data.expiry_date
        }

        if (this.quantityTargets[vinIndex] && data.quantity) {
          this.quantityTargets[vinIndex].value = data.quantity
        }

        if (this.packSizeTargets[vinIndex] && data.pack_size) {
          this.packSizeTargets[vinIndex].value = data.pack_size
        }

        this.createVinInfoFields(currentVin, data)
      })
  }

  createVinInfoFields (currentVin, data) {
    const currentValue = currentVin.value
    const vendorInfoField = document.getElementById('vendor_info_field')
    const formButtons = currentVin.nextElementSibling.children
    const id = formButtons[formButtons.length - 1].dataset.stockItemUnitId
    const form = document.getElementById('unit_stock_item_form')
    const initialValue = formButtons[formButtons.length - 1].dataset.initialValue
    const fieldType = formButtons[formButtons.length - 1].dataset.vendorInfoName
    vendorInfoField.name = `stock_item_unit[${fieldType}]`
    const vinInfoFields = [`vendor_receipt_number_field_${id}`, `vendor_receipt_date_field_${id}`, `vendor_inventory_cost_field_${id}`, `expiry_date_field_${id}`]

    this.createHiddenFields(vinInfoFields, data)
    form.action = `/old_admin/stock_item_units/${id}?${fieldType}=${initialValue}&field_type=${fieldType}&vin_scanned=true`
    vendorInfoField.value = currentValue
    vendorInfoField.nextElementSibling.click()

    this.removeHiddenFields(vinInfoFields)
  }

  removeHiddenFields (vinInfoFields) {
    vinInfoFields.forEach((fieldId) => {
      const existingVendorInfoField = document.querySelector(`input[type="hidden"][name="stock_item_unit[${fieldId.match(/(.+)_field/)[1]}]"]`)
      existingVendorInfoField.parentNode.removeChild(existingVendorInfoField)
    })
  }

  createHiddenFields (vinInfoFields, data) {
    vinInfoFields.forEach((fieldId) => {
      const newFormFieldValue = data[fieldId.match(/(.+)_field/)[1]]
      if (newFormFieldValue) {
        const hiddenField = document.createElement('input')
        hiddenField.type = 'hidden'
        hiddenField.name = 'stock_item_unit[' + `${fieldId.match(/(.+)_field/)[1]}` + ']'
        hiddenField.value = `${newFormFieldValue}`

        const form = document.getElementById('unit_stock_item_form')

        // Appending the hidden field to the form
        form.appendChild(hiddenField)
      }
    })
  }

  focusVinField (e) {
    const vinFields = document.getElementsByClassName('scan-vin-field')
    for (let i = 0; i < vinFields.length; i++) {
      vinFields[i].parentElement.classList.remove('active-green')
      vinFields[i].parentElement.classList.add('inactive')
      vinFields[i].nextElementSibling.children[0].children[0].removeAttribute('stroke')
    }
    event.currentTarget.parentElement.classList.remove('inactive')
    event.currentTarget.parentElement.classList.add('active-green')
    event.currentTarget.nextElementSibling.children[0].children[0].setAttribute('stroke', 'rgb(20, 174, 92)')
  }

  focusVinButton (e) {
    const vinFields = document.getElementsByClassName('scan-vin-field')
    for (let i = 0; i < vinFields.length; i++) {
      vinFields[i].parentElement.classList.remove('active-green')
      vinFields[i].parentElement.classList.add('inactive')
      vinFields[i].nextElementSibling.children[0].children[0].removeAttribute('stroke')
    }
    event.currentTarget.parentElement.parentElement.classList.remove('inactive')
    event.currentTarget.parentElement.parentElement.classList.add('active-green')
    event.currentTarget.children[0].setAttribute('stroke', 'rgb(20, 174, 92)')
  }

  scanVin (e) {
    const vinFields = document.getElementsByClassName('scan-vin-field')
    for (let i = 0; i < vinFields.length; i++) {
      vinFields[i].parentElement.classList.remove('active-green')
      vinFields[i].parentElement.classList.add('inactive')
      vinFields[i].nextElementSibling.children[0].classList.remove('d-none')
      vinFields[i].removeAttribute('style')
      vinFields[i].classList.add('border-right-0')
      vinFields[i].nextElementSibling.children[0].children[0].removeAttribute('stroke')
    }
    vinFields[0].parentElement.classList.remove('inactive')
    vinFields[0].parentElement.classList.add('active-green')
    vinFields[0].nextElementSibling.children[0].children[0].setAttribute('stroke', 'rgb(20, 174, 92)')
  }

  revertVendorInfo (event) {
    const uniqueField = event.currentTarget.dataset.field
    const field = document.getElementsByClassName(uniqueField)[0]
    field.value = field.dataset.initialValue
  }

  saveVendorInfo (event) {
    const previousElementSibling = event.currentTarget.parentElement.previousElementSibling
    const form = document.getElementById('unit_stock_item_form')
    const vendorInfoField = document.getElementById('vendor_info_field')
    vendorInfoField.name = `stock_item_unit[${event.currentTarget.dataset.vendorInfoName}]`
    const currentValue = vendorInfoField.name === 'stock_item_unit[expiry_date]'
      ? previousElementSibling.previousElementSibling.value
      : previousElementSibling.value
    if (previousElementSibling.id === 'stock_item_unit_vendor_inventory_number') {
      this.searchVin(currentValue, previousElementSibling)
    }
    vendorInfoField.value = currentValue
    if (vendorInfoField.name === 'stock_item_unit[expiry_date]') {
      const index = this.expiryDateTargets.indexOf(previousElementSibling.previousElementSibling)
      const expiryType = document.createElement('input')
      expiryType.type = 'hidden'
      expiryType.name = 'stock_item_unit[expiry_type]'
      expiryType.value = this.expiryTypeTargets[index].value
      form.appendChild(expiryType)
    }
    const id = event.currentTarget.dataset.stockItemUnitId
    const initialValue = event.currentTarget.dataset.initialValue
    const fieldType = event.currentTarget.dataset.vendorInfoName
    form.action = `/old_admin/stock_item_units/${id}?${fieldType}=${initialValue}&field_type=${fieldType}`
    vendorInfoField.nextElementSibling.click()
  }

  cancelVendorInfo (event) {
    event.preventDefault()
    const closestTd = event.currentTarget.closest('td')
    closestTd.children[0].children[0].value = event.currentTarget.dataset.initialValue
    closestTd.classList.add('d-none')
    closestTd.previousElementSibling.classList.remove('d-none')
  }

  editStock (event) {
    event.preventDefault()
    event.currentTarget.parentElement.classList.add('d-none')
    event.currentTarget.parentElement.nextElementSibling.classList.remove('d-none')
    event.currentTarget.parentElement.nextElementSibling.children[0].classList.remove('d-none')
  }

  updateExpiryDateField (event) {
    if (event.target.type === 'date') {
      const index = this.expiryDateTargets.indexOf(event.target)
      this.expiryFieldTargets[index].classList.add('d-none')
      this.expiryAvailableTargets[index].checked = true
      this.expiryAvailableTargets[index].setAttribute('checked', 'checked')
    } else {
      if (!event.target.checked) {
        const index = this.expiryAvailableTargets.indexOf(event.target)
        this.expiryFieldTargets[index].classList.remove('d-none')
        this.expiryAvailableTargets[index].value = '0'
      } else {
        const index = this.expiryAvailableTargets.indexOf(event.target)
        this.expiryFieldTargets[index].classList.add('d-none')
        this.expiryAvailableTargets[index].value = '1'
      }
    }
  }
}
