import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  connect () {
  }

  toggleVisibility (elementId, show) {
    const element = document.getElementById(elementId)
    if (show) {
      element.classList.add('d-flex')
      element.classList.remove('d-none')
    } else {
      element.classList.remove('d-flex')
      element.classList.add('d-none')
    }
  }

  editAction (event, elementId) {
    event.currentTarget.classList.add('d-none')
    this.toggleVisibility(elementId, true)
  }

  cancelAction (event, buttonId, elementId) {
    document.getElementById(buttonId).classList.remove('d-none')
    this.toggleVisibility(elementId, false)
  }

  saveAction (event, dataKey, inputId) {
    const orderId = event.currentTarget.dataset.orderId
    const data = {}
    data[dataKey] = document.getElementById(inputId).value
    const url = '/api/v2/platform/orders/' + orderId
    const hdr = Spree.apiV2Authentication() /* global Spree */
    hdr['Content-Type'] = 'application/json'
    fetch(url, {
      method: 'PATCH',
      headers: hdr,
      body: JSON.stringify({ order: data })
    }).then(response => {
      if (response.ok) {
        Turbo.visit(window.location.href)
      }
    })
  }

  editEnvelopeFee (event) {
    document.getElementById('envelope_fee').classList.remove('read-only-input')
    this.editAction(event, 'edit-envelope-actions')
  }

  cancelEnvelopeFee (event) {
    document.getElementById('envelope_fee').classList.add('read-only-input')
    this.cancelAction(event, 'edit-envelope-button', 'edit-envelope-actions')
    document.getElementById('envelope_fee').value = event.currentTarget.dataset.value
  }

  saveEnvelopeFee (event) {
    event.preventDefault()
    this.saveAction(event, 'envelope_fee', 'envelope_fee')
    document.getElementById('envelope_fee').classList.add('read-only-input')
  }

  editComplimentaryItems (event) {
    document.getElementById('complimentary_items').classList.remove('read-only-input')
    this.editAction(event, 'edit-complimentary-actions')
  }

  cancelComplimentaryItems (event) {
    this.cancelAction(event, 'edit-complimentary-button', 'edit-complimentary-actions')
    document.getElementById('complimentary_items').classList.add('read-only-input')
    document.getElementById('complimentary_items').value = event.currentTarget.dataset.value
  }

  saveComplimentaryItems (event) {
    this.saveAction(event, 'complimentary_items', 'complimentary_items')
    document.getElementById('complimentary_items').classList.add('read-only-input')
  }

  limitDecimalPlaces (event) {
    if (event.currentTarget.value.includes('.')) {
      const parts = event.currentTarget.value.split('.')
      if (parts[1].length > 5) {
        parts[1] = parts[1].slice(0, 5)
        event.currentTarget.value = parts.join('.')
      }
    }
  }
}
