import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['content', 'button', 'checkbox', 'dropdown']

  static openDropDown = null

  connect () {
    document.addEventListener('click', this.handleOutsideClick.bind(this))
    this.element.addEventListener('change', this.handleDropdownChange.bind(this))
  }

  toggle (event) {
    const content = this.contentTarget
    if (Controller.openDropDown && Controller.openDropDown !== this) {
      Controller.openDropDown.close()
    }

    const isCurrentlyOpen = content.style.display === 'block'
    content.style.display = isCurrentlyOpen ? 'none' : 'block'
    Controller.openDropDown = isCurrentlyOpen ? null : this
  }

  close () {
    const content = this.contentTarget
    if (content) {
      content.style.display = 'none'
    }
  }

  handleOutsideClick (event) {
    if (!event.target.closest('.custom-ebay-dropdown')) {
      if (Controller.openDropDown) {
        Controller.openDropDown.close()
        Controller.openDropDown = null
      }
    }
  }

  handleDropdownChange (event) {
    if (event.target.type === 'checkbox') {
      this.checkboxTargets.forEach(notSetCheckbox => {
        if (notSetCheckbox.value === 'none') {
          const dropdownContent = notSetCheckbox.closest('.dropdown-content')
          const anyOtherChecked = Array.from(dropdownContent.querySelectorAll('input[type="checkbox"]'))
            .some(cb => cb !== notSetCheckbox && cb.checked)
          if (anyOtherChecked) {
            notSetCheckbox.checked = false
          }
        }
      })
      this.close()
    }
  }
}
