import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['form']

  connect () {}

  search () {
    document.getElementById('scan_vin_image').classList.add('d-none')
    clearTimeout(this.timeout)
    this.timeout = setTimeout(() => {
      this.formTarget.requestSubmit()
    }, 500)
  }

  submitVin () {
    this.formTarget.requestSubmit()
  }

  setStockLocationValue (event) {
    const searchVinResultsDiv = document.getElementById('search_vin_results')
    if (searchVinResultsDiv.childElementCount > 0) {
      const variantIdValue = $('#vin_variant_id')[0].value
      $('.variant-id-vin-modal').val(variantIdValue)
      if ($('#vin_stock_location_id').val()) {
        const stockLocationValue = $('#vin_stock_location_id')[0].value
        $('.stock-location-vin-modal').val(stockLocationValue)

        const stockAddressValue = $('#vin_stock_address_id')[0].value
        $('.stock-address-vin-modal').val(stockAddressValue)
      } else {
        $('#stock_address_alert').text('Please select a stock Address!')
        event.preventDefault()
      }
    } else {
      document.getElementById('scan_vin_image').classList.add('d-none')
      setTimeout(() => { document.getElementById('scan_vin_image').classList.remove('d-none') }, 3000)
      document.getElementById('modal_highlight').firstElementChild.innerHTML = 'Please scan a VIN code!'
      setTimeout(() => { document.getElementById('modal_highlight').firstElementChild.innerHTML = '' }, 3000)
      event.preventDefault()
    }
  }

  addNextNode () {
    const div = $('#search_vin_results')[0]
    const tables = div.querySelectorAll('table')
    const bFillBlank = true

    const insertRow = (table, atFront = false) => {
      const clonedTable = table.cloneNode(true)
      const lastRow = clonedTable.querySelector('tr')
      lastRow.querySelectorAll('input').forEach(input => {
        input.name = 'stock_items[' + new Date().getTime().toString() + ']' + input.dataset.vinInputNameTarget
      })
      if (atFront) {
        div.insertBefore(clonedTable, div.firstElementChild)
      } else {
        div.appendChild(clonedTable)
      }
    }

    const fillPrevious = (atFront = false) => {
      const table = div.querySelectorAll('table')[atFront ? 0 : tables.length - 1]
      insertRow(table, atFront)
    }

    const fillBlankEntry = (atFront = false) => {
      const template = document.getElementById('stock-vin-value-template')
      const table = template.querySelectorAll('table')[0]
      insertRow(table, atFront)
    }

    if (tables.length !== 0) {
      bFillBlank ? fillBlankEntry(true) : fillPrevious(true)
    } else {
      document.getElementById('submit-vin-value').click()
      document.getElementById('scan_vin_image').classList.add('d-none')
      setTimeout(() => { document.getElementById('modal_highlight').firstElementChild.innerHTML = 'You can scan more VIN codes' }, 3000)
    }
  }

  buttonVisibility (event) {
    const buttonId = event.currentTarget.id
    const button = $(`#${buttonId}`)

    if (!button.length) {
      return
    }

    const preventDefault = button.hasClass('disabled')
    if (buttonId === 'add_stock_button' && preventDefault) {
      event.preventDefault()
    }

    button.addClass('disabled')
  }
}
