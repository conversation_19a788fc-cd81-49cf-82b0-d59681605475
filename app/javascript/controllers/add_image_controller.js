import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = [
    'orderId',
    'imageToRemove'
  ]

  connect () {}

  saveImages (e) {
    const resourceId = this.orderIdTarget.value
    const userToken = e.target.dataset.token
    const formData = new FormData()
    const files = e.target.files // Access the selected files from the event

    // Iterate through the selected files and append them to the formData object
    for (let i = 0; i < files.length; i++) {
      formData.append(`file_${i}`, files[i])
    }

    $.ajax({
      url: '/api/v2/storefront/image_upload/successful_upload' + '?resource_id=' + resourceId + '&user_token=' + userToken + '&resource=order',
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      success: function (response) {
        window.location.reload()
      },
      error: function (xhr, status, error) {}
    })
  }

  deleteImage (e) {
    const imageLabel = e.target.closest('.label_for_img')
    const imageId = imageLabel.dataset.imageId
    const resourceId = imageLabel.dataset.resourceId
    const resource = imageLabel.dataset.resource
    const pageLoading = document.getElementById('page-loading')
    pageLoading.style.display = 'flex'
    pageLoading.classList.remove('d-none')

    $.ajax({
      url: '/api/v2/storefront/image_upload/remove_image' + '?resource_id=' + resourceId + '&resource=' + resource + '&image_id=' + imageId,
      type: 'DELETE',
      success: function (response) {
        pageLoading.style.display = 'none'
        window.show_flash('success', response.message)
        document.getElementById('spree_attachment_' + imageId).remove()
      },
      error: function (xhr) {
        pageLoading.style.display = 'none'
        window.show_flash('error', xhr.responseJSON.error)
      }
    })
  }

  disconnect () {}
}
