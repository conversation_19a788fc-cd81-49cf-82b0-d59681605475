import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  connect () {
    $('#recommended_price_auto_update_period').select2()
    if (!$('#recommended_price_auto_update').prop('checked')) {
      $('#recommended_price_auto_update_period').prop('disabled', true)
    }
  }

  auto_update (e) {
    $('#recommended-prices .btn-save').removeClass('d-none')
    if (e.target.checked) {
      $('#recommended_price_auto_update_period').prop('disabled', false)
    } else {
      $('#recommended_price_auto_update_period').prop('disabled', true)
    }
  }
}
