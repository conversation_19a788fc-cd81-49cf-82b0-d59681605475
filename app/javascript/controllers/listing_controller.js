import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  connect () {
    if (this.isWalmartListingForm()) {
      const listingDataElement = document.getElementById('id_listing_data')
      const listingData = JSON.parse(listingDataElement.value)

      this.listing = listingData.listing
      this.loadSaleChannelHash = true
      this.saleChannelHash = listingData.listing.sale_channel_hash
      this.variants = listingData.variant_id_and_option_values
      this.variantsInventory = listingData.walmart_inventories
      let walmartImages = listingData.walmart_images || {}
      this.isFirstLoad = walmartImages.is_first_load || false
      this.walmartImages = walmartImages.images || {}

      this.isDateFields = ['releaseDate', 'startDate', 'endDate']
      this.isMultilineFields = ['keyFeatures']

      this.rules = {
        create_new_item: {
          forceRequireFields: [],
          draft: {
            disabledFields: [],
            enabledFields: [],
          },
          else: {
            disabledFields: ["sku", "productId", "productIdType"],
            enabledFields: [],
          }
        },
        add_by_search: {
          draft: {
            disabledFields: [],
            enabledFields: [],
          },
          else: {
            disabledFields: [],
            enabledFields: ['price'],
          }
        },
      }

      this.input_fiels_rules = []

      this.priceExample = document.getElementById("id_price_input_example")
      this.imageExample = document.getElementById("id_images_example")
      this.deleteIconExample = document.getElementById("id_delete_icon_example")

      let fileds_rules = document.createElement('input')
      fileds_rules.hidden = true
      fileds_rules.id = "id_input_fiels_rules"
      this.deleteIconExample.parentElement.appendChild(fileds_rules)


      this.updatePage()
    }
  }

  updateEnabledFields () {
    let enabledFields = ['price'] // The Enabled fields can be edited (Only be used for Orderable & Visible part)

    if (this.isDraftListingForm()) {
      enabledFields = undefined // it's means enable all the fields
    }
    else {
      if (!this.isAddingItemByMatching()) enabledFields = undefined
    }

    this.enabledFields = enabledFields
    return enabledFields
  }

  updatePage () {
    const obj = document.getElementById('product_type_select')
    // If we want to create a new item which is not existing on Walmart, then we should display all the fields, so set visbleFields as undefined
    let visbleFields = ['sku', 'ShippingWeight', 'price', 'productIdentifiers', 'condition'] // Display these fields
    let enabledFields = ['price'] // The Enabled fields can be edited (Only be used for Orderable & Visible part)
    if (this.isDraftListingForm()) {
      enabledFields = undefined // it's means enable all the fields
      this.enabledFields = undefined
      document.getElementById('add_item_method_select').disabled = false
      this.setCategoryDisabled(false)
    } else {
      if (!this.isAddingItemByMatching()) enabledFields = undefined
      this.enabledFields = enabledFields
      document.getElementById('add_item_method_select_div').parentElement.parentElement.parentElement.hidden = true
      // Disable product_type_select & category_select & product_group_select & product_type_select
      document.getElementById('add_item_method_select').disabled = true
      this.setCategoryDisabled(true)
    }
    if (!this.isAddingItemByMatching()) {
      visbleFields = undefined // it's means display all the fields
      let disable = !this.isDraftListingForm() ? true : false
      this.setCategoryDisabled(disable)
    } else {
      this.setCategoryDisabled(true)
    }

    document.getElementById('id_walmart_category').hidden = (['InProgress', 'draft'].includes(this.listing.status) && this.isAddingItemByMatching())

    this.updateEnabledFields()
    if (obj && obj.value) this.requestGetItemSpec(obj.value, visbleFields, enabledFields)
  }

  updateCategoryDisabled () {
    if (this.isDraftListingForm()) {
      this.setCategoryDisabled(false)
    } else {
      document.getElementById('add_item_method_select_div').parentElement.parentElement.parentElement.hidden = true
      // Disable product_type_select & category_select & product_group_select & product_type_select
      document.getElementById('add_item_method_select').disabled = true
      this.setCategoryDisabled(true)
    }
    if (!this.isAddingItemByMatching()) {
      let disable = !this.isDraftListingForm() ? true : false
      this.setCategoryDisabled(disable)
    } else {
      this.setCategoryDisabled(true)
    }
  }

  setCategoryDisabled (disabled) {
    document.getElementById('category_select').disabled = disabled
    document.getElementById('product_group_select').disabled = disabled
    document.getElementById('product_type_select').disabled = disabled
  }

  hideAndShow (event) {
    const div = $('#pack_size_div')

    if (!event.target.checked) {
      div.removeClass('d-block')
      div.addClass('d-none')
    } else {
      div.removeClass('d-none')
      div.addClass('d-block')
    }
  }

  hideandshowsubscription (event) {
    const elementId = event.currentTarget.id
    const div = $(`#${elementId}_div`)
    const index = event.target.dataset.index
    const variantId = event.target.dataset.variantId
    const radioName = `listing[subscription_details][${variantId}][discount_type]`
    const selectedDiscountType = document.querySelector(`input[name="${radioName}"]:checked`).value
    const variantPrice = +$(`#listing_price_${index}`).val()
    const firstDiscountId = `first_${variantId}_discount`
    const firstDiscountVal = +$(`#${firstDiscountId}`).val()
    const recurringDiscountId = `recurring_${variantId}_discount`
    const recurringDiscountVal = +$(`#${recurringDiscountId}`).val()
    let firstDiscountedPrice
    let RecurringDiscountedPrice
    if (selectedDiscountType == 'Percent off discount') {
      firstDiscountedPrice = parseFloat(variantPrice - ((variantPrice * firstDiscountVal) / 100))
      RecurringDiscountedPrice = parseFloat(variantPrice - ((variantPrice * recurringDiscountVal) / 100))   
    } else {
      firstDiscountedPrice = parseFloat(variantPrice - firstDiscountVal)
      RecurringDiscountedPrice = parseFloat(variantPrice - recurringDiscountVal)
    }

    if (!event.target.checked) {
      div.removeClass('d-block')
      div.addClass('d-none')
    } else {
      div.removeClass('d-none')
      div.addClass('d-block')
      $(`#first_${variantId}_price_per_item`).html(firstDiscountedPrice)
      $(`#first_${variantId}_price_per_item_field`).val(firstDiscountedPrice)
      $(`#recurring_${variantId}_price_per_item`).html(RecurringDiscountedPrice)
      $(`#recurring_${variantId}_price_per_item_field`).val(RecurringDiscountedPrice)
    }
  }

  hideAndShowOfferPrice (event) {
    const minimumOfferPriceDiv = $('#minimum_offer_price')
    const autoacceptOfferPriceDiv = $('#autoaccept_offer_price')

    if (!event.target.checked) {
      minimumOfferPriceDiv.removeClass('d-block')
      minimumOfferPriceDiv.addClass('d-none')
      autoacceptOfferPriceDiv.removeClass('d-block')
      autoacceptOfferPriceDiv.addClass('d-none')
    } else {
      minimumOfferPriceDiv.removeClass('d-none')
      minimumOfferPriceDiv.addClass('d-block')
      autoacceptOfferPriceDiv.removeClass('d-none')
      autoacceptOfferPriceDiv.addClass('d-block')
    }
  }

  inputValueKeepDigits (event) {
    const el = event.srcElement
    return !/^(\d+\.?)?\d{0,2}$/.test(el.value) ? (el.value = el.value.substring(0, el.value.length - 1)) : ''
  }

  removeImage (event) {
    event.preventDefault()
    const confirmed = window.confirm('Are you sure you want to remove the listing image?')
    if (confirmed) {
      const listingId = event.currentTarget.dataset.listingId
      const filename = event.currentTarget.dataset.filename
      const variantId = event.currentTarget.dataset.variantId
      fetch(`/old_admin/listings/${listingId}/remove_images?image_name=${encodeURIComponent(filename)}&variant_id=${variantId}`)
        .then(response => response.json())
        .then(data => {
          const flashType = data.success ? 'success' : 'error'
          if (data.success) {
            const variantId = data.variant_id
            const previewContainer = document.getElementById(`product-image-preview-${variantId}`)
            const filename = data.filename
            const imgContainer = document.getElementById(`image-container-${filename}`)
            previewContainer.removeChild(imgContainer)
            console.log('Image deleted successfully')
            window.show_flash(flashType, data.message)
          } else {
            window.show_flash(flashType, data.error)
          }
        })
        .catch(error => {
          console.error('Error:', error)
        })
    } else {
      console.log('Removal canceled')
    }
  }

  calculate (event) {
    const variantId = event.target.dataset.variantId
    const radioName = `listing[subscription_details][${variantId}][discount_type]`
    const selectedDiscountType = document.querySelector(`input[name="${radioName}"]:checked`).value
    const index = event.target.dataset.index

    const elementId = event.currentTarget.id
    let elementValue = +$(`#${elementId}`).val() // which is in percentage
    const listingPrice = +$(`#listing_price_${index}`).val()
    if (selectedDiscountType === 'Percent off discount') {
      if (elementValue > 99) {
        $('#' + elementId).val(parseInt(elementValue.toString().slice(0, 2), 10))
        elementValue = parseInt(elementValue.toString().slice(0, 2), 10)
      } else if (elementValue < 5) {
        $('#' + elementId).val(5)
        elementValue = 5
      }
      const discountedPrice = parseFloat(listingPrice - ((listingPrice * elementValue) / 100))
      // inserting discounted price value in price per item input box
      $(`#${(event.currentTarget.id).split('_')[0]}_${variantId}_price_per_item`).html(discountedPrice)
      $(`#${(event.currentTarget.id).split('_')[0]}_${variantId}_price_per_item_field`).val(discountedPrice)
    } else {
      if (elementValue >= listingPrice) {
        $('#' + elementId).val(listingPrice - 1)
        elementValue = listingPrice - 1
      } else if (elementValue < 1) {
        $('#' + elementId).val(1)
        elementValue = 1
      }
      $(`#${(event.currentTarget.id).split('_')[0]}_${variantId}_price_per_item`).html(listingPrice - elementValue)
      $(`#${(event.currentTarget.id).split('_')[0]}_${variantId}_price_per_item_field`).val(listingPrice - elementValue)
    }
  }

  recalculate (event) {
    const index = event.target.dataset.index
    const toggle = $(`#subscription_toggle_${index}`).val()
    if (toggle == 'true' || toggle == true) {
      const variantPrice = +$(`#listing_price_${index}`).val()
      const variantId = event.target.dataset.variantId
      const radioName = `listing[subscription_details][${variantId}][discount_type]`
      const selectedDiscountType = document.querySelector(`input[name="${radioName}"]:checked`).value
      const firstDiscountId = `first_${variantId}_discount`
      const firstDiscountVal = +$(`#${firstDiscountId}`).val()
      const recurringDiscountId = `recurring_${variantId}_discount`
      const recurringDiscountVal = +$(`#${recurringDiscountId}`).val()
      let firstDiscountedPrice
      let RecurringDiscountedPrice
      if (selectedDiscountType == 'Percent off discount') {
        firstDiscountedPrice = parseFloat(variantPrice - ((variantPrice * firstDiscountVal) / 100))
        RecurringDiscountedPrice = parseFloat(variantPrice - ((variantPrice * recurringDiscountVal) / 100))   
      } else {
        firstDiscountedPrice = parseFloat(variantPrice - firstDiscountVal)
        RecurringDiscountedPrice = parseFloat(variantPrice - recurringDiscountVal)
      }
      $(`#first_${variantId}_price_per_item`).html(firstDiscountedPrice)
      $(`#first_${variantId}_price_per_item_field`).val(firstDiscountedPrice)
      $(`#recurring_${variantId}_price_per_item`).html(RecurringDiscountedPrice)
      $(`#recurring_${variantId}_price_per_item_field`).val(RecurringDiscountedPrice)
    }
  }

  setInputBoxIcon (event) {
    const newName = event.currentTarget.value === 'Percent off discount' ? 'percentage.svg' : 'currency.svg'
    const variantId = +event.currentTarget.dataset.target
    const url = `/old_admin/listings/fetch_icon/?name=${encodeURIComponent(newName)}&variantId=${variantId}`

    fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'text/vnd.turbo-stream.html'
      }
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }
        return response.text().then((html) => ({ html, event }))
      })
      .then(({ html, event }) => {
        const parser = new DOMParser()
        const doc = parser.parseFromString(html, 'text/html')
        const turboStreams = doc.body.querySelectorAll('turbo-stream')
        const selectedRadioButton = event.target.value
        const index = event.target.dataset.index
        const variantId = +event.target.dataset.target
        const recurringCheckBox = `listing[subscription_details][${variantId}][recurring_discount_checkbox]`
        const firstTimeDiscount = `listing[subscription_details[${variantId}][first_time_discount]]`
        const firstTimeDiscountInput = document.querySelector(`input[name="${firstTimeDiscount}"]`)
        const listingPrice = +$(`#listing_price_${index}`).val()
        const recurringDiscountDiv = document.querySelector('.span-recurring-discount-' + variantId)
        const targetElement = document.getElementById('recurring_' + variantId + '_discount')
        const discountType = targetElement.dataset.discountType
        const recurringDiscountInitialValue = targetElement.dataset.recurringDiscount
        const firstTimeDiscountInitialvalue = targetElement.dataset.firstTimeDiscount

        turboStreams.forEach((turboStream) => {
          if (selectedRadioButton === 'Dollar off discount') {
            targetElement.disabled = false
            firstTimeDiscountInput.min = 1
            firstTimeDiscountInput.max = listingPrice
            document.querySelector(`input[name="${recurringCheckBox}"].recurring_discount_checkbox_${variantId}`).disabled = false
            firstTimeDiscountInput.value = 1
            $(`#${firstTimeDiscountInput.id.split('_')[0]}_${variantId}_price_per_item`).html(listingPrice - 1.0)
            $(`#first_${variantId}_price_per_item_field`).val(listingPrice - 1.0)
            $(`#${targetElement.id.split('_')[0]}_${variantId}_price_per_item`).html(listingPrice - targetElement.value)
            $(`#recurring_${variantId}_price_per_item_field`).val(listingPrice - targetElement.value)

            if (discountType === 'Dollar off discount') {
              $(`#first_${variantId}_price_per_item_field`).val(listingPrice - firstTimeDiscountInitialvalue)
              $(`#recurring_${variantId}_price_per_item_field`).val(listingPrice - recurringDiscountInitialValue)
              targetElement.value = recurringDiscountInitialValue
              targetElement.min = firstTimeDiscountInitialvalue
              targetElement.max = listingPrice
              firstTimeDiscountInput.value = firstTimeDiscountInitialvalue
              firstTimeDiscountInput.max = listingPrice
              $(`#${firstTimeDiscountInput.id.split('_')[0]}_${variantId}_price_per_item`).html(listingPrice - firstTimeDiscountInitialvalue)
            }
          } else {
            targetElement.disabled = false
            recurringDiscountDiv.style.backgroundColor = ''
            recurringDiscountDiv.style.color = ''
            document.querySelector(`input[name="${recurringCheckBox}"].recurring_discount_checkbox_${variantId}`).disabled = false
            firstTimeDiscountInput.value = 5
            firstTimeDiscountInput.min = 5
            firstTimeDiscountInput.max = 99
            targetElement.value = 6
            targetElement.min = 6
            targetElement.max = 99
            $(`#${firstTimeDiscountInput.id.split('_')[0]}_${variantId}_price_per_item`).html(5)
            let discountedPrice = parseFloat(listingPrice - ((listingPrice * firstTimeDiscountInput.value) / 100))
            $(`#first_${variantId}_price_per_item_field`).val(discountedPrice)
            $(`#${firstTimeDiscountInput.id.split('_')[0]}_${variantId}_price_per_item`).html(discountedPrice)
            let recurringDiscountedPrice = parseFloat(listingPrice - ((listingPrice * targetElement.value) / 100))
            $(`#recurring_${variantId}_price_per_item_field`).val(recurringDiscountedPrice)
            $(`#${targetElement.id.split('_')[0]}_${variantId}_price_per_item`).html(recurringDiscountedPrice)

            if (discountType !== 'Dollar off discount') {
              firstTimeDiscountInput.value = firstTimeDiscountInitialvalue
              targetElement.value = +recurringDiscountInitialValue
              targetElement.min = 1
              targetElement.max = 99
              discountedPrice = parseFloat(listingPrice - ((listingPrice * firstTimeDiscountInput.value) / 100))
              $(`#first_${variantId}_price_per_item_field`).val(discountedPrice)
              $(`#${firstTimeDiscountInput.id.split('_')[0]}_${variantId}_price_per_item`).html(discountedPrice)
              recurringDiscountedPrice = parseFloat(listingPrice - ((listingPrice * targetElement.value) / 100))
              $(`#recurring_${variantId}_price_per_item_field`).val(recurringDiscountedPrice)
              $(`#${targetElement.id.split('_')[0]}_${variantId}_price_per_item`).html(recurringDiscountedPrice)
            }
          }

          document.documentElement.appendChild(turboStream)
        })
      })
      .catch((error) => {
        console.error('Fetch error:', error)
      })
  }

  listingIssues (event) {
    const listingId = event.currentTarget.dataset.listingId
    const url = `/old_admin/listings/${listingId}/listing_issues`
    fetch(url)
      .then(response => response.text())
      .then(html => {
        document.getElementById('modal-content').innerHTML = html
        $('#listing-issue-modal').modal('show') // Use Bootstrap's modal API (optional)
      })
  }

  isAddingItemByMatching () {
    const addItemMethodSelect = document.getElementById('add_item_method_select')
    return addItemMethodSelect.value === 'match_and_add'
  }

  isCreateNewItem () {
    const addItemMethodSelect = document.getElementById('add_item_method_select')
    return !this.isAddingItemByMatching()
  }

  switchAddItemMethod (event) {
    event.preventDefault()

    this.loadSaleChannelHash = false
    this.updatePage()
  }

  getGroupProductType (event) {
    event.preventDefault()
    const productGroupSelect = document.getElementById('product_group_select')
    const productTypeGroupDiv = document.getElementById('product_group_select_div')
    const selectedCategoryId = event.currentTarget.value
    productGroupSelect.innerHTML = ''
    productGroupSelect.style.display = 'none'

    if (selectedCategoryId) {
      fetch(`/api/v2/platform/walmart_specs/product_type_groups?id=${selectedCategoryId}`)
        .then(response => response.json())
        .then(data => {
          if (data && data.length > 0) {
            // Populate product groups
            data.forEach(group => {
              const option = document.createElement('option')
              option.value = group.id
              option.text = group.name
              productGroupSelect.appendChild(option)
            })
            productGroupSelect.style.display = 'block'
            productTypeGroupDiv.classList.remove('d-none')
          }
        })
        .catch(error => console.error('Error fetching product groups:', error))
    }
  }

  getProductType (event) {
    event.preventDefault()
    // const shipNode = document.getElementById('shipNodes')
    const productTypeSelect = document.getElementById('product_type_select')
    const productTypeDiv = document.getElementById('product_type_select_div')
    const selectedGroupTypeId = event.currentTarget.value
    productTypeSelect.innerHTML = ''
    productTypeSelect.style.display = 'none'
    // shipNode.classList.add('d-none');

    if (selectedGroupTypeId) {
      fetch(`/api/v2/platform/walmart_specs/product_types?id=${selectedGroupTypeId}`)
        .then(response => response.json())
        .then(data => {
          if (data && data.length > 0) {
            // Populate product groups
            data.forEach(group => {
              const option = document.createElement('option')
              option.value = group.id
              option.text = group.name
              productTypeSelect.appendChild(option)
            })
            productTypeSelect.style.display = 'block'
            productTypeDiv.classList.remove('d-none')
          }
        })
        .catch(error => console.error('Error fetching product groups:', error))
    }
  }

  getItemSpec (event) {
    event.preventDefault()
    // const selectedproductType = event.currentTarget.value
    this.loadSaleChannelHash = false
    this.updatePage()
  }

  requestGetItemSpec (selectedproductType, requiredFields = undefined, enabledFields = undefined) {
    if (selectedproductType) {
      fetch(`/api/v2/platform/walmart_specs/item_spec?product_type_id=${selectedproductType}&feed_type=MP_ITEM`)
        .then(response => response.json())
        .then(data => {
          this.input_fiels_rules = []
          const dynamicFieldsContainer = document.getElementById('dynamic-fields')
          dynamicFieldsContainer.innerHTML = '' // Reset

          let i = 0
          this.variants.forEach(variant => {
            const hr = document.createElement('hr')
            const vairantTitle = document.createElement('h3')
            vairantTitle.textContent = `Item [${i}], Options: ` + variant.options_text
            dynamicFieldsContainer.appendChild(hr)
            dynamicFieldsContainer.appendChild(vairantTitle);

            ['Orderable', 'Visible'].forEach(dataKey => {
              this.updateFormFields(
                dynamicFieldsContainer,
                data.MPItem.items.properties[dataKey],
                dataKey,
                [variant.id, 'item_content', dataKey],
                requiredFields,
                enabledFields
              )
            })

            this.appendInventoryField(variant.id, this.variantsInventory[variant.id])
            i++
          })

          let input_fiels_rules = document.getElementById("id_input_fiels_rules")
          input_fiels_rules.value = JSON.stringify(this.input_fiels_rules)

        })
        .catch(error => console.error('Error fetching product groups:', error))
    }
  }

  loadValueFromSaleChannelHash (fieldNamePath) {
    // fieldNamePath is looke like the keys list ["parent key name", "child key name"]
    let node = this.saleChannelHash.variants
    for (let i = 0; i < fieldNamePath.length; i++) {
      const key = (typeof (fieldNamePath[i]) === 'number') ? fieldNamePath[i].toString() : fieldNamePath[i]
      if (!node[key]) {
        console.log('node[key] not found:', key)
        return undefined
      }
      node = node[key]
    }
    return node
  }

  labelAndInput (labelContent, inputName, inputType, inputId, inputPlaceholder, defaultInputValue = undefined) {
    // Create a label for the quantity field
    const quantityLabel = document.createElement('label')
    quantityLabel.setAttribute('for', inputId)
    quantityLabel.textContent = labelContent

    // Create a new input element for quantity
    const quantityField = document.createElement('input')
    quantityField.type = inputType
    quantityField.id = inputId
    quantityField.name = inputName // Adjusting the naming structure to match your form
    quantityField.placeholder = inputPlaceholder
    // quantityField.min = 1; // Minimum value for quantity
    quantityField.classList.add('form-control')
    if (defaultInputValue !== undefined) quantityField.value = defaultInputValue

    return [quantityLabel, quantityField]
  }

  appendInventoryField (vairantId, inventory) {
    if (inventory === undefined) return

    const sectionDiv = document.createElement('div')
    sectionDiv.classList.add('section', 'border', 'p-3', 'mb-4', 'rounded')

    const sectionTitle = document.createElement('h3')
    sectionTitle.textContent = 'Inventory'
    sectionTitle.classList.add('section-title', 'font-weight-bold')

    sectionDiv.appendChild(sectionTitle)

    // Create a new div container for the quantity field
    const quantityContainer = document.createElement('div')
    quantityContainer.classList.add('form-group', 'mb-3')

    // shipNodes is Array
    inventory.shipNodes.forEach(shipNode => {
      const [shipnodeLabel, shipnodeField] = this.labelAndInput(
        'Ship Node Name', // labelContent,
        `walmart[${vairantId}][inventory_content][shipNodes][][shipNode]`, // inputName,
        'string', // inputType,
        `id_varaint_${vairantId}_${shipNode.shipNode}_shipnodeField`, // inputId,
        'Ship Node', // inputPlaceholder
        shipNode.shipNode
      )
      shipnodeField.hidden = true
      shipnodeLabel.hidden = true
      quantityContainer.appendChild(shipnodeLabel)
      quantityContainer.appendChild(shipnodeField)

      const [quantityLabel, quantityField] = this.labelAndInput(
        `${shipNode.shipNodeName} - Quantity`, // labelContent,
        `walmart[${vairantId}][inventory_content][shipNodes][][quantity][inputQty]`, // inputName,
        'string', // inputType,
        `id_varaint_${vairantId}_${shipNode.shipNode}_quantityField`, // inputId,
        'Enter quantity', // inputPlaceholder
        shipNode.quantity.inputQty
      )
      // quantityField.min = 0// Minimum value for quantity
      quantityContainer.appendChild(quantityLabel)
      quantityContainer.appendChild(quantityField)
    })

    // Append the container to the form
    const form = document.getElementById('dynamic-fields') // Assuming 'dynamic-fields' is the container ID
    sectionDiv.appendChild(quantityContainer)
    form.appendChild(sectionDiv)
  }

  updateFormFields (container, data, parentTitle = '', parentKey = [], requiredFields = undefined, enabledFields = undefined) {
    let variant_id = parentKey[0]
    // let dynamicFieldsContainer = document.getElementById('dynamic-fields');
    requiredFields = (requiredFields === undefined) ? data.required : requiredFields

    if (requiredFields.length <= 0) return
    if (!this.isAddingItemByMatching()) requiredFields = requiredFields.concat(this.rules.create_new_item.forceRequireFields)

    if (parentTitle) {
      const sectionDiv = document.createElement('div')
      sectionDiv.classList.add('section', 'border', 'p-3', 'mb-4', 'rounded')

      const sectionTitle = document.createElement('h3')
      sectionTitle.textContent = parentTitle
      sectionTitle.classList.add('section-title', 'font-weight-bold')

      sectionDiv.appendChild(sectionTitle)
      container.appendChild(sectionDiv)

      // Use sectionDiv as the container for nested fields
      container = sectionDiv
    }

    const genPath = (keys) => {
      let path = 'walmart'
      keys.forEach(key => {
        path += `[${key}]`
      })
      return path
    }

    const genId = (keys) => {
      let path = 'id'
      keys.forEach(key => {
        path += `_${key}`
      })
      return path
    }

    const newLabel = (title, key) => {
      const label = document.createElement('label')
      label.textContent = title
      label.setAttribute('for', key)
      return label
    }

    // Helper function to create input or select element
    const createInputField = (key, value, parentTitle, parentKey, dataKeyPath, input_id, disabled = false, isArray = false) => {
      let input
      let isArrayEnum = false
      let rule_type = 'string'
      if (value.items != undefined && value.items.enum != undefined) isArrayEnum = true
      if (value.enum) {
        input = document.createElement('select')
        input.name = genPath(parentKey.concat(key))
        if (isArray) input.name += '[]'
        input.classList.add('form-control')

        value.enum.forEach(optionValue => {
          const option = document.createElement('option')
          option.value = optionValue
          option.textContent = optionValue
          input.appendChild(option)
        })
        input.value = value.enum[value.enum.length - 1]
      } else if (isArrayEnum) {
        input = document.createElement('select')
        input.name = genPath(parentKey.concat(key))
        if (isArray) input.name += '[]'
        input.classList.add('form-control')
        let enumValue = value.items.enum
        enumValue.forEach(optionValue => {
          const option = document.createElement('option')
          option.value = optionValue
          option.textContent = optionValue
          input.appendChild(option)
        })
        input.value = enumValue[enumValue.length - 1]
      } else {
        if (this.isMultilineFields.includes(key)) {
          input = document.createElement('textarea')
          input.rows=4
          input.cols=50
        }
        else {
          input = document.createElement('input')
          input.type = value.type === 'integer' ? 'number' : 'text'
          rule_type = value.type === 'integer' ? 'number' : 'string'
        }
        input.name = genPath(parentKey.concat(key))
        if (isArray) input.name += '[]'
        input.classList.add('form-control')
        input.placeholder = `Enter ${value.title}`
        if (this.isDateFields.includes(key)) input.placeholder += ", example: 2023-01-01T00:00:00Z"
      }

      if (dataKeyPath !== undefined) {
        const dataValue = this.loadValueFromSaleChannelHash(dataKeyPath)
        if (dataValue !== undefined) input.value = dataValue
      }
      input.disabled = disabled
      input.id = input_id

      // Add a rule for checking this field
      // Example:{id: "ShippingWeight", name: "Shipping Weight", type: "number", multiple_of: 0.001, min: 0}
      let rule = {id: input_id, name: value.title, type: rule_type}
      let value_for_rule = (value.type === 'array') ? value.items : value
      rule.type = value_for_rule.type
      rule.multiple_of = value_for_rule.multipleOf
      rule.minimum = value_for_rule.minimum
      rule.maximum = value_for_rule.maximum
      rule.min_length = value_for_rule.minLength
      rule.max_length = value_for_rule.maxLength
      rule.description = value_for_rule.description
      rule.examples = value_for_rule.examples

      this.input_fiels_rules.push(rule)

      if (dataKeyPath.at(-1) == "price") {
        let newPriceInput = this.priceExample.cloneNode(true)
        newPriceInput.hidden = false
        newPriceInput.appendChild(input)
        return newPriceInput
      } else {
        return input
      }
    }

    const clone_walmart_image_preview = (previewContainer, variantId, imageData) => {
      let outerContainer = document.createElement('div')
      outerContainer.className = "col-6 col-sm-4 col-md-3 col-lg-2 my-3"
      outerContainer.id = `image-container-${variantId}`

      let imgContainer = document.createElement('div')
      imgContainer.className = `card admin-listing-image-container`
      let inputEle = document.createElement('input')
      inputEle.value = imageData.url
      inputEle.autocomplete = "off"
      inputEle.type = "hidden"
      inputEle.name = (this.isFirstLoad == true) ? `image_files[${variantId}][]` : `image_urls[${variantId}][]`
      inputEle.id = ""//(this.isFirstLoad == true) ? `walmart_images[${variantId}][]` : `image_urls[${variantId}][]`
      imgContainer.appendChild(inputEle)

      let imgEle = document.createElement('img')
      imgEle.width = "60"
      imgEle.height = "125"
      imgEle.className = "border rounded card-img-top walmart-variant-image"
      imgEle.src = imageData.url
      imgContainer.appendChild(imgEle)

      if (this.isFirstLoad == true) {
        let deleteIconContainer = this.deleteIconExample.cloneNode(true)
        deleteIconContainer.id = undefined
        deleteIconContainer.hidden = false
        let deleteIcon = deleteIconContainer.children[0]
        deleteIcon.onclick = function () {
          previewContainer.removeChild(outerContainer)
        }
        imgContainer.appendChild(deleteIconContainer);
      }
      else
      {
        let removeCheckBox = document.createElement('div')
        removeCheckBox.className = "card-body"
        let removeCheckBoxText = document.createElement('p')
        removeCheckBoxText.className = "card-text"
        let removeCheckBoxInput = document.createElement('input')
        removeCheckBoxInput.type = "checkbox"
        removeCheckBoxInput.name = `selected_remove_images[]`
        removeCheckBoxInput.id = `selected_remove_images_${variantId}`
        removeCheckBoxInput.value = `${imageData.id}`
        removeCheckBoxText.appendChild(removeCheckBoxInput)

        removeCheckBoxText.innerHTML += "Remove image"
        removeCheckBox.appendChild(removeCheckBoxText)

        imgContainer.appendChild(removeCheckBox)
      }

      outerContainer.appendChild(imgContainer)
      return outerContainer
    }

    const clone_walmart_image = (variantId) => {
      let newImage = this.imageExample.cloneNode(true)
      let button = newImage.children[1].children[1].children[0].children[0].children[0].children[0]
      button.id = `add-image-button-${variantId}`
      button.name = `image_files[${variantId}][]`
      button.class = `float-right form-control`
      // newImage.children[1].children[1].children[0].children[0].children[0] button

      // `image-preview-${variantId}`
      newImage.hidden = false
      newImage.id = `id_images_${variantId}`

      let preview = newImage.children[2]
      preview.id = `product-image-preview-${variantId}`

      let images = this.walmartImages[variantId]
      // images is an array, loop images
      for (let image of images) {
        let newPreview = clone_walmart_image_preview(preview, variantId, image)
        preview.appendChild(newPreview)
      }

      let container = newImage.children[4]
      container.id = `image-container-${variantId}`
      container.children[0].children[0].id = `image-preview-${variantId}`
      container.children[0].children[0].children[0].id = `image-container-${variantId}`
      return newImage
    }

    const new_form_group = () => {
      let fieldDiv =document.createElement('div')
      fieldDiv.classList.add('field', 'form-group')
      return fieldDiv
    }

    let propertiesList = []
    // Iterate over the properties in the data object and create fields
    for (const [key, value] of Object.entries(data.properties)) {
      if ("productSecondaryImageURL" === key) continue
      propertiesList.push({key: key, value: value})
    }

    // loop the propertiesList
    for (const property of propertiesList) {
      const [key, value] = [property.key, property.value]
      if (!requiredFields.includes(key)) {
        continue
      }
      let disabled = false
      disabled = this.checkFieldsDisabled(key)

      // if (["has_written_warranty", "isProp65WarningRequired"].includes(key)) disabled = true // Temporary code

      let fieldDiv = new_form_group()

      const label = newLabel(value.title, key)

      let input
      let thisKeyList = parentKey.concat(key)
      switch (value.type) {
        case 'string':
        case 'integer':
        case 'number':
          fieldDiv.appendChild(label)
          if (value.title === 'Condition') value.enum = ['New']
          let input_id = genId(thisKeyList)
          input = createInputField(key, value, parentTitle, parentKey, thisKeyList, input_id, disabled, false)
          break
        case 'object':
          this.updateFormFields(container, value, value.title, thisKeyList, undefined, enabledFields)
          break
        case 'array':
          if (value.items.type === 'object') {
            this.updateFormFields(container, value.items, value.title, thisKeyList, undefined, enabledFields)
          } else {
            thisKeyList = parentKey.concat('[]', key)
            let input_id = genId(thisKeyList)
            for (let i = 0; i < value.minItems; i++) {
              if (fieldDiv === undefined) fieldDiv = new_form_group()
              fieldDiv.appendChild(newLabel(value.title + `[${i}]`, key))
              input_id = genId(parentKey.concat(i, key))
              const input = createInputField(key, value, parentTitle, parentKey, parentKey.concat(key, i), input_id, disabled, true)
              input.id = genId(parentKey.concat(i, key))
              fieldDiv.appendChild(input)
              container.appendChild(fieldDiv)
              fieldDiv = undefined
            }
            input = undefined
          }
          break
      }

      if (key == "mainImageUrl") {
        let newImage = clone_walmart_image(variant_id)
        newImage.hidden = false
        fieldDiv.appendChild(newImage)
        container.appendChild(newImage)
      }
      else {
        if (input) {
          fieldDiv.appendChild(input)
        }
        if (fieldDiv) container.appendChild(fieldDiv)
      }
    }
  }

  isDraftListingForm () {
    const draftListing = $('.draft-listing-form')
    return draftListing.length > 0
  }

  isWalmartListingForm () {
    const draftListing = $('.walmart-lisiting-form')
    return draftListing.length > 0
  }

  checkFieldsDisabled (key) {
    if (this.isCreateNewItem() && !this.isDraftListingForm()) {
      let disabledFields = this.rules.create_new_item.else.disabledFields
      if (disabledFields.length > 0) {
        return disabledFields.includes(key)
      }
      else {
        let enabledFields = this.rules.create_new_item.else.enabledFields
        if (enabledFields.length > 0) {
          return !enabledFields.includes(key)
        }
        else {
          return false
        }

      }
    }
    return this.enabledFields && !this.enabledFields.includes(key)
  }
}
