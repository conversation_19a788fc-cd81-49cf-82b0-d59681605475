import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  connect () {}

  productInactiveValidation (event) {
    const indexProductCurrentStatus = event.currentTarget.dataset.indexProductCurrentStatus
    const productId = event.currentTarget.dataset.productId
    const activeListings = event.currentTarget.dataset.activeListings
    const selectElement = document.querySelector('[data-hook="new_product_status"] select')
    let currentStatus = null
    let selectedStatus = null

    if (selectElement) {
      currentStatus = selectElement.dataset.productCurrentStatus
      selectedStatus = selectElement.selectedOptions[0].value
    }

    if (((currentStatus === 'active' && currentStatus !== selectedStatus) || (indexProductCurrentStatus === 'active')) && activeListings === 'true') {
      const userConfirmed = confirm('This product has active listings. Do you want to proceed?')

      if (userConfirmed) {
        this.endItems(productId)
      } else {
        event.preventDefault()
        event.stopPropagation()
      }
    }
  }

  endItems (productId) {
    fetch(`/old_admin/listings/batch_end_items?product_id=${productId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(resp => resp.json())
  }
}
