import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = [
    'all',
    'item',
    'disableSubmit',
    'selectCount',
    'selectedItem',
    'status'
  ]

  static values = {
    max: Number,
    maxMsg: { type: String, default: 'You can not select anymore!' }
  }

  connect () {
    // if (this.hasDisableSubmitTarget) this.disableSubmitTarget.disabled = true
    this.toggle()
    if ($('#status_in').length) {
      $('#status_in').on('select2:select', function () {
        const event = new Event('change', { bubbles: true }) // fire a native event
        this.dispatchEvent(event)
      })
    }

    if ($('#sale_channel_id_in').length) {
      $('#sale_channel_id_in').on('select2:select', function () {
        const event = new Event('change', { bubbles: true }) // fire a native event
        this.dispatchEvent(event)
      })
    }

    // explicit logic for the search through VIN
    document.addEventListener('turbo:load', function () {
      const selectAllCheckbox = document.querySelector('[data-checkbox-target="all"]')
      const checkboxTargets = document.querySelectorAll('[data-checkbox-target="item"]')
      const printSelectedButton = document.getElementById('printSelected')

      function isAnyCheckboxSelected () {
        return Array.from(checkboxTargets).some(checkbox => checkbox.checked)
      }

      function updatePrintSelectedButton () {
        printSelectedButton.disabled = !isAnyCheckboxSelected()
      }

      function handleSelectAllChange () {
        checkboxTargets.forEach(checkbox => {
          checkbox.checked = selectAllCheckbox.checked
        })
        updatePrintSelectedButton()
      }

      function handleCheckboxChange () {
        selectAllCheckbox.checked = Array.from(checkboxTargets).every(checkbox => checkbox.checked)
        updatePrintSelectedButton()
      }

      checkboxTargets.forEach(checkbox => {
        checkbox.addEventListener('change', handleCheckboxChange)
      })

      selectAllCheckbox?.addEventListener('change', handleSelectAllChange)
      updatePrintSelectedButton()
    })
  }

  disconnect () {}

  toggleAll (event) {
    event.target.setAttribute('checked', 'checked')
    const checked = this.allTarget.checked
    this.itemTargets.forEach((item) => this.toggleItem(item, checked))
    this.toggleStockItemUnit()
  }

  toggle (event) {
    event?.preventDefault()
    // if (this.isExceeded()) {
    //   if (event) event.target.checked = false
    //   window.show_flash('error', this.maxMsgValue)
    //   return
    // }
    if (event?.target) this.toggleItem(event.target, event.target.checked)

    const checked = this.itemTargets.every((item) => item.checked)
    if (this.hasAllTarget) this.allTarget.checked = checked

    if (this.hasSelectCountTarget) this.selectCountTarget.innerHTML = this.packSizeSum()

    this.handleSelectCount()
  }

  handleSelectCount () {
    if (!this.hasSelectedItemTarget) return
    if (this.hasDisableSubmitTarget && this.hasMaxValue && this.packSizeSum() === this.maxValue) {
      // this.disableSubmitTarget.disabled = false
      this.selectedItemTarget.classList.remove('orange-text')
    } else {
      // this.disableSubmitTarget.disabled = true
      this.selectedItemTarget.classList.add('orange-text')
    }
  }

  isExceeded () {
    if (!this.hasMaxValue) return false

    return this.itemTargets.filter((item) => item.checked).length > this.maxValue
  }

  select (e) {
    this.unselectAll()

    const state = e.target.dataset.state
    this.itemTargets.forEach((item) => {
      item.checked = item.dataset.state === state
    })

    this.toggle()
  }

  unselectAll () {
    this.itemTargets.forEach((item) => this.toggleItem(item, false))
    this.allTarget.checked = false
  }

  toggleItem (item, checked) {
    if (checked) {
      item.checked = true
      item.setAttribute('checked', 'checked')
      document.getElementById('printSelected')?.removeAttribute('disabled')
    } else {
      item.checked = false
      item.removeAttribute('checked')
      if (!this.itemTargets.some((item) => item.checked)) {
        document.getElementById('printSelected')?.setAttribute('disabled', 'disabled')
      }
    }
  }

  only_active () {
    const isChecked = this.element.querySelector('#only_active').checked
    const selectControl = this.statusTarget
    const activeOption = Array.from(selectControl.options).find(option => option.value === 'Active')
    if (isChecked) {
      Array.from(selectControl.options).forEach(option => {
        if (option !== activeOption) {
          option.selected = false
        }
      })

      if (!activeOption.selected) {
        activeOption.selected = true
      }

      const event = new Event('change')
      selectControl.dispatchEvent(event)
      this.statusTarget.dataset.selected = selectControl.value
    } else {
      if (activeOption.selected) {
        activeOption.selected = false
      }
      const event = new Event('change')
      selectControl.dispatchEvent(event)
      this.element.querySelector('#only_active').checked = false
    }
  }

  statusSelected () {
    const selectedOptions = Array.from(this.statusTarget.selectedOptions)
    const checkbox = this.element.querySelector('#only_active')

    if (selectedOptions.some(option => option.value !== 'Active')) {
      checkbox.checked = false
    } else {
      checkbox.checked = true
    }
  }

  toggleUnit (event) {
    let selectedUnit = parseInt(this.selectCountTarget.innerHTML, 10)
    const packSize = parseInt(event.currentTarget.dataset.packSize, 10)

    if (event.currentTarget.checked) {
      event.currentTarget.setAttribute('checked', 'checked')
      selectedUnit += packSize
    } else {
      event.currentTarget.removeAttribute('checked', 'checked')
      selectedUnit -= packSize
      // Ensure selectedUnit is not negative
      selectedUnit = Math.max(selectedUnit, 0)
    }

    this.selectCountTarget.innerHTML = selectedUnit
    this.handleSelectCount()
    if (selectedUnit > this.maxValue) {
      document.getElementById('exactUnit')?.classList?.add('d-none')
      document.getElementById('increaseUnit')?.classList?.remove('d-none')
      document.getElementById('confirm-modal-body').innerHTML = 'You have selected more quantity than the orders actual quantity'
      document.getElementById('confirm-modal-button').classList.remove('d-none')
    } else if (selectedUnit === 0) {
      document.getElementById('exactUnit')?.classList?.add('d-none')
      document.getElementById('increaseUnit')?.classList?.remove('d-none')
      document.getElementById('confirm-modal-body').innerHTML = 'Error!! Not enough quantity selected'
      document.getElementById('confirm-modal-button').classList.add('d-none')
    } else if (selectedUnit < this.maxValue) {
      document.getElementById('exactUnit')?.classList?.add('d-none')
      document.getElementById('increaseUnit')?.classList?.remove('d-none')
      document.getElementById('confirm-modal-body').innerHTML = 'You have selected less quantity than the orders actual quantity'
      document.getElementById('confirm-modal-button').classList.remove('d-none')
    } else {
      document.getElementById('exactUnit')?.classList?.remove('d-none')
      document.getElementById('increaseUnit')?.classList?.add('d-none')
    }
  }

  packSizeSum () {
    let checkedItems = []
    checkedItems = this.itemTargets.filter((item) => item.checked)

    let totalPackSize = 0

    checkedItems.forEach((item) => {
      const packSize = parseInt(item.dataset.packSize)
      totalPackSize += packSize
    })
    return totalPackSize
  }

  toggleStockItemUnit (event) {
    let checkedCounter = 0
    this.itemTargets.forEach(item => {
      if (item.checked) {
        checkedCounter++
      }
    })
    if (checkedCounter > 0) {
      document.getElementById('printSelected').removeAttribute('disabled')
    }
    document.getElementById('checkedSum').innerHTML = checkedCounter
  }
}
