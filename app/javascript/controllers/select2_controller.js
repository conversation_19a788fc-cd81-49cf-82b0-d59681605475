import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['control']

  connect () {
    this.control = this.hasControlTarget ? this.controlTarget : this.element
    const { placeholder, url, selected, allowClear, disableSearch } = this.control.dataset
    this.options = { placeholder, url, selected, allowClear, disableSearch }
    this.select()
  }

  refresh () {
    this.control.value = null
    const event = new Event('change')
    this.control.dispatchEvent(event)
  }

  searchParameters (params) {
    const term = params.term
    const ransack = this.control.dataset.ransack || 'term'

    const str = this.control.dataset.q
    const q = (!!str && JSON.parse(str)) || {}

    q[ransack] = term
    const page = params.page || 1

    return { q, page }
  }

  dispatchEvent () {
    const event = new Event('change')
    this.control.dispatchEvent(event)
    this.control.dataset.selected = this.control.value
  }

  targetValue (name) {
    const target = this.targets.find(name)
    if (!target) return

    return target.value
  }

  hitItems (data, params) {
    return { results: data.options, pagination: { more: data.more } }
  }

  select2 () {
    const config = {
      placeholder: this.options.placeholder,
      width: '100%',
      allowClear: this.options.allowClear,
      escapeMarkup: markup => markup,
      templateSelection: it => it.text,
      templateResult: it => `<option value="${it.id}">${it.text}</option>`,
      dropdownParent: $(this.control.parentNode)
    }

    if (this.options.disableSearch) {
      config.minimumResultsForSearch = Infinity
    }

    const ajax = {
      url: this.options.url,
      dataType: 'JSON',
      cache: true,
      delay: 250,
      data: this.searchParameters.bind(this),
      processResults: this.hitItems.bind(this)
    }

    if (this.options.url) config.ajax = ajax
    $(this.control).select2(config).on('select2:select select2:unselect', this.dispatchEvent.bind(this))
  }

  select () {
    if (!this.options.selected || !this.options.url) {
      this.select2()
      return
    }

    const selected = this.options.selected.split(',')
    const [path, queryString] = this.options.url.split('?')

    const searchParams = new URLSearchParams(queryString)
    searchParams.append('selected', selected)

    const url = `${path}?${searchParams.toString()}`

    fetch(url)
      .then(response => response.json())
      .then(this.appendSelected.bind(this))
      .then(this.select2.bind(this))
  }

  appendSelected (response) {
    const options = response.options.map(it => `<option value="${it.id}" selected>${it.text}</option>`)
    $(this.control).append(options)
  }
}
