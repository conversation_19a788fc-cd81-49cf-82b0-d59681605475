import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = [
    'modal',
    'frame',
    'query'
  ]

  connect () {
    this.modal = this.initializeModal()
  }

  open (event) {
    event.preventDefault()
    this.updateFrame(event)
    this.modal.modal('show')
  }

  closeAfterSubmit (event) {
    if (event.detail.success) {
      this.modal.modal('hide')
    }
  }

  close (event) {
    this.modal.modal('hide')
  }

  initializeModal () {
    const options = { backdrop: true, keyboard: true, show: false }
    return new $(this.modalTarget).modal(options)
  }

  updateFrame (event) {
    const url = new URL(event.target.href || event.target.dataset.href)
    this.queryTargets.forEach((it) => {
      if (it.type?.toLowerCase() === 'checkbox') {
        if (it.checked) url.searchParams.append(it.dataset.name, it.dataset.value)
      } else {
        url.searchParams.append(it.dataset.name, it.value)
      }
    })
    this.frameTarget.src = url.toString()
  }
}
