import { Controller } from '@hotwired/stimulus'
import flatpickr from 'flatpickr'

export default class extends Controller {
  static targets = [
    'input',
    'clear',
    'open'
  ]

  static values = {
    mode: { type: String, default: 'range' }
  }

  connect () {
    this.picker = this.initializePicker()
    if (this.hasClearTarget) this.clearTarget.addEventListener('click', this.clear.bind(this))
    if (this.hasOpenTarget) this.openTarget.addEventListener('click', this.open.bind(this))
    this.toggleButtons()
  }

  disconnect () {
    this.picker.destroy()
  }

  initializePicker () {
    const input = this.hasInputTarget ? this.inputTarget : this.element
    const options = {
      mode: this.modeValue,
      altInput: true,
      altFormat: 'M j, Y',
      onClose: [this.toggleButtons.bind(this)]
    }

    return flatpickr(input, options)
  }

  toggleButtons () {
    if (!this.hasClearTarget || !this.hasOpenTarget) return

    const selectedDates = this.picker.selectedDates

    if (selectedDates.length === 2) {
      this.clearTarget.style.display = 'inline-block'
      this.openTarget.style.display = 'none'
    } else {
      this.clearTarget.style.display = 'none'
      this.openTarget.style.display = 'inline-block'
    }
  }

  clear () {
    this.picker.clear()
    this.toggleButtons()
  }

  open () {
    this.picker.open()
  }
}
