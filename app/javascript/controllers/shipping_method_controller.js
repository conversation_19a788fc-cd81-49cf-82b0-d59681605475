import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['countrySelect', 'stateSelect']

  connect () {
    const localPickUpCheckbox = this.element.querySelector('.shipping_method_local_pickup')
    const pickupFields = this.element.querySelectorAll('.shipping_method_pickup_field')
    const addressFieldContainer = this.element.querySelector('.address_detail_container')

    const updateInputFieldVisibility = () => {
      if (localPickUpCheckbox.checked) {
        addressFieldContainer.classList.remove('d-none')
        addressFieldContainer.classList.add('d-block')
        pickupFields.forEach(field => {
          field.required = true
          field.disabled = false
        })
      } else {
        addressFieldContainer.classList.remove('d-block')
        addressFieldContainer.classList.add('d-none')
        pickupFields.forEach(field => {
          field.required = false
          field.disabled = true
        })
      }
    }
    updateInputFieldVisibility()
    localPickUpCheckbox.addEventListener('change', updateInputFieldVisibility)
  }

  updateStates (event) {
    const countryId = event.target.value
    fetch(`/old_admin/shipping_methods/load_states?country_id=${countryId}`)
      .then(response => response.text())
      .then(html => {
        this.stateSelectTarget.innerHTML = html
      })
      .catch(error => console.error('Error fetching states:', error))
  }
}
