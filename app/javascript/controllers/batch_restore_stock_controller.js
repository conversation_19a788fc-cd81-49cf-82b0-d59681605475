import { Controller } from '@hotwired/stimulus'
import flatpickr from 'flatpickr'

export default class extends Controller {
  static targets = [
    'input',
    'list',
    'item'
  ]

  connect () {
    this.inputTarget.addEventListener('keypress', this.add)
  }

  add (event) {
    const number = this.inputTarget.value || event.detail?.number
    if (!number) return

    const { key } = event
    if (key && key !== 'Enter') return

    fetch(`/old_admin/stock_item_units/${number}.json`)
      .then(resp => resp.json())
      .then(data => this.appendStockItemUnit(data))
      .catch((e) => this.showErrorMessage(e))
  }

  remove (event) {
    const id = event.target.dataset.id
    const stockItemUnit = this.findStockItemUnit(id)
    stockItemUnit?.remove()
  }

  appendStockItemUnit (stockItemUnit) {
    if (this.findStockItemUnit(stockItemUnit.id)) return

    const template = document.createElement('template')
    template.innerHTML = this.buildItem(stockItemUnit)

    this.listTarget.appendChild(template.content.firstElementChild)
  }

  findStockItemUnit (id) {
    return this.itemTargets.find(it => it.dataset.id === id.toString())
  }

  showErrorMessage (e) {
    window.show_flash('error', 'Failed to load stock')
  }

  formatDate (date) {
    if (!date) return ''

    return flatpickr.formatDate(new Date(date), 'Y-m-d')
  }

  buildItem (stockItemUnit) {
    const expiryDate = stockItemUnit.expiry_date == null ? 'mm/dd/yyyy' : this.formatDate(stockItemUnit.expiry_date)
    const tbody = this.listTarget
    const otherIcon = tbody.dataset.otherIcon
    const naIcon = tbody.dataset.naIcon
    const estimatedDateIcon = tbody.dataset.estimatedDateIcon
    return `
      <tr data-batch-restore-stock-target="item" data-id="${stockItemUnit.id}">
        <td>
          <input type="hidden" name="stock_item_unit_id[]" value="${stockItemUnit.id}" />
          ${stockItemUnit.number}
        </td>
        <td>${stockItemUnit.vendor_receipt_number || ''}</td>
        <td>${stockItemUnit.vendor_inventory_number || ''}</td>
        <td>${this.formatDate(stockItemUnit.vendor_receipt_date)}</td>
        <td>
          <img src='${otherIcon}' class='${stockItemUnit.expiry_type === 'fixed' ? 'customwidth' : 'd-none customwidth'} custom_show' />
          <img src='${naIcon}' class='${stockItemUnit.expiry_type === 'na' ? 'customwidth' : 'd-none customwidth'} custom_show' />
          <img src='${estimatedDateIcon}' class='${stockItemUnit.expiry_type === 'estimated' ? 'customwidth' : 'd-none customwidth'} custom_show' />
          ${stockItemUnit.expiry_type === 'na' ? 'N/A' : expiryDate}
        </td>
        <td>${stockItemUnit.remark || ''}</td>
        <td><a href='#' data-action="batch-restore-stock#remove" data-id="${stockItemUnit.id}">Remove</a></td>
      </tr>
    `
  }
}
