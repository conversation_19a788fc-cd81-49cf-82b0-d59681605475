import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['productIds', 'selectedSubmit', 'selectedProductCount', 'selectAllCheckbox', 'productCheckbox', 'newProduct']

  constructor () {
    super(...arguments)
    this.productIds = []
  }

  connect () {
    if (this.hasTargetSelectedSubmit()) { this.selectedSubmitTarget.disabled = true }
    this.searchProducts()
  }

  submitSearch (e) {
    e.preventDefault()
    this.searchProducts()
  }

  searchProducts () {
    const filter = {
      search_by_name: $('#q_search_by_name').val(),
      variants_including_master_sku_cont: $('#q_variants_including_master_sku_cont').val(),
      'taxons_id_in[]': $('#taxons_id_in').val()
    }
    $.ajax({
      url: '/old_admin/products/search/',
      dataType: 'json',
      data: {
        q: filter
      }
    }).then(function (data) {
      $('#searched_products tbody').html(data.map(function (product) {
        return `<tr>
          <td><input type="checkbox"
          data-action="change->product-selector#checkBox"
          id="product_check_${product.listing_id}"
          data-id="${product.listing_id}"
          data-product-selector-target="productCheckbox" /></td>
          <td class="name" scope="row" colspan="2">
            <div class="flex text-left product-info">
              <div class="admin-product-image-container small-img"><img alt="${product.name}" src="${product.image_path}" ></div>
              <span class="text">
                ${product.name}
              </span>
            </div>
          </td>
          <td class="sku">
            <span class="label bold text-right d-lg-none">SKU</span>
            <span class="text">${product.sku}</span>
          </td>
          <td>
            <span class="label bold text-right d-lg-none">Quantities</span>
            <span class="text">${product.total_on_hand}</span>
          </td>
          <td class="bold fz-14">
            <span class="label bold text-right d-lg-none">Didgital</span>
            <span class="badge badge-${product.digitals ? 'active' : 'inactive'} badge-text">${product.digitals ? 'Yes' : 'No'}</span>
          </td>
          <td class="status">
            <span class="label bold text-right d-lg-none">Status</span>
            <span class="badge badge-${product.available ? 'active' : 'inactive'}">${product.available_status}</span>
          </td>
          <td class="status">
            <span class="label bold text-right d-lg-none">Active</span>
            <span class="badge badge-${product.active ? 'active' : 'inactive'}">${product.active ? 'Yes' : 'No'}</span>
          </td>
        </tr>`
      }))
    })
  }

  allCheckBox () {
    const allChecked = this.selectAllCheckboxTarget.checked
    const productCheckboxes = this.productCheckboxTargets

    this.productIds = []

    productCheckboxes.forEach(checkbox => {
      checkbox.checked = allChecked
      if (allChecked) this.productIds.push(checkbox.dataset.id)
    })
    if (!allChecked) this.productIds = []

    this.updateSelectedProducts()
  }

  checkBox (event) {
    this.productIds = Array.from(this.productCheckboxTargets)
      .filter(checkbox => checkbox.checked)
      .map(checkbox => checkbox.dataset.id)

    this.updateSelectedProducts()
    this.selectAllCheckboxTarget.checked = this.productIds.length === this.productCheckboxTargets.length
  }

  updateSelectedProducts () {
    this.selectedSubmitTarget.disabled = this.productIds.length === 0

    if (!this.selectedSubmitTarget.disabled) $('#selected_product_ids').prop('value', this.productIds.join(','))

    this.selectedProductIdsValue = this.productIds.join(',')

    if (this.productIds.length === 0) {
      this.selectedProductCountTarget.textContent = 'No Product'
    } else {
      const plural = this.productIds.length > 1 ? 's' : ''
      this.selectedProductCountTarget.textContent = `${this.productIds.length} Product${plural}`
    }
  }
}
