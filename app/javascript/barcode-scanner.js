import $ from 'jquery'
import { Html5QrcodeScanner, Html5QrcodeScanType } from 'html5-qrcode'

function showMessage ({ type, text, duration = 5 }) {
  $('#message')
    .show()
    .html(`<p>${type.slice(0, 1).toUpperCase()}${type.slice(1)}!</p><p>${text}</p>`)
    .attr('class', 'message ' + type)

  if (duration !== 0) {
    setTimeout(() => {
      $('#message').hide()
    }, duration * 1000)
  }
}

function showModal ({ title, message, cancelText = 'Cancel', confirmText = 'Ok', onCancel, onConfirm }) {
  const modal = {
    el: $('#modal'),
    title: $('#modal .modal-title'),
    message: $('#modal .modal-message'),
    cancelBtn: $('#modal .modal-actions--cancel'),
    confirmBtn: $('#modal .modal-actions--confirm')
  }
  const onCancelCallback = () => {
    if (typeof onCancel === 'function') {
      onCancel()
    }

    modal.el.hide()
    modal.confirmBtn.off('click', onConfirmCallback)
  }
  const onConfirmCallback = () => {
    if (typeof onConfirm === 'function') {
      onConfirm()
    }

    modal.el.hide()
    modal.cancelBtn.off('click', onCancelCallback)
  }

  if (title) {
    modal.title.text(title).show()
  } else {
    modal.title.hide()
  }

  if (message) {
    modal.message.html(message).show()
  } else {
    modal.message.hide()
  }

  modal.cancelBtn
    .text(cancelText)
    .one('click', onCancelCallback)

  modal.confirmBtn
    .text(confirmText)
    .one('click', onConfirmCallback)

  modal.el.show()
}

$(document).ready(function () {
  let lastScannedText = ''
  let lastScannedTime = 0
  let soundBuffer = null
  const soundURL = 'https://static.biaoguoworks.com/beep-08b.mp3'
  const qrboxFunction = function (viewfinderWidth, viewfinderHeight) {
    // Square QR Box, with size = 80% of the min edge.
    const minEdgeSizeThreshold = 250
    const edgeSizePercentage = 0.75
    const minEdgeSize = (viewfinderWidth > viewfinderHeight)
      ? viewfinderHeight
      : viewfinderWidth
    const qrboxEdgeSize = Math.floor(minEdgeSize * edgeSizePercentage)
    if (qrboxEdgeSize < minEdgeSizeThreshold) {
      if (minEdgeSize < minEdgeSizeThreshold) {
        return { width: minEdgeSize, height: minEdgeSize }
      } else {
        return {
          width: minEdgeSizeThreshold,
          height: minEdgeSizeThreshold
        }
      }
    }
    return { width: qrboxEdgeSize, height: qrboxEdgeSize }
  }
  const onScanSuccess = (decodedText) => {
    const now = new Date().getTime()
    const timeout = 5 * 1000
    const reg = /^(https?:\/\/)/i
    const resumeCamer = () => {
      setTimeout(() => {
        $('#reader').show()
        $('#loading').hide()
        html5Qrcode.resume()

        lastScannedTime = new Date().getTime()
      }, 10)
    }

    // If the same data is scanned within x seconds, ignore it
    if (lastScannedText === decodedText && now - lastScannedTime <= timeout) {
      lastScannedTime = now

      return false
    }

    lastScannedText = decodedText

    html5Qrcode.pause(true)
    $('#reader').hide()

    if (reg.test(decodedText)) {
      return showModal({
        title: 'URL detected',
        message: `<span>Would you like to load this link?</span><br><br><a href="${decodedText}" target="_self">${decodedText}</a>`,
        onCancel () {
          resumeCamer()
        },
        onConfirm () {
          location.href = decodedText
        }
      })
    }

    $('#loading').show()

    $.ajax({
      url: '/api/v2/storefront/barcode_readers/' + decodedText + '/successful_scan',
      type: 'post',
      // eslint-disable-next-line
      data: scanParameters,
      success () {
        playSound()
        resumeCamer()
        showMessage({ type: 'success', text: 'Item scan finished.' })
      },
      error (error) {
        const msgType = error.responseJSON?.type || 'error'
        const message = error.responseJSON?.error || 'Product not found'

        playSound()
        resumeCamer()
        showMessage({ type: msgType, text: message })
      }
    })
  }

  const html5Qrcode = new Html5QrcodeScanner('reader', {
    fps: 10,
    qrbox: qrboxFunction,
    rememberLastUsedCamera: true,
    showTorchButtonIfSupported: true,
    useBarCodeDetectorIfSupported: true,
    supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA]
  })
  const startScan = () => {
    html5Qrcode.render(onScanSuccess)

    setTimeout(() => {
      const infoEl = document.querySelector('#reader img[alt="Info icon"]')
      const permissionButtonEl = document.querySelector(`#${html5Qrcode.getCameraPermissionButtonId()}`)

      if (infoEl) {
        infoEl.style.display = 'none'
      }

      if (permissionButtonEl) {
        permissionButtonEl.click()
      }

      // auto scan when ready
      const timer = setInterval(() => {
        try {
          const scanner = html5Qrcode.getHtml5QrcodeOrFail()

          if (scanner.isScanning) {
            return clearInterval(timer)
          }

          const selectCameraEl = document.querySelector('#html5-qrcode-select-camera')
          const startEl = document.querySelector('#html5-qrcode-button-camera-start')

          if (startEl && selectCameraEl && selectCameraEl.options?.length) {
            const options = Array.from(selectCameraEl.options)
            let index = options.length - 1

            for (const i in options) {
              const el = options[i]
              if ((el.text || '').includes('back')) {
                index = +i
                break
              }
            }

            selectCameraEl.selectedIndex = Math.max(0, index)

            startEl.click()

            clearInterval(timer)
          }
        } catch (error) {
          console.warn(error.message)
        }
      }, 1000)
    }, 100)
  }
  const playSound = () => {
    const AudioContext = window.AudioContext || window.webkitAudioContext || window.mozAudioContext || window.msAudioContext

    if (AudioContext) {
      const startPlay = () => {
        const audioContext = new window.AudioContext()

        audioContext.decodeAudioData(soundBuffer, buffer => {
          const source = audioContext.createBufferSource()

          source.buffer = buffer
          source.loop = false
          source.connect(audioContext.destination)
          source.start(0)
        }, function (e) {
          soundBuffer = null
          console.log('Error decoding file', e)
        })
      }

      if (soundBuffer) {
        startPlay()
      } else {
        loadSound().then(startPlay)
      }
    } else {
      const audio = new Audio(soundURL)

      audio.play()
        .then(() => {
          audio.play()
        })
        .catch((err) => {
          console.error(err)
        })
    }
  }
  const loadSound = () => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      xhr.open('GET', soundURL, true)
      xhr.responseType = 'arraybuffer'
      xhr.onload = function () {
        soundBuffer = this.response
        resolve(soundBuffer)
      }
      xhr.send()
    })
  }
  const mediaQueryList = window.matchMedia('(orientation: landscape)')

  window.initVconsole = () => {
    const script = document.createElement('script')

    script.src = 'https://cdn.bootcss.com/vConsole/3.2.2/vconsole.min.js'
    script.onload = () => {
      window.vconsole = new window.VConsole()
    }

    document.body.appendChild(script)
  }

  mediaQueryList.addEventListener('change', () => {
    html5Qrcode.clear()
    setTimeout(startScan, 100)
  })

  startScan()
  loadSound()
})
