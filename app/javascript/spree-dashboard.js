// Entry point for the build script in your package.json

import '@spree/dashboard'
import './controllers'
import './channels/qr_code_channel'
import tableDragger from 'table-dragger'

/**
 * sortable table function
 * add class'table-dragger' to the table to use it
 */
document.addEventListener('spree:load', function () {
  const element = document.querySelector('table.table-dragger')

  if (element) {
    tableDragger(element, {
      mode: 'row',
      onlyBody: true
    })
      .on('drop', () => {
        const payload = { authenticity_token: window.AUTH_TOKEN, positions: {} }

        $.each($('tr', element.querySelector('tbody')), function (position, obj) {
          const reg = /spree_(\w+_?)+_(\d+)/
          const parts = reg.exec($(obj).prop('id'))
          const partId = parts ? parts[2] : $(obj).data('id')

          if (partId) {
            payload.positions[partId] = position + 1
          }
        })

        $.ajax({
          type: 'POST',
          dataType: 'json',
          url: $(element).data('sortable-link'),
          data: payload
        })
      })
  }
})

window.Turbo = SpreeDashboard.Turbo
