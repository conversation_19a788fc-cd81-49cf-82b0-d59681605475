# frozen_string_literal: true

class ProductSales < ApplicationJob
  queue_as :product_sales

  # Daily products sales volume records Retention Period: 40 Days. Calculate the products sales volume for the last 30 days.
  THIRTY_DAYS = 30.days
  RETENTION_DAYS = 40.days

  def remove_expired_record
    Spree::DailySalesRecord.delete_records_before(Time.zone.today - RETENTION_DAYS)
  end

  def fill_daily_sales_records(date)
    records = Hash.new(0)
    Spree::Order.where(completed_at: date.all_day).find_each do |order_item|
      order_item.line_items.find_each do |line_item|
        product = line_item.product
        product_id = product.id
        records[product_id] += line_item.quantity
      end
    end

    final_records = records.transform_values do |quantity|
      { quantity: quantity, date: date }
    end
    final_records[0] = { quantity: 0, date: date } if final_records.empty?

    final_records
  end

  def fill_all_daily_sales_records
    next_day = Time.zone.today - RETENTION_DAYS
    latest_date = Spree::DailySalesRecord.maximum(:date)
    if latest_date.nil?
      latest_date = Spree::LineItem.minimum(:created_at)
      return if latest_date.nil?

      next_day = [latest_date.to_date, next_day].max
    else
      next_day = latest_date.to_date + 1.day
    end

    dates = next_day..Time.zone.today.prev_day
    dates.each do |date|
      records = fill_daily_sales_records(date)
      Spree::DailySalesRecord.batch_create_sale(records)
    end
  end

  def fill_recent_thirty_days_sales_volume
    tm = Time.zone.now
    thirty_days_ago = Time.zone.today - THIRTY_DAYS

    records = {}
    Spree::DailySalesRecord
      .where(date: thirty_days_ago..Time.zone.today.prev_day)
      .where("quantity > ?", 0)
      .pluck("DISTINCT product_id")
      .each do |product_id|
      Spree::DailySalesRecord.select("sum(spree_daily_sales_records.quantity) as quantity")
        .where(date: thirty_days_ago..Time.zone.today.prev_day)
        .where(product_id: product_id).each do |record|
        records[product_id] = { quantity: record.quantity }
      end
    end
    Spree::RecentThirtyDaysSalesRecord.batch_create_sale(records)

    # Remove the records which is not updated
    Spree::RecentThirtyDaysSalesRecord.where("updated_at < ?", tm).delete_all
  end

  def reset_data
    Spree::DailySalesRecord.delete_all
    Spree::RecentThirtyDaysSalesRecord.delete_all
  end

  def run
    fill_all_daily_sales_records
    fill_recent_thirty_days_sales_volume
    remove_expired_record
  end

  def perform
    Apartment.tenant_names.each do |tenant|
      Apartment::Tenant.switch(tenant) do
        run
      end
    end
  end
end
