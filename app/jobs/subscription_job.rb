# frozen_string_literal: true

class SubscriptionJob < ApplicationJob
  queue_as :default

  def perform
    Apartment.tenant_names.each do |tenant|
      Apartment::Tenant.switch(tenant) do
        Spree::Subscription.reorder_after_three_days.find_each do |subscription|
          Subscription::Reorder.new(subscription.id).execute
          last_recurring_order = subscription.orders.last
          next_delivery_date = fetch_recurring_date(time_interval, last_recurring_order)
          subscription.update(next_delivery_date: next_delivery_date)
        end
      end
    end
  end

  private

  def fetch_recurring_date(interval, order)
    order.fetch_next_reorder_date(interval, order).to_date
  end
end
