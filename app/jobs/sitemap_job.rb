# frozen_string_literal: true

class SitemapJob < ApplicationJob
  queue_as :default

  def perform
    execute and return if Rails.env.development?

    Apartment.tenant_names.each do |tenant|
      Rails.logger.info("SitemapJob: Switch tenant #{tenant}")
      Apartment::Tenant.switch(tenant) do
        execute
      end
    end
  end

  def execute
    ::Spree::Store.find_each do |store|
      make_sitemap(store)
    end
  end

  def make_sitemap(store)
    Rails.logger.info("SitemapJob: Generate for store #{store.name}")

    options = {
      public_path: "storage",
      sitemaps_path: "sitemaps/#{store.code}",
      default_host: "https://#{store.url}",
      compress: false,
    }

    job = self

    SitemapGenerator::Sitemap.create(options) do |sitemap|
      sitemap.add("/")

      job.list_menu_items(store) { |link, date| add(link, lastmod: date) }
      job.list_products(store) { |link, updated_at| add(link, lastmod: updated_at) }
    end
  rescue StandardError => e
    Rails.logger.error("SitemapJob: Error generating sitemap for store #{store.name}: #{e.message}")
  end

  def list_products(store)
    ::Spree::Listing
      .joins(:sale_channel, :product)
      .where(status: 'Active', store: store)
      .where(sale_channel: { brand: "storefront" })
      .find_each do |listing|
        product = listing.product
        next unless product&.slug.present?
        
        yield "/p/#{product.id}/#{listing.id}/#{product.slug}", listing.updated_at
      end
  rescue StandardError => e
    Rails.logger.error("SitemapJob: Error listing products for store #{store.name}: #{e.message}")
  end

  def list_menu_items(store)
    ::Spree::Menu.for_store(store).where(locale: "en").find_each do |menu|
      menu.menu_items.where(item_type: "Link").find_each do |item|
        link = nil, date = nil
        case item.linked_resource_type
        when "Spree::Taxon"
          link = "/c/#{item.linked_resource.permalink}" if item.linked_resource&.permalink.present?
        when "Spree::Product"
          if item.linked_resource&.slug.present?
            link = "/p/#{item.linked_resource.id}/#{item.linked_resource.slug}"
            date = item.linked_resource.updated_at
          end
        when "Spree::CmsPage"
          if item.linked_resource&.slug.present?
            link = "/pages/#{item.linked_resource.slug}"
            date = item.linked_resource.updated_at
          end
        when "Spree::Linkable::Uri"
          link = item.destination
        end

        yield link, date if link.present?
      end
    end
  rescue StandardError => e
    Rails.logger.error("SitemapJob: Error listing menu items for store #{store.name}: #{e.message}")
  end
end
