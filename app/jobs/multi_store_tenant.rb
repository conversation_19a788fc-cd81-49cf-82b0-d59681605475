# frozen_string_literal: true

class MultiStoreTenant < ApplicationJob
  queue_as :default

  def perform
    Spree::Organisation.find_each do |org|
      Apartment::Tenant.switch(org.subdomain) do
        stores = Spree::Store.all

        if stores.count > 1
          subdomains = stores.map { |s| s.url.sub(%r{^https?\://(www.)?}, "").split(".").first }
          subdomains.each do |subdomain|
            RedisStore.set(subdomain, org.subdomain)
          end
        end
      end
    end
  end
end
