# frozen_string_literal: true

# Sync listings from DB to Elasticsearch
class EsListingJob < ApplicationJob
  queue_as :elasticsearch

  def sync_all
    es = AxelSpree::Elasticsearch::Listing.new
    Apartment.tenant_names.each do |tenant|
      Apartment::Tenant.switch(tenant) do
        Rails.logger.info("aync all tenant #{tenant}")
        es.sync_all
      end
    end
  end

  def perform
    es = AxelSpree::Elasticsearch::Listing.new
    es.check_and_create_index
    unless es.has_records_in_elasticsearch
      logger.info("Sync listing form DB to Elasticsearch...")
      sync_all
    end
  end
end
