# frozen_string_literal: true

class VicUpdateJob < ApplicationJob
  queue_as :default

  def perform
    logger = Logger.new("log/vic_update_job.log")

    Apartment.tenant_names.each do |tenant|
      Apartment::Tenant.switch(tenant) do
        Spree::StockItemUnit.where("
        (vendor_inventory_cost IS NULL OR vendor_inventory_cost = '')
        AND
        (vendor_inventory_number IS NOT NULL AND vendor_inventory_number != '')").find_each do |stock_item_unit|
          vendor_inventory_number = stock_item_unit.vendor_inventory_number
          begin
            command = ::Admin::GetPricecompareInfoCommand.call(vin: vendor_inventory_number)
            if command.result[:success]
              response = command.result[:data]
              if response.present?
                stock_item_unit.update!(vendor_inventory_cost: response["total_amount_per_item"])
                logger.info("#{tenant}: Sucess for VIN #{vendor_inventory_number}")
              end
            else
              logger.error("#{tenant}: Error for VIN #{vendor_inventory_number}: #{command.result[:message]}")
            end
          rescue StandardError => e
            logger.error("#{tenant}: Error for VIN #{vendor_inventory_number}: #{e.message}")
          end
        end
      end
    end
  end
end
