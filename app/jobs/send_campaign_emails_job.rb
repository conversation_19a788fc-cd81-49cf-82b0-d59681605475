# frozen_string_literal: true

class SendCampaignEmailsJob < ApplicationJob
  queue_as :default

  def perform(campaign_id)
    campaign = Spree::Campaign.find_by(id: campaign_id)
    return unless campaign

    store = campaign.store
    message_base = "#{SecureRandom.uuid}-#{store.id}-#{campaign.id}"

    sent_successful_ids = []
    failed_updates = {}

    campaign.subscribers.find_each(batch_size: 100) do |subscriber|
      campaign_subscriber = Spree::CampaignSubscriber.find_or_initialize_by(campaign: campaign, subscriber: subscriber)
      message_id = "<#{message_base}-#{subscriber.id}@#{store.url}.mail>"

      begin
        campaign_subscriber.update!(message_id: message_id)
        Spree::CampaignMailer.send_campaign_email(campaign, subscriber, campaign_subscriber).deliver_now
        sent_successful_ids << campaign_subscriber.id
      rescue => e
        error_message = "#{e.class.name}: #{e.message}"
        Rails.logger.error "Email sending failed for subscriber ID #{subscriber.id}, message: #{error_message}"
        failed_updates[campaign_subscriber.id] = error_message
      end
    end

    if sent_successful_ids.any?
      Spree::CampaignSubscriber.where(id: sent_successful_ids).update_all(sent: true, failed: false, error_message: nil)
    end

    if failed_updates.any?
      failed_ids = failed_updates.keys
      Spree::CampaignSubscriber.where(id: failed_ids).update_all(failed: true)
      failed_updates.each do |id, error_message|
        Spree::CampaignSubscriber.where(id: id).update_all(error_message: error_message)
      end
    end

    campaign.update_columns(
      sent_count: campaign.campaign_subscribers.where(sent: true).count,
      failed_count: campaign.campaign_subscribers.where(failed: true).count
    )

    finalize_campaign_status(campaign)
  end

  private

  def finalize_campaign_status(campaign)
    if campaign.subscribers.count == campaign.campaign_subscribers.where.not(sent: false, failed: false).count
      campaign.update_column(:status, 'sent')
    end
  end
end
