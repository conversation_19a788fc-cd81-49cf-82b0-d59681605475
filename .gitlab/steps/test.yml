.test_base:
  image: $CI_IMAGE_PULL@${CI_IMAGE_HASH}
  stage: test
  needs:
    - job: "initialize"
      artifacts: true
  variables:
    RAILS_ENV: "test"
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres
    POSTGRES_HOST_AUTH_METHOD: trust
    DB_HOST: postgres
    REDIS_URL: redis://redis:6379/1
  services:
    - postgres:14
    - redis:6.2
  before_script:
    - bin/rake db:create db:migrate
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*$/
      when: never
    - when: on_success      

test:rspec:
  extends:
    - .test_base
  script:
    - bundle exec rspec

test:cucumber:
  extends:
    - .test_base
  script:
    - echo 'pending'
