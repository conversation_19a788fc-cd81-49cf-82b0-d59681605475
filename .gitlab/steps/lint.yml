.lint_base:
  image: $CI_IMAGE_PULL@${CI_IMAGE_HASH}
  stage: lint
  needs:
    - job: "initialize"
      artifacts: true
  allow_failure: true
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*$/ || $CI_COMMIT_BRANCH == 'master'
      when: never
    - when: on_success

lint:rubocop:
  extends:
    - .lint_base
  script:
    - bundle exec rubocop

lint:eslint:
  extends:
    - .lint_base
  script:
    - yarn eslint
  allow_failure: true

lint:erb:
  extends:
    - .lint_base
  script:
    - bundle exec erblint --lint-all --fail-level W

lint:fasterer:
  extends:
    - .lint_base
  script:
    - bundle exec fasterer
  allow_failure: true

lint:brakeman:
  extends:
    - .lint_base
  script:
    - bundle exec brakeman -z -i .brakeman.ignore
  allow_failure: true

lint:bundle-audit:
  extends:
    - .lint_base
  script:
    - bundle-audit check --update
  allow_failure: true
