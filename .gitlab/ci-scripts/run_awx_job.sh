tower-cli config host $AWX_SERVER
tower-cli config username $AWX_USER
tower-cli config password $AWX_PASSWORD
tower-cli config verify_ssl true

DEPLOY_ENV=$(echo $DEPLOY_ENV |  tr '[:upper:]' '[:lower:]' | tr '.' '-' | tr '_' '-')

kubectl config use-context "AXEL_MARKETPLACE/gitlab-kas:am-gl-agent"

if (kubectl get namespace $DEPLOY_ENV); then
  echo "Namespace $DEPLOY_ENV exists."
  echo "AWX template ID=${JOB_TEMPLATE_ID} has been launched."
  job_id=$(tower-cli job launch --job-template $JOB_TEMPLATE_ID --extra-vars '{"dev_env": "'${DEPLOY_ENV}'", "spree_backend_tag": "'${CI_COMMIT_REF_NAME}'"}' -f id)
else
  echo "Namespace ${DEPLOY_ENV} does not exist. AWX template ID=${AWX_TEMPLATE_DEPLOY_WHOLE_ENV} has been launched to create whole env."
  echo "spree_backend_tag=${CI_COMMIT_REF_NAME}"
  echo "spree_storefront_tag=staging" 
  job_id=$(tower-cli job launch --job-template $AWX_TEMPLATE_DEPLOY_WHOLE_ENV --extra-vars '{"dev_env": "'${DEPLOY_ENV}'", "spree_backend_tag": "'${CI_COMMIT_REF_NAME}'", "spree_storefront_tag": "staging"}' -f id)
fi

while $(tower-cli job status $job_id | grep -q -E "pending|running")
 do
    echo "Waiting the AWX job id=${job_id} finish ..."
    sleep 10
 done

if $(tower-cli job status $job_id | grep -q successful); then
  echo "The job id=${job_id} finished successfully"
  exit 0
else
  echo "The job id=${job_id} failed"
  tower-cli job status $job_id
  exit 1
fi



