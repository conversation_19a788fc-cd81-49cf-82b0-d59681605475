# syntax = docker/dockerfile:1

# Make sure RUBY_VERSION matches the Ruby version in .ruby-version and Gemfile
ARG RUBY_VERSION=3.3.5
FROM ruby:$RUBY_VERSION-slim as base

# Rails app lives here
WORKDIR /rails

# Update gems and bundler
RUN gem update --system --no-document && \
    gem install -N bundler

# Install packages needed to install nodejs
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y curl && \
    rm -rf /var/lib/apt/lists /var/cache/apt/archives

# Install Node.js
ARG NODE_VERSION=18.15.0
ENV PATH=/usr/local/node/bin:$PATH
RUN curl -sL https://github.com/nodenv/node-build/archive/master.tar.gz | tar xz -C /tmp/ && \
    /tmp/node-build-master/bin/node-build "${NODE_VERSION}" /usr/local/node && \
    rm -rf /tmp/node-build-master


# Throw-away build stages to reduce size of final image
FROM base as prebuild

# Install packages needed to build gems and node modules
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y build-essential git libpq-dev libvips node-gyp pkg-config python-is-python3


FROM prebuild

RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y curl imagemagick libjemalloc2 libvips postgresql-client && \
    rm -rf /var/lib/apt/lists /var/cache/apt/archives

# Install yarn
ARG YARN_VERSION=1.22.19
RUN npm install -g yarn@$YARN_VERSION

# Install node modules
COPY --link package.json yarn.lock ./
RUN yarn install --frozen-lockfile --cache-folder .yarn --network-timeout 100000

# Build options
ENV PATH="/usr/local/node/bin:$PATH"

# Install application gems
COPY --link Gemfile Gemfile.lock ./

COPY --link \
     extensions/better_spree_paypal_express/Gemfile \
     extensions/better_spree_paypal_express/spree_paypal_express.gemspec \
     extensions/better_spree_paypal_express/
COPY --link \
     extensions/better_spree_paypal_express/lib/ \
     extensions/better_spree_paypal_express/lib/

COPY --link \
     extensions/spree_volume_pricing/Gemfile \
     extensions/spree_volume_pricing/spree_volume_pricing.gemspec \
     extensions/spree_volume_pricing/
COPY --link \
     extensions/spree_volume_pricing/lib/ \
     extensions/spree_volume_pricing/lib/

COPY --link \
     extensions/spree_sale_channel/Gemfile \
     extensions/spree_sale_channel/spree_sale_channel.gemspec \
     extensions/spree_sale_channel/
COPY --link \
     extensions/spree_sale_channel/lib/ \
     extensions/spree_sale_channel/lib/

COPY --link \
     extensions/spree_stripe_tax/Gemfile \
     extensions/spree_stripe_tax/spree_stripe_tax.gemspec \
     extensions/spree_stripe_tax/
COPY --link \
     extensions/spree_stripe_tax/lib/ \
     extensions/spree_stripe_tax/lib/

RUN bundle install
